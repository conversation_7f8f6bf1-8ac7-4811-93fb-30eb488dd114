apiVersion: apps/v1
kind: Deployment
metadata:
  name: gripxstoreweb
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gripxstoreweb
  template:
    metadata:
      labels:
        app: gripxstoreweb
    spec:
      containers:
        - name: gripxstoreweb
          image: gripx.azurecr.io/gripxstoreweb:latest
          ports:
            - containerPort: 80
          imagePullPolicy: Always
      imagePullSecrets:
        - name: acr-secret
---
apiVersion: v1
kind: Service
metadata:
  name: gripxstoreweb-service
  namespace: dev
spec:
  type: LoadBalancer
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: gripxstoreweb
