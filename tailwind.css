@tailwind base;
@tailwind components;
@tailwind utilities;
@layer components {
    .markdown-body {
        @apply break-words dark:text-gray-400 leading-relaxed;

        p,
        blockquote,
        ul,
        ol,
        dl,
        table,
        pre,
        code {
            @apply mb-4;
        }

        hr {
            @apply h-1 my-2 bg-gray-300 dark:bg-gray-600;
        }

        blockquote {
            @apply py-2 text-gray-500 border-l-2 border-gray-500 pl-4;

            p {
                @apply mb-0;
            }
        }

        kbd {
            @apply inline-block px-2 py-1 text-sm text-gray-500 bg-gray-300 dark:bg-gray-600 border rounded-sm shadow-sm;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            @apply mt-3 mb-2 font-semibold;
        }

        h1 {
            @apply text-2xl border-b border-gray-200 dark:border-gray-600 pb-4;
        }

        h2 {
            @apply text-xl border-b border-gray-200 dark:border-gray-600 pb-3;
        }

        h3 {
            @apply text-lg;
        }

        h4,
        h5,
        h6 {
            @apply text-base;
        }

        ul,
        ol {
            @apply pl-2 ml-4;
        }

        ul {
            @apply list-disc;
        }

        ol {
            @apply list-decimal;
        }

        table {
            @apply w-full overflow-auto block;

            th {
                @apply font-semibold;
            }

            th,
            td {
                @apply py-2 px-4 border border-gray-400 dark:border-gray-600;
            }

            tr:nth-child(2n) {
                @apply bg-gray-200 dark:border-gray-500;
            }
        }

        img {
            @apply max-w-full block my-3;
        }

        code,
        tt {
            @apply py-2 px-4 bg-gray-200 dark:border-gray-600 rounded;
        }

        p:first-child {
            img:first-child {
                @apply mt-0;
            }
        }

        p:last-child {
            @apply mb-0;

            img:last-child {
                @apply mb-0;
            }
        }
    }
}



.module-title{
    @apply relative flex items-center text-[16px] before:content-[''] before:block before:h-[12px] before:w-[4px] before:bg-[#F49C1F] before:mr-[6px];
}

.button-outline:not([disabled]){
    @apply border border-[#F49C1F] text-[#F49C1F] hover:bg-[#F49C1F] hover:text-white;
}

.border-left-gray{
    border-left: 1px solid #e5e7eb;
}

.border-right-gray{
    border-right: 1px solid #e5e7eb;
}