export default {
  'shop.common.button.addProduct': '添加商品',
  'shop.common.button.addGift': '添加赠品',
  'shop.common.message.needAddProduct': '请至少添加一个商品',
  'shop.common.message.needAddGift': '请至少添加一个赠品',

  // 专题页管理
  'shop.topic.topicStatus.draft': '草稿',
  'shop.topic.topicStatus.published': '已发布',
  'shop.topic.topicPageSource.topic': '专题页',
  'shop.topic.topicPageSource.app': 'app首页',

  'shop.topic.list.topicPageId': '专题页ID',
  'shop.topic.list.topicPageName': '专题页名称',
  'shop.topic.list.pageStatus': '页面状态',
  'shop.topic.list.pageSource': '页面类型',
  'shop.topic.list.updater': '更新人',
  'shop.topic.list.updateTime': '更新时间',
  'shop.topic.list.pageRemark': '页面备注',
  'shop.topic.list.copy': '复制',
  'shop.topic.list.publish': '发布',
  'shop.topic.list.unpublish': '取消发布',
  'shop.topic.list.operation.decoration': '装修',

  // 活动管理
  'shop.activity.list.activityId': '活动Id',
  'shop.activity.list.activityName': '活动名称',
  'shop.activity.list.activityType': '活动类型',
  'shop.activity.list.activityStatus': '活动状态',
  'shop.activity.list.activityStep': '进行状态',
  'shop.activity.list.activityStartTime': '活动开始时间',
  'shop.activity.list.activityEndTime': '活动结束时间',
  'shop.activity.list.updater': '更新人',
  'shop.activity.list.updateTime': '更新时间',
  'shop.activity.list.activityRemark': '活动备注',
  'shop.activity.activityStatus.draft': '草稿',
  'shop.activity.activityStatus.takeEffect': '已生效',
  'shop.activity.activityStatus.invalid': '失效',
  'shop.activity.activityType.everyFullGift': '每满赠',
  'shop.activity.activityType.ladderFullGift': '阶梯满赠',
  'shop.activity.activityType.buyGiftSelf': '买赠活动',
  'shop.activity.activityType.specialPrice': '限时特价',
  'shop.activity.activityType.suiteItem': '组合套餐',
  'shop.activity.activityProgress.notStartActivity': '未开始',
  'shop.activity.activityProgress.inActivity': '进行中',
  'shop.activity.activityProgress.endActivity': '已结束',
  'shop.activity.list.operation.enable': '生效',
  'shop.activity.list.operation.disable': '失效',

  'shop.activity.title.basicInfo': '基础信息',
  'shop.activity.title.applicableProducts': '活动商品',
  'shop.activity.title.giftInfo': '赠品设置',
  'shop.activity.name': '活动名称',
  'shop.activity.description': '活动说明',
  'shop.activity.descriptionPlaceholder': '支持200个字符输入，将展示在活动页面',
  'shop.activity.period': '活动周期',
  'shop.activity.startTime': '开始日期',
  'shop.activity.endTime': '结束日期',
  'shop.activity.audience': '活动人群',
  'shop.activity.audienceUnlimited': '不限',
  'shop.activity.audienceSpecificTags': '指定标签客户',
  'shop.activity.image': '活动图',
  'shop.activity.uploadImage': '上传图片',
  'shop.activity.supportCouponOverlay': '支持优惠券叠加',
  'shop.activity.supportAccountPayment': '支持挂账支付',
  'shop.activity.allowOrderLowStock': '库存不足可下单',
  'shop.activity.remarks': '活动备注',
  'shop.activity.remarksPlaceholder': '支持200个字符输入，仅用于内部展示',
  'shop.activity.tagRequired': '请选择指定客户标签',

  'shop.activity.products.activityPrice': '活动价',
  'shop.activity.products.minOrderQuantityPerCustomer': '单客户起订数',
  'shop.activity.products.maxPurchaseQuantityPerCustomer': '单客户限购数',
  'shop.activity.products.totalPurchaseQuantity': '总限购数',
  'shop.activity.products.sortOrder': '排序',
  'shop.activity.products.purchasedQuantity': '购本品数',
  'shop.activity.products.giftQuantity': '赠本品数',
  'shop.activity.products.totalMaxPurchaseQuantity': '商品总限购数',
  'shop.activity.products.totalMaxGiftQuantity': '赠品总限购数',
  'shop.activity.products.type.productCombination': '商品组合',
  'shop.activity.products.type.discountPackage': '优惠套餐',
  'shop.activity.products.promotionPrice': '活动价',
  'shop.activity.products.quantity': '数量',
  'shop.activity.products.limit': '购买限制',
  'shop.activity.products.packageActivityPrice': '套餐内单价',
  'shop.activity.products.minOrderPackageNum': '单客户起订套数',
  'shop.activity.products.singleCustomerLimitPackageNum': '单客户限购套数',
  'shop.activity.products.totalLimitPackageNum': '商品总限购套数',
  'shop.activity.products.totalPurchasePrice': '单买合计',
  'shop.activity.products.packageTotalPrice': '套餐总价',
  'shop.activity.products.discountPrice': '优惠价格',

  'shop.activity.products.required': '此项为必填项',
  'shop.activity.products.activityPriceRequired': '活动价为必填项',
  'shop.activity.products.numberMinZero': '必须为大于或等于0的数字',
  'shop.activity.products.numberPositive': '必须为大于0的数字',

  'shop.activity.products.taskDesc.specialPrice': '限时特价活动商品导入',
  'shop.activity.products.taskDesc.everyFullGift': '阶梯满赠/每满赠活动商品导入',
  'shop.activity.products.taskDesc.ladderFullGift': '阶梯满赠活动商品导入',
  'shop.activity.products.taskDesc.buyGiftSelf': '买赠活动商品导入',
  'shop.activity.products.taskDesc.suiteItem': '组合套餐活动商品导入',

  'shop.activity.giftInfo.ladderMin': '至少需要一个阶梯',

  'shop.activity.giftInfo.title': '赠品设置',
  'shop.activity.giftInfo.giftRule': '赠送规则',
  'shop.activity.giftInfo.perFull': '每满赠',
  'shop.activity.giftInfo.tieredGifting': '阶梯赠',
  'shop.activity.giftInfo.giftCondition': '赠送条件',
  'shop.activity.giftInfo.fullItems': '满件',
  'shop.activity.giftInfo.fullAmount': '满元',
  'shop.activity.giftInfo.conditionUnitItems': '件',
  'shop.activity.giftInfo.conditionUnitAmount': '元',
  'shop.activity.giftInfo.addGift': '添加赠品',
  'shop.activity.giftInfo.giftQuantity': '赠送数量',
  'shop.activity.giftInfo.totalMaxBuyQuantity': '总限购数',
  'shop.activity.giftInfo.ladder': '阶梯',
  'shop.activity.giftInfo.addLadder': '添加阶梯',
  'shop.activity.giftInfo.enableAmountRequired': '请输入条件金额/数量',
  'shop.activity.giftInfo.giftQuantityRequired': '请输入赠送数量',
  'shop.activity.giftInfo.maxBuyQuantityRequired': '请输入总限购数量',
  'shop.activity.giftInfo.giftQuantityMin': '赠送数量不能小于1',
  'shop.activity.giftInfo.maxBuyQuantityMin': '总限购数量不能小于1',
  'shop.activity.giftInfo.giftItemsRequired': '每个阶梯至少添加一件赠品',

  // 优惠券管理
  'shop.coupon.list.button.batchSend': '批量发券',
  'shop.coupon.list.batchSend.methodChoose': '选择客户',
  'shop.coupon.list.batchSend.methodInput': '手动输入',
  'shop.coupon.list.batchSend.methodInput.placeholder':
    '请输入客户id，并用英文逗号隔开，最多输入20个客户id',
  'shop.coupon.list.couponId': '优惠券ID',
  'shop.coupon.list.couponName': '优惠券名称',
  'shop.coupon.list.startingAmount': '起用金额',
  'shop.coupon.list.couponAmount': '优惠券金额',
  'shop.coupon.list.totalQuantity': '总数量',
  'shop.coupon.list.remainingQuantity': '剩余数量',
  'shop.coupon.list.validityPeriod': '有效期',
  'shop.coupon.list.status': '状态',
  'shop.coupon.list.updater': '更新人',
  'shop.coupon.list.updateTime': '更新时间',
  'shop.coupon.list.effectiveTime': '生效时间',
  'shop.coupon.list.expiryTime': '失效时间',
  'shop.coupon.list.operation.valid': '生效',
  'shop.coupon.list.operation.invalid': '失效',
  'shop.coupon.couponStatus.wait': '待生效',
  'shop.coupon.couponStatus.active': '生效中',
  'shop.coupon.couponStatus.invalid': '失效',

  // 优惠券使用记录
  'shop.couponRecord.list.used': '已使用',
  'shop.couponRecord.list.valid': '已领取',
  'shop.couponRecord.list.expired': '已过期',
  'shop.couponRecord.list.invalid': '已作废',
  'shop.couponRecord.list.notUsed': '未使用',
  'shop.couponRecord.list.operation.invalid': '作废',
  'shop.couponRecord.list.operation.batchInvalid': '批量作废',
  'shop.couponRecord.message.select.notUsed': '请选择未使用的优惠券',
  'shop.couponRecord.confirm.batchInvalid': '确定要批量作废选中的 {count} 张优惠券吗？',

  'shop.couponRecord.list.recordId': '记录ID',
  'shop.couponRecord.list.couponId': '优惠券ID',
  'shop.couponRecord.list.couponName': '优惠券名称',
  'shop.couponRecord.list.couponValidity': '优惠券有效期',
  'shop.couponRecord.list.customerCode': '客户编码',
  'shop.couponRecord.list.customerName': '客户名称',
  'shop.couponRecord.list.status': '状态',
  'shop.couponRecord.list.issuerReceiver': '发放/领取人',
  'shop.couponRecord.list.receiveTime': '领取时间',
  'shop.couponRecord.list.useTime': '使用时间',
  'shop.couponRecord.list.orderNumber': '订单号',

  'shop.coupon.basicInfo': '基础信息',
  'shop.coupon.applicableProducts': '适用商品',
  'shop.coupon.couponName': '优惠券名称',
  'shop.coupon.totalStock': '发放总数',
  'shop.coupon.singleAccountNum': '单客户领取上限',
  'shop.coupon.singleAccountNum.addonBefore': '单个账号',
  'shop.coupon.singleAccountNum.addonAfter': '个',
  'shop.coupon.discountAmount': '优惠金额',
  'shop.coupon.enableAmount': '起用金额',
  'shop.coupon.validityType': '有效期类型',
  'shop.coupon.validityTypeAbsolute': '起止时间',
  'shop.coupon.validityTypeRelative': '有效期',
  'shop.coupon.validityBeginTime': '生效时间',
  'shop.coupon.validityEndTime': '失效时间',
  'shop.coupon.validityDays': '有效期',
  'shop.coupon.validityDaysPlaceholder': '请输入天数',
  'shop.coupon.validityDaysPlaceholder.addonBefore': '领取后',
  'shop.coupon.validityDaysPlaceholder.addonAfter': '天有效',
  'shop.coupon.useDesc': '使用说明',
  'shop.coupon.useDescPlaceholder': '支持200个字符输入，将展示给客户',
  'shop.coupon.remark': '活动备注',
  'shop.coupon.remarkPlaceholder': '支持200个字符输入，仅用于内部展示',
  'shop.coupon.validity.scopeBrandCategoryListRequired': '请至少选择一个品牌或品类',

  'shop.coupon.goodsRange': '商品范围',
  'shop.coupon.goodsRangeSpecific': '指定商品',
  'shop.coupon.goodsRangeGeneral': '通用',
  'shop.coupon.productSettings': '商品设置',
  'shop.coupon.itemScopeTypeSpecific': '指定商品',
  'shop.coupon.itemScopeTypeBrandCategory': '指定品牌品类',

  'shop.coupon.minAmountValidation': '起用金额不能小于0',
  'shop.coupon.minStockValidation': '发放总数不能小于0',
  'shop.coupon.maxDaysValidation': '有效期天数不能超过365天',
  'shop.coupon.minDaysValidation': '有效期天数不能小于1天',
  'shop.coupon.dateRangeValidation': '请选择生效和失效时间',

  // 商城目录
  'shop.category.add': '新增一级分类',
  'shop.category.updateSort': '更新排序',
  'shop.category.expandAll': '全部展开',
  'shop.category.collapseAll': '全部收起',

  'shop.category.categoryName': '分类名称',
  'shop.category.jumpType': '跳转类型',
  'shop.category.jumpContent': '跳转内容',
  'shop.category.status': '状态',
  'shop.category.sort': '排序',
  'shop.category.opt': '操作',

  'shop.category.add.child': '添加子分类',
  'shop.category.parent': '上级分类',
  'shop.category.image': '分类图片',
  'shop.category.updateImage': '上传图片',

  'shop.category.addNew': '新增分类',
  'shop.category.edit': '编辑分类',

  'shop.category.keyword': '关键词',
  'shop.category.show': '显示',
  'shop.category.hide': '隐藏',
};
