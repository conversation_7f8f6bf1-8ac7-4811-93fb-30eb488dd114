export default {
  collection: {
    title: '应收管理',
    detail: '应收详情',
    detailTitle: '应收明细',
    totalReceivableAmount: '应收总金额',
    onlyShowReceivable: '仅看应收金额大于0',
    onlyShowRemaining: '仅看应收金额大于0',
    exportDescription: '零售商应收管理数据导出',
    exportDetailDescription: '零售商应收管理明细数据导出',
    columns: {
      customer: '应收对象',
      store: '门店',
      overdueStatus: '逾期状态',
      receivableAmount: '应收金额',
      overdueAmount: '逾期金额',
      totalAmount: '总额度',
      availableAmount: '剩余额度',
      creditTerms: '账期(天)',
      arrearsDay: '欠款时间(天)',
      remainTerms: '剩余账期(天)',
      businessOrderNo: '业务单号',
      businessCompleteTime: '业务完成时间',
      orderAmount: '单据金额',
      receivedAmount: '已收金额',
      remainReceivableAmount: '未收金额',
      customerCode: '单位编码',
      contact: '联系人',
      contactPhone: '联系电话',
      contactAddress: '联系地址',
      settleType: '结算类型',
      currency: '币种',
      rate: '汇率',
    },
    summary: {
      orderTotal: '单据总额',
      receivedTotal: '已收总额',
      receivableTotal: '未收总额',
      overdueTotal: '逾期总额',
    },
  },
  receive: {
    title: '收款管理',
    detail: '收款详情',
    add: '新增收款',
    confirm: '确认收款',
    autoAssign: '自动分配',
    receivedAmount: '收款金额',
    currentWriteOff: '本次核销总计',
    writeOffOrder: '核销订单',
    writeOffAmountMismatch: '本次核销合计需等于收款金额',
    noAmountError: '收款金额不能为空',
    negativeAmountError: '收款金额小于等于0，请手动分配核销金额',
    noOrderSelectedWarning: '本次收款尚未选择具体的订单，请检查！',
    amountMismatchError: '各订单收款总金额与输入收款金额不一致，请修改！',
    columns: {
      serialNumber: '收款单号',
      store: '收款门店',
      receiveTime: '收款时间',
      customer: '收款对象',
      customerName: '客户',
      receivedAccount: '收款账户',
      receivedAmount: '收款金额',
      creator: '制单人',
      remark: '备注',
      receiveType: '收款类型',
      storeName: '门店',
      businessOrderNo: '业务单号',
      orderCompleteTime: '业务完成时间',
      transactionCompleteTime: '业务完成时间',
      orderAmount: '单据金额',
      receivedAmountPaid: '已收金额',
      unreceived: '未收金额',
      currentWriteOff: '本次核销',
      writeOffAmount: '核销金额',
      adjustAmount: '调整金额',
      status: '状态',
      advanceAmount: '转预收金额',
      currency: '币种',
      rate: '汇率',
      lossAmount: '本位币汇率损益',
      image: '图片',
      writeOffTotal: '本次核销合计',
    },
    placeholders: {
      businessOrderNo: '业务单号',
      enterAmount: '请输入',
    },
    status: {
      cancelled: '已取消',
      aborted: '审核不通过',
      draft: '草稿',
      pending: '待审核',
      unpayment: '待收款',
      payment: '已收款',
    },
    audit: {
      title: '审核',
      result: '审核结果',
      approve: '通过',
      reject: '驳回',
      opinion: '审核意见',
      'reject.reason.placeholder': '请输入不通过原因',
    },
    cancel: {
      title: '取消收款单',
      content: '是否确认取消该收款单？',
      confirm: '确认取消',
      giveup: '放弃',
    },
    multiCurrencyError: '每次收款只能核销一个币种的订单，不同币种订单请分开收款。',
    totalReceivable: '应收总额',
    receiveStore: '收款门店',
    receiveCurrency: '收款币种',
    rate: '汇率',
    adjustAmount: '调整金额',
    adjustReason: '调整原因',
    adjustType: {
      none: '无',
      round: '收款抹零',
      discount: '收款优惠',
    },
    filter: {
      due: '仅看已到期',
      overdue: '仅看已超期',
    },
    receiptImage: '收款图片',
    summary: {
      totalReceived: '收款金额',
      totalAdjust: '调整金额',
      totalWriteOff: '本次核销合计',
      totalAdvance: '转预收金额',
      local: {
        totalReceived: '收款金额(本位币)',
        totalAdjust: '调整金额(本位币)',
        totalWriteOff: '本次核销合计(本位币)',
        totalAdvance: '转预收金额(本位币)',
        loss: '本位币汇率损益',
      },
    },
  },
  payment: {
    title: '应付管理',
    detail: '应付详情',
    detailTitle: '应付明细',
    totalPayableAmount: '应付总金额',
    onlyShowPayable: '仅看应付金额大于0',
    onlyShowRemaining: '仅看应付金额大于0',
    exportDescription: '零售商应付管理数据导出',
    exportDetailDescription: '零售商应付管理明细数据导出',
    columns: {
      supplier: '供应商',
      payableObject: '应付对象',
      supplierCode: '单位编码',
      store: '门店',
      payableAmount: '应付金额',
      businessOrderNo: '业务单号',
      businessCompleteTime: '业务完成时间',
      orderAmount: '单据金额',
      paidAmount: '已付金额',
      remainPayableAmount: '应付金额',
      contact: '联系人',
      contactPhone: '联系电话',
      contactAddress: '联系地址',
    },
    summary: {
      orderTotal: '单据总额',
      paidTotal: '已付总额',
      payableTotal: '应付总额',
    },
  },
  supplierPayment: {
    title: '供应商付款',
    detailTitle: '付款详情',
    add: '新增付款',
    confirm: '确认付款',
    autoAssign: '自动分配',
    paymentAmount: '付款金额',
    currentWriteOff: '本次核销总计',
    writeOffOrder: '核销订单',
    writeOffAmountMismatch: '本次核销合计需等于付款金额',
    noAmountError: '付款金额不能为空且必须大于0元！',
    negativeAmountError: '付款金额小于等于0，请手动分配核销金额',
    noOrderSelectedWarning: '本次付款尚未选择具体的单据，请检查！',
    amountMismatchError: '各单据付款总金额与输入付款金额不一致，请修改！',
    totalPayable: '应付总额',
    paymentCurrency: '付款币种',
    multiCurrencyError: '每次付款只能核销一个币种的订单，不同币种订单请分开付款。',
    multiCurrencynotSupportError: '多币种订单不支持自动分配，请手动分配',
    summary: {
      paymentAmountLocal: '付款金额（本位币）',
      currentWriteOffLocal: '本次核销合计（本位币）',
      lossAmount: '本位币汇率损益',
    },
    columns: {
      serialNumber: '付款单号',
      paymentStore: '付款门店',
      paymentTime: '付款时间',
      supplier: '付款对象',
      supplierName: '付款对象',
      paymentAccount: '付款账户',
      paymentAmount: '付款金额',
      creator: '制单人',
      remark: '付款备注',
      storeName: '门店',
      businessOrderNo: '业务单号',
      businessCompleteTime: '业务完成时间',
      orderAmount: '单据金额',
      paidAmount: '已付金额',
      unpaidAmount: '未付金额',
      currentWriteOff: '本次核销合计',
      writeOffAmount: '核销金额',
      status: '付款状态',
      lossAmount: '本位币汇率损益',
      image: '图片',
      paymentPic: '付款图片',
    },
    placeholders: {
      businessOrderNo: '业务单号',
      enterAmount: '请输入',
    },
    detail: {
      columns: {
        storeName: '门店',
        orderNo: '单据号',
        billDate: '单据日期',
        orderAmount: '单据金额',
        paymentAmount: '核销金额',
      },
    },
  },
  cost: {
    title: '收支管理',
    income: '收入',
    expend: '支出',
    otherIncome: '其他收入',
    otherExpend: '其他支出',
    add: '新增',
    invalidateSuccess: '作废成功',
    confirmInvalidate: '确认作废吗',
    invalidate: '作废',
    columns: {
      store: '门店',
      incomeType: '收入类型',
      expendType: '支出类型',
      createTime: '制单时间',
      serialNumber: '收支单号',
      incomeStore: '收款门店',
      expendStore: '付款门店',
      incomeAmount: '收款金额',
      expendAmount: '付款金额',
      settlementAccount: '结算账户',
      creator: '制单人',
      incomeRemark: '收款备注',
      expendRemark: '付款备注',
      incomeNature: '收入性质',
      expendNature: '支出性质',
      // 付款对象
      incomeObject: '收款对象',
      // 收款对象
      expendObject: '付款对象',
    },
    form: {
      incomeStore: '收款门店',
      expendStore: '付款门店',
      incomeType: '收入类型',
      expendType: '支出类型',
      incomeAmount: '收款金额',
      expendAmount: '付款金额',
      settlementAccount: '结算账户',
      incomeRemark: '收款备注',
      expendRemark: '付款备注',
      yuan: '元',
      pic: '图片',
    },
    status: {
      cancelled: '已作废',
      aborted: '审核不通过',
      pending: '待审核',
      confirmed: '已确认',
    },
    type: {
      receivable: '应收',
      payable: '应付',
      income: '收入',
      expend: '支出',
    },
    recordType: '记{type}',
    detailTitle: '{type}详情',
  },
  flow: {
    title: '资金流水',
    totalIncome: '总收入',
    totalExpend: '总支出',
    columns: {
      businessOrderNo: '业务单号',
      occurTime: '发生时间',
      customerOrSupplier: '客户/供应商/往来单位',
      incomeExpendType: '收支类型',
      store: '门店',
      settlementAccount: '结算账户',
      income: '收入',
      expend: '支出',
    },
  },
  customer: {
    title: '账户管理',
    addAccount: '新增账户',
    editAccount: '编辑账户',
    confirmDelete: '确认删除吗?',
    columns: {
      accountName: '账户名称',
      bankName: '开户银行',
      bankCardNumber: '银行卡号',
      belongToStore: '所属门店',
      accountBalance: '账户余额',
      initialBalance: '期初余额',
      currency: '币种',
    },
    form: {
      accountName: '账户名称',
      bankName: '开户银行',
      bankCardNumber: '银行卡号',
      belongToStore: '所属门店',
      initialBalance: '期初余额',
      currency: '币种',
    },
  },
  tag: {
    title: '收支类型管理',
    incomeExpenseType: '收支类型',
    addIncomeExpenseType: '新增收支类型',
    editIncomeExpenseType: '编辑收支类型',
    columns: {
      incomeExpenseType: '收支类型',
      incomeDirection: '收入方向',
      source: '来源',
      isEnabled: '是否启用',
    },
    form: {
      incomeExpenseType: '收支类型',
      incomeExpenseDirection: '收支方向',
      income: '收入',
      expense: '支出',
      pleaseInput: '请输入',
    },
  },
  currency: {
    title: '币种管理',
    name: '币种名称',
    symbol: '币种符号',
    exchangeRate: '汇率',
    'exchangeRate.tooltip': '位币金额*汇率=其他币种金额',
    isBase: '本位币',
    status: '状态',
    source: '来源',
    add: '新增币种',
  },
  accountFlow: {
    title: '预收明细',
    totalAdvanceBalance: '总预收余额',
    columns: {
      bizNo: '业务单号',
      bizTime: '发生时间',
      customerName: '客户',
      bizTypeName: '业务类型',
      storeName: '门店',
      amount: '发生金额',
      accountBalance: '结余金额',
      createPerson: '操作人',
    },
    bizType: {
      RECEIVED_INCOME: '预收充值',
      ADVANCE_RECEIVED_IN: '收款使用预收',
      ADVANCE_SALE_OUT: '销售使用预收',
      ADVANCE_REFUND_IN: '退货转预收',
    },
  },
  otherRelated: {
    search: {
      info: {
        placeholder: '单位信息/联系人/联系方式',
      },
    },
    columns: {
      companyCode: '单位编码',
      companyName: '单位名称',
      shortName: '单位简称',
      abn: 'ABN',
      defaultContact: '默认联系人',
      defaultContactPhone: '默认联系方式',
      defaultAddress: '默认地址',
      remark: '备注',
      status: '状态',
      operation: '操作',
      companyInfo: '单位信息',
      contact: '联系人',
      contactPhone: '联系方式',
    },
    confirm: {
      enable: '确认启用吗?',
      disable: '确认禁用吗?',
    },
    detail: {
      title: '单位详情',
      basicInfo: '单位信息',
      contactInfo: '联系人信息',
      addressInfo: '地址信息',
      contact: {
        post: '职务',
        isDefault: '默认联系人',
      },
      address: {
        isDefault: '默认地址',
        number: '地址{number}',
      },
    },
    form: {
      title: {
        add: '新增其他往来单位',
        edit: '编辑其他往来单位',
      },
      basicInfo: '基础信息',
      companyName: '单位名称',
      companyNamePlaceholder: '供应商编码/名称/简称',
      companyCode: '单位编码',
      companyCodePlaceholder: '未输入系统将自动生成',
      shortName: '单位简称',
      abn: 'ABN',
      remark: '备注',
      contactInfo: '联系人信息',
      addressInfo: '地址信息',
      button: {
        addContact: '新增联系人',
        addAddress: '新增地址',
      },
      contact: {
        firstName: '姓',
        lastName: '名',
        contactPerson: '联系人',
        phone: '联系方式',
        post: '职务',
        email: '邮箱',
        remark: '备注',
        isDefault: '设为默认',
      },
      address: {
        postCode: '邮编',
        suburb: 'Suburb',
        state: 'State',
        detail: '详细地址',
        contact: '联系人',
        phone: '联系方式',
        isDefault: '设为默认',
      },
    },
  },
  bill: {
    detail: {
      title: '账单详情',
      billDetail: '账单明细',
      columns: {
        orderNo: '业务单号',
        billDate: '业务完成时间',
        storeName: '销售门店',
        orderAmount: '单据金额',
        receivedAmount: '已收金额',
        remainReceivableAmount: '未收金额',
      },
      summary: {
        billTotalAmount: '单据总额',
        billReceivedAmount: '已收总额',
        remainAmount: '未收总额',
      },
    },
    columns: {
      billNo: '账单编号',
      customerName: '客户',
      customerId: '客户编码',
      storeName: '归属门店',
      billCycle: '账单周期',
      cycleType: '账单类型',
      billTotalAmount: '账单金额',
      billReceivedAmount: '已收金额',
      remainAmount: '未收金额',
      billDate: '账单日',
      overdueDate: '超期日',
      status: '逾期状态',
    },
    status: {
      billed: '已出账',
      overdue: '已逾期',
      cleared: '已结清',
    },
  },
  budget: {
    title: '预算管理',
    button: {
      add: '新增预算',
      viewUsage: '使用情况',
    },
    modal: {
      add: '新增预算',
      edit: '编辑预算',
    },
    column: {
      budgetName: '预算名称',
      totalBudget: '预算金额',
      type: '预算类型',
      itemNames: '关联费用项',
      cycle: '预算周期',
      updater: '更新人',
      updateTime: '更新时间',
    },
    form: {
      budgetName: '预算名称',
      totalBudget: '预算金额',
      type: '预算类型',
      itemIds: '关联费用项',
      cycleType: '预算周期',
      year: '年度',
      month: '月度',
    },
    type: {
      purchase: '采购费用',
      other: '其他费用',
    },
    cycle: {
      annual: '年度',
      monthly: '月度',
    },
    usage: {
      title: '预算使用',
      detail: '使用明细',
      column: {
        usedAmount: '已使用',
        paidAmount: '已付款',
        totalAmount: '总预算',
      },
    },
  },
};
