export default {
  'external.title': '外采下单',
  'external.label.supplier': '供应商',
  'external.placeholder.selectSupplier': '请选择',
  'external.label.purchaseStore': '采购门店',
  'external.placeholder.selectPurchaseStore': '请选择',
  'external.label.receiveWarehouse': '收货仓库',
  'external.subtitle.purchaseOrderDetail': '采购单明细',
  'external.label.purchaseOrderNo': '采购单号',
  'external.label.purchaseStatus': '采购状态',
  'external.label.remarks': '备注',
  'external.placeholder.remarks': '请输入，最多100个字符',
  'external.label.totalPurchaseQuantity': '商品总数',
  'external.label.totalAmount': '商品总金额',
  'external.label.totalPurchaseAmount': '采购总金额',
  'external.label.freight': '运费',
  'external.label.deliveryTime': '预计到货时间',
  'external.button.confirmSettlement': '确认结算',
  'external.button.oneClickInStock': '一键入库',
  'external.button.submitAndPrint': '提交后打印',
  'external.button.submitPurchaseOrder': '提交采购单',
  'external.button.batchImport': '批量导入',
  'external.confirm.delete': '确认删除吗',
  'external.button.delete': '删除',
  'external.message.completeRequiredInfo': '请完善必填信息！',
  'external.message.deleteSuccess': '删除成功',
  'external.message.addSuccess': '添加成功',
  'external.import.taskDesc': '采购明细导入',
  'external.columns.index': '序号',
  'external.columns.itemName': '商品名称',
  'external.columns.itemCode': '商品编码',
  'external.columns.oeNo': 'OE',
  'external.columns.brandPartNo': '供应商编码',
  'external.columns.brand': '品牌',
  'external.columns.category': '分类',
  'external.columns.originRegion': '产地',
  'external.columns.specification': '规格',
  'external.columns.vehicleRemark': '车型备注',
  'external.columns.itemRemark': '商品备注',
  'external.columns.unit': '单位',
  'external.columns.localInventory': '本地库存',
  'external.columns.inventoryLowerLimit': '采购库存下限',
  'external.columns.inventoryUpperLimit': '采购库存上限',
  'external.columns.location': '库位',
  'external.columns.subtotal': '小计',
  'external.columns.purchasePrice': '采购单价',
  'external.columns.purchaseQuantity': '采购数量',
  'external.columns.operation': '操作',
  'external.columns.totalInventory': '总库存',
  'external.columns.preTaxSubtotal': '税前小计',
  'external.columns.afterTaxSubtotal': '税后小计',
  'external.columns.freightUnitPrice': '单件运费成本',
  'external.validation.minAmount': '最小金额0.01',
  'external.label.supplierPayable': '供应商应付',
  'external.button.viewDetails': '查看详情',

  // Purchase List Page
  'list.button.addPurchase': '新增采购',
  'list.button.export': '导出',
  'list.button.import': '导入',
  'list.export.taskDesc': '采购单列表导出',
  'list.import.taskDesc': '采购单列表导入',
  'list.import.history.title': '导入历史采购单',
  'list.import.freightCost.title': '导入采购商品运费成本',
  'list.import.freightCost.desc': '导入后将使用商品采购价+商品运费成本作为商品最终成本。',
  'list.message.submitSuccess': '提交成功',

  // Purchase List Table Columns
  'list.columns.orderNo': '采购单号',
  'list.columns.sourceNo': '关联单号',
  'list.columns.supplierName': '供应商名称',
  'list.columns.goodsInfo': '商品信息',
  'list.columns.goodsInfo.placeholder': '商品编码/商品名称/OE/供应商编码',
  'list.columns.purchaseStore': '采购门店',
  'list.columns.receiveWarehouse': '收货仓库',
  'list.columns.purchaseQuantity': '采购数量',
  'list.columns.purchaseAmount': '采购金额',
  'list.columns.purchaseUser': '制单人',
  'list.columns.payType': '结算方式',
  'list.columns.payStatus': '结算状态',
  'list.columns.balanceStatus': '付款状态',
  'list.columns.orderTime': '采购时间',
  'list.columns.orderStatus': '单据状态',
  'list.columns.currency': '币种',
  'list.tag.replenishment': '补货',
  'list.button.edit': '编辑',
  'list.button.void': '作废',
  'list.button.withdraw': '撤回',
  'list.button.againOrder': '再来一单',
  'list.confirm.void': '确认作废吗',
  'list.confirm.withdraw': '确认撤回吗',

  // Purchase Detail Page
  'list.payStatus.unBalance': '未结算',
  'list.payStatus.partBalance': '已结算',
  'detail.title.etcOrderNo': '一体系订单号',
  'detail.button.edit': '编辑',
  'detail.button.void': '作废',
  'detail.button.withdraw': '撤回',
  'detail.button.print': '打印',
  'detail.button.printLabel': '打印标签',
  'detail.button.confirmSettlement': '确认结算',
  'detail.button.oneClickInStock': '一键入库',
  'detail.button.audit': '审核',
  'detail.button.afterSales': '发起售后',
  'detail.button.afterSalesRecord': '售后记录',
  'detail.button.modify': '修改',
  'detail.confirm.void': '是否确认作废？',
  'detail.confirm.withdraw': '是否确认撤回？',
  'detail.confirm.oneClickInStock': '是否确认一键入库？',

  // Purchase Detail Labels
  'detail.label.supplier': '供应商',
  'detail.label.orderStatus': '订单状态',
  'detail.label.purchaseAmount': '采购金额',
  'detail.label.payType': '结算方式',
  'detail.label.purchaseStore': '采购门店',
  'detail.label.receiveWarehouse': '收货仓库',
  'detail.label.orderTime': '下单时间',
  'detail.label.creator': '制单人',
  'detail.label.purchaseRemark': '采购备注',
  'detail.label.auditor': '审核人',
  'detail.label.auditTime': '审核时间',
  'detail.label.remark': '备注',

  // Purchase Detail Sections
  'detail.section.logisticsDetail': '物流详情',
  'detail.section.goodsDetail': '商品明细',
  'detail.button.collapse': '收起',
  'detail.button.viewMore': '查看更多',

  // Purchase Detail Summary
  'detail.summary.totalQuantity': '商品总数',
  'detail.summary.totalAmount': '商品总金额',
  'detail.summary.freight': '运费',
  'detail.summary.totalPurchaseAmount': '采购总金额',

  // Purchase Detail Table Columns
  'detail.columns.itemName': '商品名称',
  'detail.columns.itemCode': '商品编码',
  'detail.columns.oe': 'OE',
  'detail.columns.brandPartNo': '供应商编码',
  'detail.columns.brand': '品牌',
  'detail.columns.category': '分类',
  'detail.columns.unit': '单位',
  'detail.columns.purchasePrice': '采购单价',
  'detail.columns.purchaseQuantity': '采购数量',
  'detail.columns.subtotal': '小计',
  'detail.columns.operation': '操作',

  // Audit Modal
  'audit.modal.title': '审核',
  'audit.option.approve': '通过',
  'audit.option.reject': '不通过',
  'audit.placeholder.reason': '请输入不通过原因',
  'audit.placeholder.remark': '请输入',
  'audit.validation.selectResult': '请选择审核结果',
  'audit.validation.rejectReason': '请输入不通过原因',

  // After Sales Record Modal
  'afterSales.modal.title': '售后记录',
  'afterSales.button.viewDetail': '查看详情',
  'afterSales.validation.selectVehicle': '请选择车型',

  // Warehouse Modal
  'warehouse.modal.title': '收货仓库',
  'warehouse.label.receiveWarehouse': '收货仓库',
  'warehouse.placeholder.selectWarehouse': '请选收货仓库',
  'warehouse.validation.selectWarehouse': '请选择收货仓库',

  // Capital Flow
  'capitalFlow.title': '结算记录',
  'capitalFlow.columns.settlementTime': '结算时间',
  'capitalFlow.columns.settlementAccount': '结算账户',
  'capitalFlow.columns.settlementAmount': '结算金额',

  // Operation Detail
  'operationDetail.title': '操作记录',
  'operationDetail.columns.operationNode': '操作节点',
  'operationDetail.columns.operationTime': '操作时间',
  'operationDetail.columns.operator': '操作人',

  // Order Status
  'orderStatus.draft': '草稿',
  'orderStatus.auditing': '审核中',
  'orderStatus.confirm': '已确认',
  'orderStatus.toArrival': '待入库',
  'orderStatus.received': '已入库',
  'orderStatus.complete': '已完成',
  'orderStatus.close': '已作废',
  'orderStatus.reject': '审核不通过',

  // Pay Type Status
  'payType.cash': '现款',
  'payType.hangingAccounts': '挂账',

  // Balance Status
  'balanceStatus.unBalance': '未付款',
  'balanceStatus.partBalance': '部分付款',
  'balanceStatus.allBalance': '已付款',

  // Returns List
  'returns.list.title': '采购退货列表',
  'returns.list.button.add': '新增采购退货',
  'returns.list.button.export': '导出',
  'returns.list.export.taskDesc': '采购退货导出',
  'returns.list.columns.returnOrderNo': '退货单号',
  'returns.list.columns.supplierName': '供应商名称',
  'returns.list.columns.returnTime': '退货时间',
  'returns.list.columns.orderStatus': '单据状态',
  'returns.list.columns.returnStore': '退货门店',
  'returns.list.columns.outWarehouse': '发货仓库',
  'returns.list.columns.returnAmount': '退货金额',
  'returns.list.columns.payType': '结算方式',
  'returns.list.columns.payStatus': '结算状态',
  'returns.list.columns.completeTime': '退货完成时间',
  'returns.list.columns.creator': '制单人',
  'returns.list.columns.remark': '备注',
  'returns.list.search.productInfo': '商品信息',
  'returns.list.search.productInfo.placeholder': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'returns.list.search.productInfo.tooltip': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'returns.list.button.edit': '编辑',
  'returns.list.button.void': '作废',
  'returns.list.button.withdraw': '撤回',
  'returns.list.confirm.void': '确认作废吗',
  'returns.list.confirm.withdraw': '确认撤回吗',

  // Return Order Status
  'returnOrderStatus.draft': '草稿',
  'returnOrderStatus.toOutbound': '待出库',
  'returnOrderStatus.outbound': '已出库',
  'returnOrderStatus.complete': '已完成',
  'returnOrderStatus.close': '已关闭',

  // Returns Detail
  'returns.detail.button.edit': '编辑',
  'returns.detail.button.void': '作废',
  'returns.detail.button.withdraw': '撤回',
  'returns.detail.button.print': '打印',
  'returns.detail.button.confirmSettlement': '确认结算',
  'returns.detail.button.oneClickOutbound': '一键出库',
  'returns.detail.confirm.void': '是否确认作废？',
  'returns.detail.confirm.withdraw': '是否确认撤回？',
  'returns.detail.confirm.oneClickOutbound': '是否确认一键出库？',
  'returns.detail.label.orderStatus': '单据状态',
  'returns.detail.label.returnAmount': '退款金额',
  'returns.detail.label.paymentMethod': '结算方式',
  'returns.detail.label.returnStore': '退款门店',
  'returns.detail.label.shippingWarehouse': '发货仓库',
  'returns.detail.label.orderTime': '下单时间',
  'returns.detail.label.creator': '制单人',
  'returns.detail.label.currency': '币种',
  'returns.detail.label.exchangeRate': '汇率',
  'returns.detail.label.gstExcluded': 'GST Excluded',
  'returns.detail.label.remark': '订单备注',
  'returns.detail.section.productDetail': '商品明细',
  'returns.detail.summary.totalQuantity': '商品总数',
  'returns.detail.summary.totalAmount': '商品总金额',
  'returns.detail.summary.gst': 'GST',
  'returns.detail.summary.totalReturnAmount': '退货总金额',
  'returns.detail.title': '采购退货详情',

  // Supplier List
  'supplier.list.title': '供应商列表',
  'supplier.list.button.add': '新增供应商',
  'supplier.list.button.import': '导入',
  'supplier.list.button.export': '导出',
  'supplier.list.import.taskDesc': '供应商导入',
  'supplier.list.export.taskDesc': '供应商导出',
  'supplier.list.modal.title.add': '新增供应商',
  'supplier.list.modal.title.edit': '编辑供应商',
  'supplier.list.columns.supplierCode': '供应商编码',
  'supplier.list.columns.supplierInfo': '供应商信息',
  'supplier.list.columns.supplierInfo.placeholder': '供应商编码/名称/简称',
  'supplier.list.columns.supplierName': '供应商名称',
  'supplier.list.columns.shortName': '供应商简称',
  'supplier.list.columns.defaultContact': '默认联系人',
  'supplier.list.columns.defaultContactPhone': '默认联系方式',
  'supplier.list.columns.defaultAddress': '默认供应商地址',
  'supplier.list.columns.status': '状态',
  'supplier.list.columns.createTime': '创建时间',
  'supplier.list.columns.abn': 'ABN',
  'supplier.list.columns.settleType': '结算类型',
  'supplier.list.columns.financialEmail': '财务邮箱',
  'supplier.list.search.contact': '联系人',
  'supplier.list.search.contactPhone': '联系方式',
  'supplier.list.button.edit': '编辑',
  'supplier.list.confirm.statusChange': '确认{action}吗',

  // Supplier Status
  'supplierStatus.enable': '启用',
  'supplierStatus.disable': '禁用',

  // Supplier Detail
  'supplier.detail.title': '供应商详情',
  'supplier.detail.basic.supplierCode': '供应商编码',
  'supplier.detail.basic.shortName': '供应商简称',
  'supplier.detail.basic.remark': '供应商备注',
  'supplier.detail.contact.title': '联系人信息',
  'supplier.detail.contact.defaultContact': '默认联系人',
  'supplier.detail.contact.contactPhone': '联系方式',
  'supplier.detail.contact.qq': 'QQ',
  'supplier.detail.contact.wechat': '微信',
  'supplier.detail.contact.email': '邮箱',
  'supplier.detail.address.title': '地址信息',
  'supplier.detail.address.addressNumber': '地址{number}',
  'supplier.detail.address.defaultAddress': '默认地址',
  'supplier.detail.address.region': '所在地区',
  'supplier.detail.address.detailAddress': '详细地址',
  'supplier.detail.address.contactPerson': '联系人',
  'supplier.detail.address.contactPhone': '联系方式',
  'supplier.detail.settlement.title': '结算信息',
  'supplier.detail.settlement.initAccount': '初期应付',

  // Supplier Operation
  'supplier.operation.basic.title': '基础信息',
  'supplier.operation.basic.supplierName': '供应商名称',
  'supplier.operation.basic.supplierCode': '供应商编码',
  'supplier.operation.basic.shortName': '供应商简称',
  'supplier.operation.basic.remark': '供应商备注',
  'supplier.operation.basic.remark.placeholder': '请输入，最多100个字符',
  'supplier.operation.contact.title': '联系人信息',
  'supplier.operation.contact.add': '新增联系人',
  'supplier.operation.contact.columns.defaultContact': '默认联系人',
  'supplier.operation.contact.columns.contactPerson': '联系人',
  'supplier.operation.contact.columns.contactPhone': '联系方式',
  'supplier.operation.contact.columns.post': '职务',
  'supplier.operation.contact.columns.email': '邮箱',
  'supplier.operation.contact.columns.remark': '备注',
  'supplier.operation.contact.button.edit': '编辑',
  'supplier.operation.contact.button.delete': '删除',
  'supplier.operation.contact.confirm.delete': '确认删除吗',
  'supplier.operation.address.title': '地址信息',
  'supplier.operation.address.add': '新增地址',
  'supplier.operation.address.columns.defaultAddress': '默认地址',
  'supplier.operation.address.columns.region': '省市区',
  'supplier.operation.address.columns.detailAddress': '详细地址',
  'supplier.operation.address.columns.contactPerson': '联系人',
  'supplier.operation.address.columns.contactPhone': '联系方式',
  'supplier.operation.address.button.edit': '编辑',
  'supplier.operation.address.button.delete': '删除',
  'supplier.operation.address.confirm.delete': '确认删除吗',
  'supplier.operation.settlement.title': '结算信息',
  'supplier.operation.settlement.initAccount': '初期应付',
  'supplier.operation.settlement.settleType': '结算方式',
  'supplier.operation.settlement.gstExcluded': 'GST Excluded',
  'supplier.operation.settlement.isMultiCurrency': '多币种结算',
  'supplier.operation.settlement.bankAccount': '银行账户',
  'supplier.operation.settlement.addAccount': '新增账户',
  'supplier.operation.settlement.columns.accountName': '账户名称',
  'supplier.operation.settlement.columns.bsb': 'BSB',
  'supplier.operation.settlement.columns.accountNumber': 'Account Number',
  'supplier.operation.settlement.columns.swiftCode': 'Swift Code',
  'supplier.operation.basic.abn': 'ABN',
  'supplier.operation.basic.clientCode': 'Client Code',
  'supplier.operation.basic.country': 'Country',
  'supplier.operation.basic.universalEmail': '通用邮箱',
  'supplier.operation.basic.financeEmail': '财务邮箱',
  'supplier.operation.basic.telephone': '座机电话',
  'supplier.operation.address.columns.postCode': '邮编',
  'supplier.operation.address.columns.provinceCityDistrict': 'State/Suburb',
  'supplier.operation.address.columns.state': 'State',
  'supplier.operation.address.columns.suburb': 'Suburb',
  'supplier.operation.address.columns.contactName': '联系人',
  'supplier.operation.settlement.currency': '元',

  // Returns Operation
  'returns.operation.form.supplier': '供应商',
  'returns.operation.form.supplier.placeholder': '请选择',
  'returns.operation.form.store': '发起门店',
  'returns.operation.form.store.placeholder': '请选择',
  'returns.operation.form.warehouse': '发货仓库',
  'returns.operation.form.warehouse.placeholder': '请选择',
  'returns.operation.form.sourceNo': '关联单号',
  'returns.operation.tab.type': '退货类型',
  'returns.operation.tab.returnByOrder': '采购单退货',
  'returns.operation.tab.returnByOrder.description': '选择采购单里的商品退货',
  'returns.operation.tab.returnByItem': '商品退货',
  'returns.operation.tab.returnByItem.description': '选择商品退货',
  'returns.operation.detail.title': '退货单明细',
  'returns.operation.detail.orderNo': '退货单号',
  'returns.operation.detail.status': '退货状态',
  'returns.operation.detail.delete': '删除',
  'returns.operation.form.remark': '备注',
  'returns.operation.form.remark.placeholder': '请输入，最多100个字符',
  'returns.operation.summary.returnQuantity': '退款数量',
  'returns.operation.summary.returnAmount': '退款金额',
  'returns.operation.checkbox.confirmSettlement': '确认结算',
  'returns.operation.checkbox.directOutbound': '直接出库',
  'returns.operation.checkbox.printAfterSubmit': '提交后打印',
  'returns.operation.button.submit': '提交采购退货',
  'returns.operation.message.completeRequired': '请完善必填信息！',
  'returns.operation.message.maxItems': '加购商品数量不能超过200',
  'returns.operation.message.addSuccess': '添加成功',
  'returns.operation.message.fillReturnAmount': '请填写退货金额！',
  'returns.operation.message.fillReturnQuantity': '请填退货数量！',

  // Returns Operation - Order Returns Columns
  'returns.operation.orderColumns.productInfo': '商品信息',
  'returns.operation.orderColumns.productInfo.placeholder': '名称/编码/OE/供应商编码',
  'returns.operation.orderColumns.purchaseOrderNo': '采购单号',
  'returns.operation.orderColumns.purchaseTime': '采购时间',
  'returns.operation.orderColumns.productCode': '商品编码',
  'returns.operation.orderColumns.productName': '商品名称',
  'returns.operation.orderColumns.oe': 'OE',
  'returns.operation.orderColumns.brandPartNo': '供应商编码',
  'returns.operation.orderColumns.brand': '品牌',
  'returns.operation.orderColumns.purchaseStore': '采购门店',
  'returns.operation.orderColumns.receiveWarehouse': '收货仓库',
  'returns.operation.orderColumns.paymentMethod': '支付方式',
  'returns.operation.orderColumns.actualPrice': '实付单价',
  'returns.operation.orderColumns.purchaseQuantity': '采购数量',
  'returns.operation.orderColumns.returnableQuantity': '可退数量',
  'returns.operation.orderColumns.returnAmount': '退款单价',
  'returns.operation.orderColumns.returnQuantity': '退货数量',
  'returns.operation.orderColumns.button.return': '退货',

  // Returns Operation - Detail Columns
  'returns.operation.detailColumns.productCode': '商品编码',
  'returns.operation.detailColumns.productName': '商品名称',
  'returns.operation.detailColumns.oe': 'OE',
  'returns.operation.detailColumns.brandPartNo': '供应商编码',
  'returns.operation.detailColumns.brand': '品牌',
  'returns.operation.detailColumns.purchaseOrderNo': '采购单号',
  'returns.operation.detailColumns.purchaseStore': '采购门店',
  'returns.operation.detailColumns.returnAmount': '退货单价',
  'returns.operation.detailColumns.returnQuantity': '退货数量',
  'returns.operation.detailColumns.returnReason': '退货原因',
  'returns.operation.detailColumns.returnReason.placeholder': '请输入',

  // Stock Up List
  'stockUp.list.button.addStockUp': '新增采购建议',
  'stockUp.list.button.transferSuggestion': '新增调拨建议',
  'stockUp.list.menu.stockByInventory': '按库存补货',
  'stockUp.list.menu.stockBySales': '按销售补货',
  'stockUp.list.loading.generating': '正在生成补货建议...',
  'stockUp.list.message.generating': '补货建议生成中，请稍后查看',
  'stockUp.list.columns.index': '序号',
  'stockUp.list.columns.suggestionNo': '采购建议单号',
  'stockUp.list.columns.transferSuggestionNo': '调拨建议单号',
  'stockUp.list.columns.suggestionName': '补货建议名称',
  'stockUp.list.columns.status': '状态',
  'stockUp.list.columns.sumQuantity': '商品数量',
  'stockUp.list.columns.createTime': '创建时间',
  'stockUp.list.columns.updateTime': '更新时间',
  'stockUp.list.columns.operation': '操作',
  'stockUp.list.button.view': '查看',

  // Stock Up Status
  'stockUpStatus.effective': '已生成',
  'stockUpStatus.expire': '已失效',
  'stockUpStatus.generating': '生成中',
  'stockUpStatus.replenished': '已补货',

  // Stock By Inventory Modal
  'stockUp.inventory.modal.title': '按库存补货',
  'stockUp.inventory.modal.alert':
    '系统会根据补货规则，查询所选补货仓库里满足条件的商品，如商品未设置库存上下限或建议补货数量为0，建议补货结果里不会展示。',
  'stockUp.inventory.form.warehouse': '补货仓库',
  'stockUp.inventory.form.warehouse.placeholder': '请选择',
  'stockUp.inventory.form.rule': '补货规则',
  'stockUp.inventory.form.rule.upperUp': '低于上限补到上限',
  'stockUp.inventory.form.rule.lowUp': '低于下限补到上限',

  // Stock By Sales Modal
  'stockUp.sales.modal.title': '按销售补货',
  'stockUp.sales.modal.alert':
    '系统会查询指定销售门店，指定下单时间内售出的商品，并根据仓库设置进行补货。您可以根据自己补货结果筛选建议明细。',
  'stockUp.sales.form.store': '销售门店',
  'stockUp.sales.form.store.placeholder': '请选择',
  'stockUp.sales.form.orderTime': '下单时间',
  'stockUp.sales.form.salesStatus': '销售状态',
  'stockUp.sales.form.preset.today': '今天',
  'stockUp.sales.form.preset.recent3Days': '近3天',
  'stockUp.sales.form.preset.recent7Days': '近7天',
  'stockUp.sales.form.preset.recent30Days': '近30天',
  'stockUp.sales.form.preset.thisMonth': '本月',
  'stockUp.sales.status.draft': '草稿',
  'stockUp.sales.status.waitToOutbound': '待出库',
  'stockUp.sales.status.outboundFinish': '已出库',
  'stockUp.sales.status.tradeSuccess': '已完成',
};
