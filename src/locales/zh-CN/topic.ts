export default {
    'decoration.title.pageSetting': '页面设置',
    'decoration.title.components': '基础组件',
    'decoration.title.pageLayout': '页面布局',
    'decoration.label.pageTitle': '页面标题',
    'decoration.placeholder.pageTitle': '请输入页面标题',
    'decoration.label.isHome': '设置为首页',
    'decoration.label.pageRemark': '页面备注',
    'decoration.button.exit': '退出编辑',

    'decoration.icon.hotspot': '图片热区',
    'decoration.icon.imageAd': '图片广告',
    'decoration.icon.moduleTitle': '模块标题',
    'decoration.icon.productList': '商品',
    'decoration.icon.coupon': '优惠券',
    'decoration.icon.container': '容器',
    'decoration.icon.divider': '分割线',

    'decoration.tips.dragComponent': '从左侧拖拽组件到画布区域',
    'decoration.tips.clickComponent': '点击组件可编辑属性',

    'decoration.productList.mock.itemName': 'Here will display product name, up to 2 lines',

    'decoration.divider.padding.horizontal': '水平间距',
    'decoration.divider.padding.vertical': '垂直间距',

    'decoration.imageAd.empty': '点击编辑图片广告',
    'decoration.imageAd.upload': '添加图片',
    'decoration.imageAd.reUpload': '更换图片',
    'decoration.imageAd.upload.tips': '上传多张图时轮播展示',


    'decoration.moduleTitle.titleContent': '标题内容',
    'decoration.moduleTitle.titleContent.placeholder': '模块标题',
    'decoration.moduleTitle.showSubtitle': '显示副标题',
    'decoration.moduleTitle.subtitleContent.placeholder': '所有商品免费配送',
    'decoration.moduleTitle.subtitleContent': '副标题内容',
    'decoration.moduleTitle.showMore': '显示更多',
    'decoration.moduleTitle.showMore.placeholder': '查看更多 >',
    'decoration.moduleTitle.promptText': '提示文字',
    'decoration.moduleTitle.linkSetting': '链接设置',

    'decoration.linkSetting.type.none': '无链接',
    'decoration.linkSetting.type.product': '商品',
    'decoration.linkSetting.type.topic': '专题页',
    'decoration.linkSetting.type.coupon': '优惠券',
    'decoration.linkSetting.type.activity': '活动',
    'decoration.linkSetting.type.keyword': '关键词',
    'decoration.linkSetting.type.placeholder': '请选择跳转类型',
    'decoration.linkSetting.keyword.placeholder': '请输入关键词',
    'decoration.linkSetting.select': '选择{type}',


    'decoration.imageHotspot.placeholder': '点击编辑图片热区',
    'decoration.imageHotspot.suggestion': '建议图片宽度1280px，高度不限。',
    'decoration.imageHotspot.instructions': '在下方图片区域拖拽创建，或点击按钮添加。',
    'decoration.imageHotspot.upload': '上传图片',
    'decoration.imageHotspot.reupload': '更换图片',
    'decoration.imageHotspot.upload.placeholder': '请先上传图片',
    'decoration.imageHotspot.hotspotsLinks': '热区链接',
    'decoration.imageHotspot.hotspots': '热区',
    'decoration.imageHotspot.deleteHotspot': '删除热区',
    'decoration.imageHotspot.addHotspot': '添加热区',

    'decoration.coupon': '优惠券',
    'decoration.coupon.management': '优惠券管理',
    'decoration.coupon.select': '选择优惠券',
    'decoration.coupon.claim': '领取',
    'decoration.coupon.threshold': '满${threshold}可用',
    'decoration.coupon.noCoupons': '点击添加优惠券',

    // Product List Settings
    'decoration.productList.settings.conditionsTitle': '以下条件至少设置一条',
    'decoration.productList.settings.productTag': '商品标签',
    'decoration.productList.settings.selectTag': '请选择标签',
    'decoration.productList.settings.onlyCampaignProducts': '仅活动商品',
    'decoration.productList.settings.displayCount': '显示数量',
    'decoration.productList.settings.display': '显示',
    'decoration.productList.settings.displayAll': '显示全部',
    'decoration.productList.settings.countPlaceholder': '数量',
    'decoration.productList.settings.selectProduct': '选择商品',
    'decoration.productList.settings.selectedCount': '已选 {count} 个商品',
    'decoration.productList.settings.productSource': '商品来源',
    'decoration.productList.settings.specifiedProducts': '指定商品',
    'decoration.productList.settings.conditionalProducts': '指定条件商品',
    'decoration.productList.settings.listStyle': '列表样式',
    'decoration.productList.settings.onePerRow': '一行一个',
    'decoration.productList.settings.twoPerRow': '一行二个',
    'decoration.productList.settings.horizontalScroll': '横向滚动',
    'decoration.productList.settings.type': '类型',
    'decoration.productList.settings.product': '商品',
    'decoration.productList.settings.productGroup': '商品分组',
    'decoration.productList.settings.newGroup': '新分组 {number}',
    'decoration.productList.settings.group': '分组',
    'decoration.productList.settings.filter.onlyActive': '仅查看活动商品',
};