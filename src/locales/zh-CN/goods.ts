export default {
  'property.inputFieldLabel.brandName': '品牌名称',
  'property.inputFieldLabel.brandShortName': '品牌简称',
  'property.inputFieldLabel.unitName': '单位名称',
  'property.inputFieldLabel.categoryName': '品类名称',
  'property.inputFieldLabel.originRegionName': '产地名称',
  'property.inputFieldLabel.tagName': '商品标签',
  'property.inputFieldLabel.priceLevelName': '价格等级',
  'property.button.brand': '品牌',
  'property.button.category': '品类',
  'property.button.unit': '单位',
  'property.button.originRegionRelation': '产地',
  'property.button.tag': '标签',
  'property.button.priceLevel': '价格等级',
  'property.tab.brandManagement': '品牌管理',
  'property.tab.categoryManagement': '品类管理',
  'property.tab.unitManagement': '单位管理',
  'property.tab.originRegionRelationManagement': '产地管理',
  'property.tab.tagsManagement': '商品标签管理',
  'property.tab.tagManagement': '商品标签管理',
  'property.tab.priceLevelManagement': '价格等级管理',
  'property.modal.title.add': '新增{name}',
  'property.modal.title.addSuffix': '{name}添加',
  'property.common.index': '序号',
  'property.common.source': '来源',
  'property.common.status': '状态',
  'property.common.visibleToCustomer': '对客显示',
  'property.formModal.placeholder': '可批量添加，一行表示一个{label}',
  'list.createGoodsTitle': '新建商品',
  'list.createGeneralGroupTitle': '新增通用组',
  'list.editGoodsTitle': '商品编辑',
  'list.createGoodsButton': '新建商品',
  'list.importGoodsButton': '导入商品',
  'list.exportGoodsTaskDesc': '零售商商品导出',
  'list.exportGoodsButton': '导出商品',
  'list.onlineButton': '上架',
  'list.offlineButton': '下架',
  'createForm.addBrandSuccess': '【{brandName}】品牌新增成功！',
  'createForm.inputBrandNameRequired': '请输入品牌名称！',
  'createForm.baseInfoTitle': '基础信息',
  'createForm.oeLabel': 'OE',
  'createForm.brandPartNoLabel': '供应商编码',
  'createForm.realBrandPartNoLabel': '真实供应商编码',
  'createForm.itemNameLabel': '商品名称',
  'createForm.itemSnLabel': '商品编码',
  'createForm.channelLabel': '销售渠道',
  'createForm.channelLabel.store': '门店',
  'createForm.channelLabel.ecommerce': '商城',
  'createForm.supplier.default': '默认供应商',
  'createForm.supplier.add': '新增供应商',
  'createForm.supplier': '供应商',
  'createForm.purchasePrice': '采购价',
  'createForm.moq': 'MOQ',
  'createForm.mpq': 'MPQ',
  'createForm.memCodeLabel': '助记码',
  'createForm.skuWeightLabel': '重量(kg)',
  'createForm.skuSizeLabel': '长宽高',
  'createForm.skuSizeLabel.length': '长',
  'createForm.skuSizeLabel.width': '宽',
  'createForm.skuSizeLabel.height': '高',
  'createForm.brandLabel': '品牌',
  'createForm.tags': '商品标签',
  'createForm.realBrandLabel': '真实品牌',
  'createForm.ownCodeLabel': '商品自编码',
  'createForm.orderCodeLabel': '订货编码',
  'createForm.barCodeLabel': '商品条码',
  'createForm.searchEmpty': '搜索为空',
  'createForm.addBrandAndSelectButton': '新增品牌并选择',
  'createForm.categoryLabel': '分类',
  'createForm.unitLabel': '商品单位',
  'createForm.specLabel': '商品规格',
  'createForm.originRegionLabel': '商品产地',
  'createForm.supplierLabel': '供应商',
  'createForm.adaptModelLabel': '车型备注',
  'createForm.remarkLabel': '商品备注',
  'createForm.imagesLabel': '商品图片',
  'createForm.mainImage': '商品主图',
  'createForm.priceInfoTitle': '价格信息',
  'createForm.mallDetailTitle': '商城详情',
  'createForm.purchaseTitle': '采购信息',
  'createForm.minPurchaseStock': '采购库存下限',
  'createForm.maxPurchaseStock': '采购库存上限',
  'createForm.suggestPriceLabel': '销售价',
  'createForm.suggestPriceTooltip': '商品的建议销售价',
  'createForm.lowPriceLabel': '最低售价',
  'createForm.lowPriceTooltip': '销售开单时商品价格低于最低售价，系统会进行提示',
  'createForm.purchasePriceLabel': '采购价',
  'createForm.purchasePriceTooltip': '商品的参考采购价',
  'importModal.title': '批量导入',
  'importModal.createCustomGoodsTitle': '批量创建自建商品',
  'importModal.createCustomGoodsDesc': '您可以导入商品信息批量创建自建商品',
  'importModal.importPriceTitle': '批量导入商品价格',
  'importModal.importPriceDesc':
    '您可以使用商品编码，批量维护商品价格，包含：销售价、最低售价、采购价以及分层定价',
  'importModal.importSupplierRelationTitle': '批量导入供应商与商品关联',
  'importModal.importSupplierRelationDesc':
    '您可以导入商品编码和供应商名称，批量新增/删除供应商与商品关联',
  'importModal.general.group.update.batch': '批量更新通用组',
  'importModal.general.group.update.batch.desc': '您可以导入商品信息批量更新通用组',
  'importModal.downloadTemplate': '下载模板',
  'importModal.importFile': '导入文件',
  'importModal.taskCreatedTitle': '提示',
  'importModal.taskCreatedContent': '导入任务已经创建成功，是否查看导入结果？',
  'importModal.viewResultButton': '前往查看',
  'list.table.itemGroupName': '通用组',
  'list.table.itemSn': '商品编码',
  'list.table.itemName': '商品名称',
  'list.table.queryKeyword': '商品信息',
  'list.table.queryKeywordPlaceholder': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'list.table.queryKeywordTooltip': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'list.table.oeNos': 'OE',
  'list.table.brandPartNos': '供应商编码',
  'list.table.brand': '品牌',
  'list.table.brandName': '品牌',
  'list.table.category': '分类',
  'list.table.categoryName': '分类',
  'list.table.tags': '商品标签',
  'list.table.unit': '单位',
  'list.table.spec': '规格',
  'list.table.originRegion': '产地',
  'list.table.supplier': '供应商',
  'list.table.suggestPrice': '销售价',
  'list.table.lowPrice': '最低售价',
  'list.table.purchasePrice': '采购价',
  'list.table.inventoryNum': '总库存',
  'list.table.adaptModel': '车型备注',
  'list.table.remark': '商品备注',
  'list.table.memCode': '助记码',
  'list.table.status': '状态',
  'list.table.confirmOperation': '确认{operType}吗?',
  'list.table.operation.enable': '上架',
  'list.table.operation.disable': '下架',
  'list.status.disable': '已下架',
  'list.status.enable': '已上架',

  // GoodsSearch component
  'search.tab.synthesis': '综合查询',
  'search.tab.vin': 'VIN码查询',
  'search.tab.salesSlip': '销售商品查询',
  'search.form.goodsInfo': '商品信息',
  'search.form.goodsInfoPlaceholder': '请输商品名称/编码/OE',
  'search.form.brand': '品牌',
  'search.form.category': '分类',
  'search.form.orderStatus': '单据状态',
  'search.form.orderNo': '销售单号',
  'search.form.orderTime': '下单时间',
  'search.button.query': '查询',
  'search.button.reset': '重置',
  'search.button.batchAdd': '批量添加',
  'search.checkbox.exactSearch': '精确查找',
  'search.checkbox.inStockOnly': '仅看本地有货',
  'search.checkbox.inTotalStockOnly': '仅看有货',
  'search.checkbox.supplierSupplyOnly': '仅看供应商供应',
  'search.checkbox.belowStockLimit': '低于库存下限',
  'search.message.fillPrice': '请填写价格',
  'search.message.fillQuantity': '请填写数量',
  'search.message.selectGoods': '请选择商品',
  'search.tooltip.keyboardShortcuts': '键盘快捷键',

  // GoodsSearch table columns
  'search.table.index': '序号',
  'search.table.itemSn': '商品编码',
  'search.table.itemName': '商品名称',
  'search.table.oeNos': 'OE',
  'search.table.brandPartNos': '供应商编码',
  'search.table.brandName': '品牌',
  'search.table.categoryName': '分类',
  'search.table.originRegionName': '产地',
  'search.table.spec': '规格',
  'search.table.unitName': '单位',
  'search.table.adaptModel': '车型备注',
  'search.table.remark': '商品备注',
  'search.table.avaNum': '本地库存',
  'search.table.locationCode': '库位',
  'search.table.suggestPrice': '建议售价',
  'search.table.lastSalePrice': '上次售价',
  'search.table.lowPrice': '最低售价',
  'search.table.costPrice': '成本价',
  'search.table.grossMargin': '销售毛利',
  'search.table.price': '售价',
  'search.table.number': '销售数量',
  'search.table.returnQuantity': '退货数量',
  'search.table.action': '操作',
  'search.table.add': '添加',

  // Validation messages
  'search.validation.required': '此项为必填项',
  'search.validation.belowMinPrice': '低于最低售价',
  'search.validation.belowCostPrice': '低于成本价',

  // External purchase specific columns
  'search.table.lowerLimit': '库存下限',
  'search.table.upperLimit': '库存上限',
  'search.table.saleNum30d': '30天销量',
  'search.table.saleAvgNum': '日均销量',
  'search.table.purchasePrice': '采购价',
  'search.table.purchaseUnitPrice': '采购单价',
  'search.table.purchaseQuantity': '采购数量',
  'search.table.totalInventory': '总库存',
  'search.table.purchaseLowerLimit': '采购库存下限',
  'search.table.purchaseUpperLimit': '采购库存上限',
  'search.placeholder.pleaseInput': '请输入',

  // Sales slip specific columns
  'search.table.orderNo': '销售单号',
  'search.table.createTime': '创建时间',
  'search.table.storeName': '销售门店',
  'search.table.warehouseName': '发货仓库',
  'search.table.saleNum': '销售数量',

  // Sales return specific columns
  'search.table.skuName': '商品名称',
  'search.table.refundAmount': '退款金额',
  'search.table.refundUnitPrice': '退款单价',

  // 通用组
  'general.group.add': '加入通用组',
  'general.group.goods.list': '商品列表',
  'general.group.goods.set': '通用组商品',
  'general.group.set.primary.goods': '设为主商品',
  'general.group.remove': '删除通用组',
  'general.group.name': '通用组名称',
  'general.group.merge.transfer.suggestions': '调货建议按主商品合并',
  'general.group.edit': '编辑通用组',
  'general.group.create': '新增通用组',
};
