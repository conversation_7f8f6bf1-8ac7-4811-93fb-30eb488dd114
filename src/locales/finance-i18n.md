| Key | 中文 | English |
| --- | --- | --- |
| collection.title | 应收管理 | Receivables Management |
| collection.detail | 应收详情 | Receivable Details |
| collection.detailTitle | 应收明细 | Receivable Details |
| collection.totalReceivableAmount | 应收总金额 | Total Receivable Amount |
| collection.onlyShowReceivable | 仅看应收金额大于0 | Only show existing receivables amount > 0 |
| collection.onlyShowRemaining | 仅看应收金额大于0 | Only show existing receivables amount > 0 |
| collection.exportDescription | 零售商应收管理数据导出 | Retailer receivables management data export |
| collection.exportDetailDescription | 零售商应收管理明细数据导出 | Retailer receivables management detail data export |
| collection.columns.customer | 应收对象 | Receivable Object |
| collection.columns.store | 门店 | Store |
| collection.columns.overdueStatus | 逾期状态 | Overdue Status |
| collection.columns.receivableAmount | 应收金额 | Receivable Amount |
| collection.columns.overdueAmount | 逾期金额 | Overdue Amount |
| collection.columns.totalAmount | 总额度 | Total Credit |
| collection.columns.availableAmount | 剩余额度 | Available Credit |
| collection.columns.creditTerms | 账期(天) | Credit Terms (Days) |
| collection.columns.arrearsDay | 欠款时间(天) | Arrears Days |
| collection.columns.remainTerms | 剩余账期(天) | Remaining Terms (Days) |
| collection.columns.businessOrderNo | 业务单号 | Business Order No. |
| collection.columns.businessCompleteTime | 业务完成时间 | Business Complete Time |
| collection.columns.orderAmount | 单据金额 | Order Amount |
| collection.columns.receivedAmount | 已收金额 | Received Amount |
| collection.columns.remainReceivableAmount | 应收金额 | Receivable Amount |
| collection.columns.customerCode | 单位编码 | Unit Code |
| collection.columns.contact | 联系人 | Contact |
| collection.columns.contactPhone | 联系电话 | Contact Phone |
| collection.columns.contactAddress | 联系地址 | Contact Address |
| collection.columns.settleType | 结算类型 | Settle Type |
| collection.columns.currency | 币种 | Currency |
| collection.columns.rate | 汇率 | Rate |
| collection.summary.orderTotal | 单据总额 | Order Total |
| collection.summary.receivedTotal | 已收总额 | Received Total |
| collection.summary.receivableTotal | 应收总额 | Receivable Total |
| collection.summary.overdueTotal | 逾期总额 | Overdue Total |
| receive.title | 收款管理 | Payment Management |
| receive.detail | 收款详情 | Payment Details |
| receive.add | 新增收款 | Add Payment |
| receive.confirm | 确认收款 | Confirm Payment |
| receive.autoAssign | 自动分配 | Auto Assign |
| receive.receivedAmount | 收款金额 | Received Amount |
| receive.currentWriteOff | 本次核销总计 | Current Write-off Total |
| receive.writeOffOrder | 核销订单 | Write-off Orders |
| receive.writeOffAmountMismatch | 本次核销合计需等于收款金额 | Current write-off total must equal received amount |
| receive.noAmountError | 收款金额不能为空 | Received amount cannot be empty |
| receive.negativeAmountError | 收款金额小于等于0，请手动分配核销金额 | Received amount is less than or equal to 0, please manually assign write-off amount |
| receive.noOrderSelectedWarning | 本次收款尚未选择具体的订单，请检查！ | No specific orders selected for this payment, please check! |
| receive.amountMismatchError | 各订单收款总金额与输入收款金额不一致，请修改！ | Total received amount from orders does not match input received amount, please modify! |
| receive.columns.serialNumber | 收款单号 | Payment No. |
| receive.columns.store | 收款门店 | Payment Store |
| receive.columns.receiveTime | 收款时间 | Payment Time |
| receive.columns.customer | 收款对象 | Receiver |
| receive.columns.customerName | 客户 | Customer |
| receive.columns.receivedAccount | 收款账户 | Payment Account |
| receive.columns.receivedAmount | 收款金额 | Received Amount |
| receive.columns.creator | 制单人 | Creator |
| receive.columns.remark | 备注 | Remark |
| receive.columns.receiveType | 收款类型 | Payment Type |
| receive.columns.storeName | 门店名称 | Store Name |
| receive.columns.businessOrderNo | 业务单号 | Business Order No. |
| receive.columns.orderCompleteTime | 订单完成时间 | Order Complete Time |
| receive.columns.transactionCompleteTime | 交易完成时间 | Transaction Complete Time |
| receive.columns.orderAmount | 单据金额 | Order Amount |
| receive.columns.receivedAmountPaid | 已收金额 | Received Amount |
| receive.columns.unreceived | 未收金额 | Unreceived Amount |
| receive.columns.currentWriteOff | 本次核销 | Current Write-off |
| receive.columns.writeOffAmount | 核销金额 | Write-off Amount |
| receive.columns.adjustAmount | 调整金额 | Adjust Amount |
| receive.columns.status | 状态 | Status |
| receive.columns.advanceAmount | 转预收金额 | Advance Amount |
| receive.columns.currency | 币种 | Currency |
| receive.columns.rate | 汇率 | Rate |
| receive.columns.lossAmount | 本位币汇率损益 | Loss Amount |
| receive.columns.image | 图片 | Image |
| receive.columns.writeOffTotal | 本次核销合计 | Write-off Total |
| receive.placeholders.businessOrderNo | 业务单号 | Business Order No. |
| receive.placeholders.enterAmount | 请输入 | Please enter |
| receive.status.cancelled | 已取消 | Cancelled |
| receive.status.aborted | 审核不通过 | Aborted |
| receive.status.draft | 草稿 | Draft |
| receive.status.pending | 待审核 | Pending |
| receive.status.unpayment | 待收款 | Unpayment |
| receive.status.payment | 已收款 | Payment |
| receive.audit.title | 审核 | Audit |
| receive.audit.result | 审核结果 | Audit Result |
| receive.audit.approve | 通过 | Approve |
| receive.audit.reject | 驳回 | Reject |
| receive.audit.opinion | 审核意见 | Audit Opinion |
| receive.audit.reject.reason.placeholder | 请输入不通过原因 | Please enter the reason for rejection |
| receive.cancel.title | 取消收款单 | Cancel Receipt |
| receive.cancel.content | 是否确认取消该收款单？ | Are you sure you want to cancel this receipt? |
| receive.cancel.confirm | 确认取消 | Confirm Cancel |
| receive.cancel.giveup | 放弃 | Give Up |
| receive.multiCurrencyError | 每次收款只能核销一个币种的订单，不同币种订单请分开收款。 | Only orders of one currency can be written off at a time. Please create separate receipts for orders of different currencies. |
| receive.totalReceivable | 应收总额 | Total Receivable |
| receive.receiveStore | 收款门店 | Receiving Store |
| receive.receiveCurrency | 收款币种 | Currency |
| receive.rate | 汇率 | Rate |
| receive.adjustAmount | 调整金额 | Adjustment Amount |
| receive.adjustReason | 调整原因 | Adjustment Reason |
| receive.adjustType.none | 无 | None |
| receive.adjustType.round | 收款抹零 | Rounding |
| receive.adjustType.discount | 收款优惠 | Discount |
| receive.filter.due | 仅看已到期 | Show Due Only |
| receive.filter.overdue | 仅看已超期 | Show Overdue Only |
| receive.receiptImage | 收款图片 | Receipt Image |
| receive.summary.totalReceived | 收款金额 | Received Amount |
| receive.summary.totalAdjust | 调整金额 | Adjustment Amount |
| receive.summary.totalWriteOff | 本次核销合计 | Total Write-off |
| receive.summary.totalAdvance | 转预收金额 | To Advance |
| receive.summary.local.totalReceived | 收款金额(本位币) | Received (Local) |
| receive.summary.local.totalAdjust | 调整金额(本位币) | Adjustment (Local) |
| receive.summary.local.totalWriteOff | 本次核销合计(本位币) | Write-off (Local) |
| receive.summary.local.totalAdvance | 转预收金额(本位币) | To Advance (Local) |
| receive.summary.local.loss | 本位币汇率损益 | Currency Loss |
| payment.title | 应付管理 | Payables Management |
| payment.detail | 应付详情 | Payable Details |
| payment.detailTitle | 应付明细 | Payable Details |
| payment.totalPayableAmount | 应付总金额 | Total Payable Amount |
| payment.onlyShowPayable | 仅看应付金额大于0 | Only show payables amount > 0 |
| payment.onlyShowRemaining | 仅看应付金额大于0 | Only show payables amount > 0 |
| payment.exportDescription | 零售商应付管理数据导出 | Retailer payables management data export |
| payment.exportDetailDescription | 零售商应付管理明细数据导出 | Retailer payables management detail data export |
| payment.columns.supplier | 供应商 | Supplier |
| payment.columns.payableObject | 应付对象 | Payable Object |
| payment.columns.supplierCode | 单位编码 | Unit Code |
| payment.columns.store | 门店 | Store |
| payment.columns.payableAmount | 应付金额 | Payable Amount |
| payment.columns.businessOrderNo | 业务单号 | Business Order No. |
| payment.columns.businessCompleteTime | 业务完成时间 | Business Complete Time |
| payment.columns.orderAmount | 单据金额 | Order Amount |
| payment.columns.paidAmount | 已付金额 | Paid Amount |
| payment.columns.remainPayableAmount | 应付金额 | Payable Amount |
| payment.columns.contact | 联系人 | Contact |
| payment.columns.contactPhone | 联系电话 | Contact Phone |
| payment.columns.contactAddress | 联系地址 | Contact Address |
| payment.summary.orderTotal | 单据总额 | Order Total |
| payment.summary.paidTotal | 已付总额 | Paid Total |
| payment.summary.payableTotal | 应付总额 | Payable Total |
| supplierPayment.title | 供应商付款 | Supplier Payment |
| supplierPayment.detailTitle | 付款详情 | Payment Details |
| supplierPayment.add | 新增付款 | Add Payment |
| supplierPayment.confirm | 确认付款 | Confirm Payment |
| supplierPayment.autoAssign | 自动分配 | Auto Assign |
| supplierPayment.paymentAmount | 付款金额 | Payment Amount |
| supplierPayment.currentWriteOff | 本次核销总计 | Current Write-off Total |
| supplierPayment.writeOffOrder | 核销订单 | Write-off Orders |
| supplierPayment.writeOffAmountMismatch | 本次核销合计需等于付款金额 | Current write-off total must equal payment amount |
| supplierPayment.noAmountError | 付款金额不能为空且必须大于0元！ | Payment amount cannot be empty and must be greater than 0! |
| supplierPayment.negativeAmountError | 付款金额小于等于0，请手动分配核销金额 | Payment amount is less than or equal to 0, please manually assign write-off amount |
| supplierPayment.noOrderSelectedWarning | 本次付款尚未选择具体的单据，请检查！ | No specific documents selected for this payment, please check! |
| supplierPayment.amountMismatchError | 各单据付款总金额与输入付款金额不一致，请修改！ | Total payment amount for each document is inconsistent with input payment amount, please modify! |
| supplierPayment.totalPayable | 应付总额 | Total Payable |
| supplierPayment.paymentCurrency | 付款币种 | Payment Currency |
| supplierPayment.multiCurrencyError | 每次付款只能核销一个币种的订单，不同币种订单请分开付款。 | Only orders of one currency can be written off at a time. Please create separate payments for orders of different currencies. |
| supplierPayment.multiCurrencynotSupportError | 多币种订单不支持自动分配，请手动分配 | Multi-currency orders are not supported for automatic allocation. Please manually assign write-off amount. |
| supplierPayment.summary.paymentAmountLocal | 付款金额（本位币） | Payment Amount (Local) |
| supplierPayment.summary.currentWriteOffLocal | 本次核销合计（本位币） | Current Write-off (Local) |
| supplierPayment.summary.lossAmount | 本位币汇率损益 | Loss Amount |
| supplierPayment.columns.serialNumber | 付款单号 | Payment No. |
| supplierPayment.columns.paymentStore | 付款门店 | Payment Store |
| supplierPayment.columns.paymentTime | 付款时间 | Payment Time |
| supplierPayment.columns.supplier | 付款对象 | Payment Object |
| supplierPayment.columns.supplierName | 付款对象 | Payment Object |
| supplierPayment.columns.paymentAccount | 付款账户 | Payment Account |
| supplierPayment.columns.paymentAmount | 付款金额 | Payment Amount |
| supplierPayment.columns.creator | 制单人 | Creator |
| supplierPayment.columns.remark | 付款备注 | Payment Remark |
| supplierPayment.columns.storeName | 门店名称 | Store Name |
| supplierPayment.columns.businessOrderNo | 业务单号 | Business Order No. |
| supplierPayment.columns.businessCompleteTime | 业务完成时间 | Business Complete Time |
| supplierPayment.columns.orderAmount | 单据金额 | Order Amount |
| supplierPayment.columns.paidAmount | 已付金额 | Paid Amount |
| supplierPayment.columns.unpaidAmount | 未付金额 | Unpaid Amount |
| supplierPayment.columns.currentWriteOff | 本次核销合计 | Current Write-off |
| supplierPayment.columns.writeOffAmount | 核销金额 | Write-off Amount |
| supplierPayment.columns.status | 付款状态 | Payment Status |
| supplierPayment.columns.lossAmount | 本位币汇率损益 | Loss Amount |
| supplierPayment.columns.image | 图片 | Image |
| supplierPayment.columns.paymentPic | 付款图片 | Payment Pic |
| supplierPayment.placeholders.businessOrderNo | 业务单号 | Business Order No. |
| supplierPayment.placeholders.enterAmount | 请输入 | Please enter |
| supplierPayment.detail.columns.storeName | 门店名称 | Store Name |
| supplierPayment.detail.columns.orderNo | 单据号 | Order No. |
| supplierPayment.detail.columns.billDate | 单据日期 | Bill Date |
| supplierPayment.detail.columns.orderAmount | 单据金额 | Order Amount |
| supplierPayment.detail.columns.paymentAmount | 核销金额 | Payment Amount |
| cost.title | 收支管理 | Income & Expense Management |
| cost.income | 收入 | Income |
| cost.expend | 支出 | Expense |
| cost.otherIncome | 其他收入 | Other Income |
| cost.otherExpend | 其他支出 | Other Expense |
| cost.add | 新增 | Add |
| cost.invalidateSuccess | 作废成功 | Invalidated successfully |
| cost.confirmInvalidate | 确认作废吗 | Confirm to invalidate? |
| cost.invalidate | 作废 | Invalidate |
| cost.columns.store | 门店 | Store |
| cost.columns.incomeType | 收入类型 | Income Type |
| cost.columns.expendType | 支出类型 | Expense Type |
| cost.columns.createTime | 制单时间 | Create Time |
| cost.columns.serialNumber | 收支单号 | Transaction No. |
| cost.columns.incomeStore | 收款门店 | Income Store |
| cost.columns.expendStore | 付款门店 | Payment Store |
| cost.columns.incomeAmount | 收款金额 | Income Amount |
| cost.columns.expendAmount | 付款金额 | Payment Amount |
| cost.columns.settlementAccount | 结算账户 | Settlement Account |
| cost.columns.creator | 制单人 | Creator |
| cost.columns.incomeRemark | 收款备注 | Income Remark |
| cost.columns.expendRemark | 付款备注 | Payment Remark |
| cost.columns.incomeNature | 收入性质 | Income Nature |
| cost.columns.expendNature | 支出性质 | Expense Nature |
| cost.columns.incomeObject | 收款对象 | Income Object |
| cost.columns.expendObject | 付款对象 | Payment Object |
| cost.form.incomeStore | 收款门店 | Income Store |
| cost.form.expendStore | 付款门店 | Payment Store |
| cost.form.incomeType | 收入类型 | Income Type |
| cost.form.expendType | 支出类型 | Expense Type |
| cost.form.incomeAmount | 收款金额 | Income Amount |
| cost.form.expendAmount | 付款金额 | Payment Amount |
| cost.form.settlementAccount | 结算账户 | Settlement Account |
| cost.form.incomeRemark | 收款备注 | Income Remark |
| cost.form.expendRemark | 付款备注 | Payment Remark |
| cost.form.yuan | 元 | Yuan |
| cost.form.pic | 图片 | Image |
| cost.status.cancelled | 已作废 | Cancelled |
| cost.status.aborted | 审核不通过 | Aborted |
| cost.status.pending | 待审核 | Pending |
| cost.status.confirmed | 已确认 | Confirmed |
| cost.type.receivable | 应收 | Receiveable |
| cost.type.payable | 应付 | Payable |
| cost.type.income | 收入 | Income |
| cost.type.expend | 支出 | Expend |
| cost.recordType | 记{type} | Record as {type} |
| cost.detailTitle | {type}详情 | {type} Details |
| flow.title | 资金流水 | Capital Flow |
| flow.totalIncome | 总收入 | Total Income |
| flow.totalExpend | 总支出 | Total Expenditure |
| flow.columns.businessOrderNo | 业务单号 | Business Order No. |
| flow.columns.occurTime | 发生时间 | Occurrence Time |
| flow.columns.customerOrSupplier | 客户/供应商/往来单位 | Customer/Supplier/Other Company |
| flow.columns.incomeExpendType | 收支类型 | Income/Expense Type |
| flow.columns.store | 门店 | Store |
| flow.columns.settlementAccount | 结算账户 | Settlement Account |
| flow.columns.income | 收入 | Income |
| flow.columns.expend | 支出 | Expenditure |
| customer.title | 账户管理 | Account Management |
| customer.addAccount | 新增账户 | Add Account |
| customer.editAccount | 编辑账户 | Edit Account |
| customer.confirmDelete | 确认删除吗? | Confirm to delete? |
| customer.columns.accountName | 账户名称 | Account Name |
| customer.columns.bankName | 开户银行 | Bank Name |
| customer.columns.bankCardNumber | 银行卡号 | Bank Card Number |
| customer.columns.belongToStore | 所属门店 | Belongs to Store |
| customer.columns.accountBalance | 账户余额 | Account Balance |
| customer.columns.initialBalance | 期初余额 | Initial Balance |
| customer.columns.currency | 币种 | Currency |
| customer.form.accountName | 账户名称 | Account Name |
| customer.form.bankName | 开户银行 | Bank Name |
| customer.form.bankCardNumber | 银行卡号 | Bank Card Number |
| customer.form.belongToStore | 所属门店 | Belongs to Store |
| customer.form.initialBalance | 期初余额 | Initial Balance |
| customer.form.currency | 币种 | Currency |
| tag.title | 收支类型管理 | Income & Expense Type Management |
| tag.incomeExpenseType | 收支类型 | Income & Expense Type |
| tag.addIncomeExpenseType | 新增收支类型 | Add Income & Expense Type |
| tag.editIncomeExpenseType | 编辑收支类型 | Edit Income & Expense Type |
| tag.columns.incomeExpenseType | 收支类型 | Income & Expense Type |
| tag.columns.incomeDirection | 收入方向 | Income Direction |
| tag.columns.source | 来源 | Source |
| tag.columns.isEnabled | 是否启用 | Is Enabled |
| tag.form.incomeExpenseType | 收支类型 | Income & Expense Type |
| tag.form.incomeExpenseDirection | 收支方向 | Income & Expense Direction |
| tag.form.income | 收入 | Income |
| tag.form.expense | 支出 | Expense |
| tag.form.pleaseInput | 请输入 | Please input |
| currency.title | 币种管理 | Currency Management |
| currency.name | 币种名称 | Currency Name |
| currency.symbol | 币种符号 | Currency Symbol |
| currency.exchangeRate | 汇率 | Exchange Rate |
| currency.exchangeRate.tooltip | 位币金额*汇率=其他币种金额 | Amount of the coin * exchange rate = Amount of other coins |
| currency.isBase | 本位币 | standard coin |
| currency.status | 状态 | Status |
| currency.source | 来源 | Source |
| currency.add | 新增币种 | Add Currency |
| accountFlow.title | 预收明细 | Advance Details |
| accountFlow.totalAdvanceBalance | 总预收余额 | Total Advance Balance |
| accountFlow.columns.bizNo | 业务单号 | Business No. |
| accountFlow.columns.bizTime | 发生时间 | Time |
| accountFlow.columns.customerName | 客户 | Customer |
| accountFlow.columns.bizTypeName | 业务类型 | Business Type |
| accountFlow.columns.storeName | 门店 | Store |
| accountFlow.columns.amount | 发生金额 | Amount |
| accountFlow.columns.accountBalance | 结余金额 | Balance |
| accountFlow.columns.createPerson | 操作人 | Operator |
| accountFlow.bizType.RECEIVED_INCOME | 预收充值 | Advance Top-up |
| accountFlow.bizType.ADVANCE_RECEIVED_IN | 收款使用预收 | Payment using advance |
| accountFlow.bizType.ADVANCE_SALE_OUT | 销售使用预收 | Sales using advance |
| accountFlow.bizType.ADVANCE_REFUND_IN | 退货转预收 | Refund to advance |
| otherRelated.search.info.placeholder | 单位信息/联系人/联系方式 | Unit Info/Contact/Contact Phone |
| otherRelated.columns.companyCode | 单位编码 | Unit Code |
| otherRelated.columns.companyName | 单位名称 | Unit Name |
| otherRelated.columns.shortName | 单位简称 | Unit Short Name |
| otherRelated.columns.abn | ABN | ABN |
| otherRelated.columns.defaultContact | 默认联系人 | Default Contact |
| otherRelated.columns.defaultContactPhone | 默认联系方式 | Default Contact Phone |
| otherRelated.columns.defaultAddress | 默认地址 | Default Address |
| otherRelated.columns.remark | 备注 | Remark |
| otherRelated.columns.status | 状态 | Status |
| otherRelated.columns.operation | 操作 | Operation |
| otherRelated.columns.companyInfo | 单位信息 | Company Info |
| otherRelated.columns.contact | 联系人 | Contact |
| otherRelated.columns.contactPhone | 联系方式 | Contact Phone |
| otherRelated.confirm.enable | 确认启用吗? | Confirm to enable? |
| otherRelated.confirm.disable | 确认禁用吗? | Confirm to disable? |
| otherRelated.detail.title | 单位详情 | Unit Details |
| otherRelated.detail.basicInfo | 单位信息 | Basic Information |
| otherRelated.detail.contactInfo | 联系人信息 | Contact Information |
| otherRelated.detail.addressInfo | 地址信息 | Address Information |
| otherRelated.detail.contact.post | 职务 | Position |
| otherRelated.detail.contact.isDefault | 默认联系人 | Default Contact |
| otherRelated.detail.address.isDefault | 默认地址 | Default Address |
| otherRelated.detail.address.number | 地址{number} | Address {number} |
| otherRelated.form.title.add | 新增其他往来单位 | Add Other Related Unit |
| otherRelated.form.title.edit | 编辑其他往来单位 | Edit Other Related Unit |
| otherRelated.form.basicInfo | 基础信息 | Basic Information |
| otherRelated.form.companyName | 单位名称 | Unit Name |
| otherRelated.form.companyNamePlaceholder | 供应商编码/名称/简称 | Supplier Code/Name/Short Name |
| otherRelated.form.companyCode | 单位编码 | Unit Code |
| otherRelated.form.companyCodePlaceholder | 未输入系统将自动生成 | Will be automatically generated if not entered |
| otherRelated.form.shortName | 单位简称 | Unit Short Name |
| otherRelated.form.abn | ABN | ABN |
| otherRelated.form.remark | 备注 | Remark |
| otherRelated.form.contactInfo | 联系人信息 | Contact Information |
| otherRelated.form.addressInfo | 地址信息 | Address Information |
| otherRelated.form.button.addContact | 新增联系人 | Add Contact |
| otherRelated.form.button.addAddress | 新增地址 | Add Address |
| otherRelated.form.contact.firstName | 姓 | First Name |
| otherRelated.form.contact.lastName | 名 | Last Name |
| otherRelated.form.contact.contactPerson | 联系人 | Contact Person |
| otherRelated.form.contact.phone | 联系方式 | Phone |
| otherRelated.form.contact.post | 职务 | Position |
| otherRelated.form.contact.email | 邮箱 | Email |
| otherRelated.form.contact.remark | 备注 | Remark |
| otherRelated.form.contact.isDefault | 设为默认 | Set as Default |
| otherRelated.form.address.postCode | 邮编 | Post Code |
| otherRelated.form.address.suburb | Suburb | Suburb |
| otherRelated.form.address.state | State | State |
| otherRelated.form.address.detail | 详细地址 | Detail Address |
| otherRelated.form.address.contact | 联系人 | Contact |
| otherRelated.form.address.phone | 联系方式 | Phone |
| otherRelated.form.address.isDefault | 设为默认 | Set as Default |
| bill.detail.title | 账单详情 | Bill Detail |
| bill.detail.billDetail | 账单明细 | Bill Detail |
| bill.detail.columns.orderNo | 业务单号 | Order No |
| bill.detail.columns.billDate | 业务完成时间 | Bill Date |
| bill.detail.columns.storeName | 销售门店 | Store Name |
| bill.detail.columns.orderAmount | 单据金额 | Order Amount |
| bill.detail.columns.receivedAmount | 已收金额 | Received Amount |
| bill.detail.columns.remainReceivableAmount | 应收金额 | Remain Receivable Amount |
| bill.detail.summary.billTotalAmount | 单据总额 | Bill Total Amount |
| bill.detail.summary.billReceivedAmount | 已收总额 | Bill Received Amount |
| bill.detail.summary.remainAmount | 应收总额 | Remain Amount |
| bill.columns.billNo | 账单编号 | Bill No |
| bill.columns.customerName | 客户 | Customer |
| bill.columns.customerId | 客户编码 | Customer Code |
| bill.columns.storeName | 归属门店 | Store |
| bill.columns.billCycle | 账单周期 | Bill Cycle |
| bill.columns.cycleType | 账单类型 | Cycle Type |
| bill.columns.billTotalAmount | 账单金额 | Bill Total Amount |
| bill.columns.billReceivedAmount | 已收金额 | Bill Received Amount |
| bill.columns.remainAmount | 剩余额度 | Remain Amount |
| bill.columns.billDate | 账单日 | Bill Date |
| bill.columns.overdueDate | 超期日 | Overdue Date |
| bill.columns.status | 逾期状态 | Status |
| bill.status.billed | 已出账 | Billed |
| bill.status.overdue | 已逾期 | Overdue |
| bill.status.cleared | 已结清 | Cleared |
