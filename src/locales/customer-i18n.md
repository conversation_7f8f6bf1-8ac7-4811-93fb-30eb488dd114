| Key | 中文 | English |
| --- | --- | --- |
| customerList.position.boss | 老板 | Boss |
| customerList.position.purchase | 采购 | Purchase |
| customerList.position.finance | 财务 | Finance |
| customerList.position.reception | 前台 | Reception |
| customerList.position.repairman | 修理工 | Repairman |
| customerList.deliveryAmountType.free | 免运费 | Free |
| customerList.deliveryAmountType.fixed | 固定运费 | Fixed |
| customerList.deliveryAmountType.bargaining | 议价运费 | Bargaining |
| customerList.createModal.titleAdd | 新增客户 | Add Customer |
| customerList.createModal.titleEdit | 客户编辑 | Edit Customer |
| customerList.button.addCustomer | 新增客户 | Add Customer |
| customerList.button.openMallAccount | 商城账号 | Mall Account |
| customerList.openMallAccount.title | 开通客户商城账号 | Open Customer Mall Account |
| customerList.openMallAccount.mallSecretKey | 商城密钥 | Mall Secret Key |
| customerList.openMallAccount.secretKey | 密钥 | Secret Key |
| customerList.openMallAccount.boundDevice | 已绑设备 | Bound Device |
| customerList.openMallAccount.addSecretKey | 新增密钥 | Add Secret Key |
| customerList.detailModal.title | 客户详情 | Customer Details |
| customerList.table.column.customerSn | 客户编码 | Customer Code |
| customerList.table.column.customerName | 客户名称 | Customer Name |
| customerList.table.column.nickName | 客户简称 | Customer Nickname |
| customerList.table.column.customerTags | 客户标签 | Customer Tags |
| customerList.table.column.storeName | 归属门店 | Store |
| customerList.table.column.salesmanName | 业务员 | Salesperson |
| customerList.table.column.allowCredit | 支持挂账 | Allow Credit |
| customerList.table.column.creditLimit | 挂账额度 | Credit Limit |
| customerList.table.column.creditLimit.used | 已用 | Used |
| customerList.table.column.creditLimit.available | /可用 | /Available |
| customerList.table.column.creditTerms | 账期(天) | Credit Terms (days) |
| customerList.table.column.receivableAmount | 客户应收 | Receivable Amount |
| customerList.table.column.advanceAmount | 客户预收 | Advance Amount |
| customerList.table.column.settleType | 结算类型 | Settle Type |
| customerList.table.column.defaultContactName | 联系人 | Contact |
| customerList.table.column.defaultContactPhone | 联系方式 | Contact Phone |
| customerList.table.column.defaultAddress | 客户地址 | Address |
| customerList.table.column.createTime | 创建时间 | Creation Time |
| customerList.table.column.mallPermission | 已开通商城 | already open mall |
| customerList.table.column.ABN | ABN | ABN |
| customerList.table.column.status | 客户状态 | Customer Status |
| customerList.table.column.universalEmail | 通用邮箱 | Universal Email |
| customerList.table.column.Suburb | 区 | Suburb |
| customerList.table.popconfirm.enableDisable | 确认{operType}吗? | Confirm {operType}? |
| customerList.table.search.customerTags | 客户标签 | Customer Tags |
| customerList.table.search.store | 归属门店 | Store |
| customerList.table.search.customerInfo | 客户信息 | Customer Info |
| customerList.table.search.customerInfo.placeholder | 客户编码/客户名称/客户简称 | Customer Code/Name/Nickname |
| customerList.table.search.contactName | 联系人 | Contact Name |
| customerList.table.search.contactPhone | 联系方式 | Contact Phone |
| customerList.table.search.createTime | 创建时间 | Creation Time |
| customerList.import.taskDesc | 零售商客户导入 | Retail Customer Import |
| customerList.export.taskDesc | 零售商客户导出 | Retail Customer Export |
| customerList.amountHistoryModal.title | 额度调整记录 | Credit Adjustment History |
| customerList.amountHistoryModal.operatorLabel | 操作人 | Operator |
| customerList.amountHistoryModal.operationTypeLabel | 操作类型 | Operation Type |
| customerList.amountHistoryModal.operationContentLabel | 操作内容 | Operation Content |
| customerList.createForm.group.baseInfo | 基本信息 | Basic Info |
| customerList.createForm.group.billingInfo | 开票信息 | Billing Info |
| customerList.createForm.group.settlementInfo | 结算信息 | Settlement Info |
| customerList.createForm.group.contactInfo | 联系人信息 | Contact Info |
| customerList.createForm.group.addressInfo | 地址信息 | Address Info |
| customerList.createForm.allowOnAccount | 允许挂账 | Allow on Account |
| customerList.createForm.onAccountLimit | 挂账额度 | On Account Limit |
| customerList.createForm.customerExpectedAmount | 客户期望额度：${amount} | Customer Expected Amount: ${amount} |
| customerList.createForm.customerExpectedPeriod | 客户期望账期：{period} | Customer Expected Period: {period} |
| customerList.createForm.accountPeriod | 账期 | Account Period |
| customerList.createForm.isMultiCurrency | 多币种客户 | Multi-currency Customer |
| customerList.createForm.isGstExcluded | GST Excluded | GST Excluded |
| customerList.createForm.companyEntity | 公司主体 | Company Entity |
| customerList.createForm.simplifyCustomerId | Simplify 客户 ID | Simplify Customer ID |
| customerList.createForm.label.customerName | 客户名称 | Customer Name |
| customerList.createForm.label.customerSn | 客户编码 | Customer Code |
| customerList.createForm.label.nickName | 客户简称 | Customer Nickname |
| customerList.createForm.label.customerTags | 客户标签 | Customer Tags |
| customerList.createForm.label.store | 归属门店 | Store |
| customerList.createForm.label.salesman | 业务员 | Salesperson |
| customerList.createForm.label.remark | 备注 | Remarks |
| customerList.createForm.label.customerPhoto | 客户照片 | Customer Photo |
| customerList.createForm.label.billingUnit | 开票单位 | Billing Unit |
| customerList.createForm.label.taxNo | 纳税识别号 | Tax ID |
| customerList.createForm.label.bankName | 开户行名称 | Bank Name |
| customerList.createForm.label.accountNo | 开户行账号 | Bank Account |
| customerList.createForm.label.billingAddress | 开票地址 | Billing Address |
| customerList.createForm.label.billingPhone | 开票电话 | Billing Phone |
| customerList.createForm.label.initialReceivable | 期初应收 | Initial Receivable |
| customerList.createForm.label.allowCredit | 允许挂账 | Allow Credit |
| customerList.createForm.label.creditLimit | 挂账额度 | Credit Limit |
| customerList.createForm.label.creditTerms | 结算类型 | Credit Terms |
| customerList.createForm.label.universalEmail | 通用邮箱 | Universal Email |
| customerList.createForm.label.financeEmail | 财务邮箱 | Finance Email |
| customerList.createForm.label.deliverWay | 运费类型 | Deliver Way |
| customerList.createForm.label.sendFinanceEmailFlag | 发送财务邮件 | Send Finance Email Flag |
| customerList.createForm.label.deliveryAmount | 运费 | Delivery Amount |
| customerList.createForm.placeholder.deliveryAmount | 请输入运费 | Please enter delivery amount |
| customerList.createForm.tooltip.creditLimit | 挂账额度 | Credit Limit |
| customerList.createForm.tooltip.creditTerms | 结算类型 | Credit Terms |
| customerList.createForm.button.addContact | 新增联系人 | Add Contact |
| customerList.createForm.button.addAddress | 新增地址 | Add Address |
| customerList.createForm.contactTable.column.isDefaultContact | 默认联系人 | Default Contact |
| customerList.createForm.contactTable.column.contactName | 联系人 | Contact Name |
| customerList.createForm.addressTable.column.isDefaultAddress | 默认地址 | Default Address |
| customerList.createForm.addressTable.column.province | 洲 | State |
| customerList.createForm.addressTable.column.prefecture | 区 | Suburb |
| customerList.createForm.addressTable.column.provinceCityDistrict | 洲/区 | State/Suburb |
| customerList.createForm.addressTable.column.detailAddress | 详细地址 | Detailed Address |
| customerList.createForm.addressTable.column.contactName | 联系人 | Contact Name |
| customerList.createForm.addressTable.column.contactPhone | 联系人方式 | Contact Phone |
| customerList.createForm.addressTable.column.postCode | 邮编 | Post Code |
| customerList.detailForm.status.enabled | 启用 | Enabled |
| customerList.detailForm.status.disabled | 禁用 | Disabled |
| customerList.detailForm.label.customerSn | 客户编码 | Customer Code |
| customerList.detailForm.label.nickName | 客户简称 | Customer Nickname |
| customerList.detailForm.label.customerTags | 客户标签 | Customer Tags |
| customerList.detailForm.label.store | 归属门店 | Store |
| customerList.detailForm.label.salesman | 业务员 | Salesperson |
| customerList.detailForm.label.createTime | 创建时间 | Creation Time |
| customerList.detailForm.label.remark | 客户备注 | Customer Remarks |
| customerList.detailForm.label.source | 客户来源 | Customer Source |
| customerList.detailForm.group.contactInfo | 联系人信息 | Contact Info |
| customerList.detailForm.group.addressInfo | 地址信息 | Address Info |
| customerList.detailForm.group.settlementInfo | 结算信息 | Settlement Info |
| customerList.detailForm.group.billingInfo | 开票信息 | Billing Info |
| customerList.detailForm.tag.defaultContact | 默认联系人 | Default Contact |
| customerList.detailForm.tag.defaultAddress | 默认地址 | Default Address |
| customerList.detailForm.contact.label.position | 职务 | Position |
| customerList.detailForm.contact.label.phone | 联系方式 | Phone |
| customerList.detailForm.contact.label.email | 邮箱 | Email |
| customerList.detailForm.contact.label.remark | 备注 | Remarks |
| customerList.detailForm.contact.label.firstName | 名字 | First Name |
| customerList.detailForm.contact.label.lastName | 姓氏 | Last Name |
| customerList.detailForm.address.label.address | 地址 | Address |
| customerList.detailForm.address.label.phone | 联系方式 | Phone |
| customerList.detailForm.address.label.area | 所在地区 | Area |
| customerList.detailForm.address.label.detailAddress | 详细地址 | Detailed Address |
| customerList.detailForm.address.label.contactName | 联系人 | Contact Name |
| customerList.detailForm.link.viewAmountHistory | 查看额度调整记录 | View Credit Adjustment History |
| customerList.detailForm.settlement.label.allowCredit | 支持挂账 | Allow Credit |
| customerList.detailForm.settlement.label.creditLimit | 挂账额度 | Credit Limit |
| customerList.detailForm.settlement.label.used | 已用额度 | Used Limit |
| customerList.detailForm.settlement.label.frozen | ，冻结额度 | , Frozen Limit |
| customerList.detailForm.settlement.label.available | ，可用 | , Available |
| customerList.detailForm.settlement.label.creditTerms | 结算类型 | Credit Terms |
| customerList.detailForm.settlement.label.usedAndFrozenCredit | 已用额度： {usedAmount}, 冻结额度： {freezeAmount} | Used {usedAmount}, Frozen {freezeAmount} |
| customerList.detailForm.settlement.label.remainingCredit | (已用 {usedAmount}，冻结 {freezeAmount}，可用 {availableAmount}) | (Used {usedAmount}, Frozen {freezeAmount}, Available {availableAmount}) |
| customerList.detailForm.billing.label.billingUnit | 开票单位： | Billing Unit: |
| customerList.detailForm.billing.label.taxNo | 纳税识别号： | Tax ID: |
| customerList.detailForm.billing.label.bankName | 开户行名称： | Bank Name: |
| customerList.detailForm.billing.label.accountNo | 开户行账号： | Bank Account: |
| customerList.detailForm.billing.label.phone | 开票电话： | Billing Phone: |
| customerList.detailForm.billing.label.address | 开票地址： | Billing Address: |
| customerList.contactTable.column.phone | 联系方式 | Phone |
| customerList.contactTable.column.position | 职务 | Position |
| customerList.contactTable.column.email | 邮箱 | Email |
| customerList.contactTable.column.remark | 备注 | Remarks |
| customerList.contactTable.column.firstName | 名字 | First Name: |
| customerList.contactTable.column.lastName | 姓氏 | Last Name: |
| customerProperty.label.customerTag | 客户标签 | Customer Tag |
| customerProperty.label.tag | 标签 | Tag |
| customerProperty.createModal.titleAddTag | 新增标签 | Add Tag |
| customerProperty.createModal.titleAddTagSuffix | 标签添加 | Tag Addition |
| customerProperty.tab.customerTag | 客户标签 | Customer Tag |
| customerProperty.button.addTag | 新增标签 | Add Tag |
| customerProperty.tagTable.column.tagName | 客户标签 | Customer Tag |
| customerProperty.tagTable.column.source | 来源 | Source |
| customerProperty.tagTable.search.tagName | 客户标签 | Customer Tag |
| customerProperty.priceLevel.column.name | 价格级别 | Price Level |
| customerProperty.priceLevel.column.source | 来源 | Source |
