import common from "./en-US/common";
import customer from "./en-US/customer";
import finance from "./en-US/finance";
import goods from "./en-US/goods";
import home from "./en-US/home";
import personnel from "./en-US/personnel";
import purchase from "./en-US/purchase";
import report from "./en-US/report";
import sales from "./en-US/sales";
import shop from "./en-US/shop";
import stocks from "./en-US/stocks";
import system from "./en-US/system";
import topic from "./en-US/topic";

export default {
    layout: {
        tabActions: {
            refresh: 'Refresh',
            close: 'Close',
            closeOther: 'Close Other',
        },
        sidebar: {
            fold: 'Fold Sidebar',
        },
        button: {
            logout: 'Logout',
        },
    },
    login: {
        title: 'GRIPX Smart Auto Parts System',
        phoneNumber: 'Phone Number',
        phoneRequired: 'Phone number is required',
        password: 'Password',
        passwordRequired: 'Password is required',
        captchaLogin: 'Captcha Login',
        passwordLogin: 'Password Login',
        forgotPassword: 'Forgot Password',
        captchaPlaceholder: 'Please enter captcha',
        captchaRequired: 'Please enter captcha!',
        sendCaptcha: 'Send Captcha',
        captchaError: 'Failed to get captcha',
        captchaSent: 'Captcha sent successfully!',
        loginFailed: 'Login failed, please try again!',
        resetSuccess: 'Reset successful!',
        setNewPassword: 'Please set new password',
        setInitialPassword: 'Please set initial password',
        enterPhoneNumber: 'Please enter phone number',
        phoneNumberRequired: 'Phone number is required',
        enterPassword: 'Please enter login password',
        passwordRule: '8-12 characters with letters, numbers, symbols, must contain 3 types',
        passwordRuleError: 'Password does not meet requirements',
        confirmPassword: 'Please enter confirm password',
        confirmPasswordRequired: 'Confirm password is required',
        passwordMismatch: 'Passwords do not match!',
    },
    common,
    stocks,
    goods,
    system,
    topic,
    customer,
    finance,
    ...home,
    purchase,
    report,
    sales,
    ...shop,
    ...personnel
}
