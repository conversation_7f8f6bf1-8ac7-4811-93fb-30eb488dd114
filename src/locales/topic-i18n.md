| key | 中文 | 英文 |
|---|---|---|
| decoration.title.pageSetting | 页面设置 | Page Settings |
| decoration.title.components | 基础组件 | Basic Components |
| decoration.title.pageLayout | 页面布局 | Page Layout |
| decoration.label.pageTitle | 页面标题 | Page Title |
| decoration.placeholder.pageTitle | 请输入页面标题 | Please enter page title |
| decoration.label.isHome | 设置为首页 | Set as Homepage |
| decoration.label.pageRemark | 页面备注 | Page Remark |
| decoration.button.exit | 退出编辑 | Exit Editing |
| decoration.icon.hotspot | 图片热区 | Image Hotspot |
| decoration.icon.imageAd | 图片广告 | Image Ad |
| decoration.icon.moduleTitle | 模块标题 | Module Title |
| decoration.icon.productList | 商品列表 | Product List |
| decoration.icon.coupon | 优惠券 | Coupon |
| decoration.icon.container | 容器 | Container |
| decoration.icon.divider | 分割线 | Divider |
| decoration.tips.dragComponent | 从左侧拖拽组件到画布区域 | Drag components from the left to the canvas area |
| decoration.tips.clickComponent | 点击组件可编辑属性 | Click on a component to edit its properties |
| decoration.productList.mock.itemName | Here will display product name, up to 2 lines | Here will display product name, up to 2 lines |
| decoration.divider.padding.horizontal | 水平间距 | Horizontal Padding |
| decoration.divider.padding.vertical | 垂直间距 | Vertical Padding |
| decoration.imageAd.empty | 点击编辑图片广告 | Click to edit image ad |
| decoration.imageAd.upload | 添加图片 | Add Image |
| decoration.imageAd.reUpload | 更换图片 | Replace Image |
| decoration.imageAd.upload.tips | 上传多张图时轮播展示 | Upload multiple images to display in carousel |
| decoration.moduleTitle.titleContent | 标题内容 | Title Content |
| decoration.moduleTitle.titleContent.placeholder | 模块标题 | Module Title |
| decoration.moduleTitle.showSubtitle | 显示副标题 | Show Subtitle |
| decoration.moduleTitle.subtitleContent.placeholder | 所有商品免费配送 | free shipping |
| decoration.moduleTitle.subtitleContent | 副标题内容 | Subtitle Content |
| decoration.moduleTitle.showMore | 显示更多 | Show More |
| decoration.moduleTitle.showMore.placeholder | 查看更多 > | View All |
| decoration.moduleTitle.promptText | 提示文字 | Prompt Text |
| decoration.moduleTitle.linkSetting | 链接设置 | Link Settings |
| decoration.linkSetting.type.none | 无链接 | No Link |
| decoration.linkSetting.type.product | 商品 | Product |
| decoration.linkSetting.type.topic | 专题页 | Topic Page |
| decoration.linkSetting.type.coupon | 优惠券 | Coupon |
| decoration.linkSetting.type.activity | 活动 | Activity |
| decoration.linkSetting.type.keyword | 关键词 | Keyword |
| decoration.linkSetting.type.placeholder | 请选择跳转类型 | Please select jump type |
| decoration.linkSetting.keyword.placeholder | 请输入关键词 | Please enter keyword |
| decoration.linkSetting.select | 选择{type} | Select {type} |
| decoration.imageHotspot.placeholder | 点击编辑图片热区 | Click to edit image hotspot |
| decoration.imageHotspot.suggestion | 建议图片宽度1280px，高度不限。 | Suggested image width 1280px, height unlimited. |
| decoration.imageHotspot.instructions | 在下方图片区域拖拽创建，或点击按钮添加。 | Drag and create in the image area below, or click the button to add. |
| decoration.imageHotspot.upload | 上传图片 | Upload Image |
| decoration.imageHotspot.reupload | 更换图片 | Replace Image |
| decoration.imageHotspot.upload.placeholder | 请先上传图片 | Please upload an image first |
| decoration.imageHotspot.hotspotsLinks | 热区链接 | Hotspot Links |
| decoration.imageHotspot.hotspots | 热区 | Hotspots |
| decoration.imageHotspot.deleteHotspot | 删除热区 | Delete Hotspot |
| decoration.imageHotspot.addHotspot | 添加热区 | Add Hotspot |
| decoration.coupon | 优惠券 | Coupon |
| decoration.coupon.management | 优惠券管理 | Coupon Management |
| decoration.coupon.select | 选择优惠券 | Select Coupon |
| decoration.coupon.claim | 领取 | Claim |
| decoration.coupon.threshold | 满${threshold}可用 | ${threshold} to use |
| decoration.coupon.noCoupons | 点击添加优惠券 | click to add coupon |
| decoration.productList.settings.conditionsTitle | 以下条件至少设置一条 | At least one of the following conditions must be set |
| decoration.productList.settings.productTag | 商品标签 | Product Tag |
| decoration.productList.settings.selectTag | 请选择标签 | Please select tags |
| decoration.productList.settings.onlyCampaignProducts | 仅活动商品 | Campaign Products Only |
| decoration.productList.settings.displayCount | 显示数量 | Display Count |
| decoration.productList.settings.display | 显示 | Display |
| decoration.productList.settings.displayAll | 显示全部 | Display All |
| decoration.productList.settings.countPlaceholder | 数量 | Count |
| decoration.productList.settings.selectProduct | 选择商品 | Select Products |
| decoration.productList.settings.selectedCount | 已选 {count} 个商品 | Selected {count} products |
| decoration.productList.settings.productSource | 商品来源 | Product Source |
| decoration.productList.settings.specifiedProducts | 指定商品 | Specified Products |
| decoration.productList.settings.conditionalProducts | 指定条件商品 | Conditional Products |
| decoration.productList.settings.listStyle | 列表样式 | List Style |
| decoration.productList.settings.onePerRow | 一行一个 | One Per Row |
| decoration.productList.settings.twoPerRow | 一行二个 | Two Per Row |
| decoration.productList.settings.horizontalScroll | 横向滚动 | Horizontal Scroll |
| decoration.productList.settings.type | 类型 | Type |
| decoration.productList.settings.product | 商品 | Product |
| decoration.productList.settings.productGroup | 商品分组 | Product Group |
| decoration.productList.settings.newGroup | 新分组 {number} | New Group {number} |
| decoration.productList.settings.group | 分组 | Group |
| decoration.productList.settings.filter.onlyActive | 仅查看活动商品 | only view active products |
