| key | 中文 | 英文 |
|---|---|---|
| shop.common.button.addProduct | 添加商品 | Add Product |
| shop.common.button.addGift | 添加赠品 | Add Gift |
| shop.common.message.needAddProduct | 请至少添加一个商品 | Please add at least one product |
| shop.common.message.needAddGift | 请至少添加一个赠品 | Please add at least one gift |
| shop.topic.topicStatus.draft | 草稿 | draft |
| shop.topic.topicStatus.published | 已发布 | published |
| shop.topic.topicPageSource.topic | 专题页 | topic |
| shop.topic.topicPageSource.app | app首页 | app home |
| shop.topic.list.topicPageId | 专题页ID | Topic Page ID |
| shop.topic.list.topicPageName | 专题页名称 | Topic Page Name |
| shop.topic.list.pageStatus | 页面状态 | Page Status |
| shop.topic.list.pageSource | 页面类型 | Page Type |
| shop.topic.list.updater | 更新人 | Updater |
| shop.topic.list.updateTime | 更新时间 | Update Time |
| shop.topic.list.pageRemark | 页面备注 | Page Remark |
| shop.topic.list.copy | 复制 | Copy |
| shop.topic.list.publish | 发布 | Publish |
| shop.topic.list.unpublish | 取消发布 | Unpublish |
| shop.topic.list.operation.decoration | 装修 | Decoration |
| shop.activity.list.activityId | 活动Id | Activity ID |
| shop.activity.list.activityName | 活动名称 | Activity Name |
| shop.activity.list.activityType | 活动类型 | Activity Type |
| shop.activity.list.activityStatus | 活动状态 | Activity Status |
| shop.activity.list.activityStep | 进行状态 | Progress Status |
| shop.activity.list.activityStartTime | 活动开始时间 | Activity Start Time |
| shop.activity.list.activityEndTime | 活动结束时间 | Activity End Time |
| shop.activity.list.updater | 更新人 | Updater |
| shop.activity.list.updateTime | 更新时间 | Update Time |
| shop.activity.list.activityRemark | 活动备注 | Activity Remark |
| shop.activity.activityStatus.draft | 草稿 | Draft |
| shop.activity.activityStatus.takeEffect | 已生效 | Take Effect |
| shop.activity.activityStatus.invalid | 失效 | Invalid |
| shop.activity.activityType.everyFullGift | 每满赠 | Every Full Gift |
| shop.activity.activityType.ladderFullGift | 阶梯满赠 | Ladder Full Gift |
| shop.activity.activityType.buyGiftSelf | 买赠活动 | Buy Gift Self |
| shop.activity.activityType.specialPrice | 限时特价 | Special Price |
| shop.activity.activityType.suiteItem | 组合套餐 | Suite Item |
| shop.activity.activityProgress.notStartActivity | 未开始 | Not Start |
| shop.activity.activityProgress.inActivity | 进行中 | In Activity |
| shop.activity.activityProgress.endActivity | 已结束 | End Activity |
| shop.activity.list.operation.enable | 生效 | Valid |
| shop.activity.list.operation.disable | 失效 | Invalid |
| shop.activity.title.basicInfo | 基础信息 | Basic Information |
| shop.activity.title.applicableProducts | 活动商品 | Applicable Products |
| shop.activity.title.giftInfo | 赠品设置 | Gift Information |
| shop.activity.name | 活动名称 | Activity Name |
| shop.activity.description | 活动说明 | Activity Description |
| shop.activity.descriptionPlaceholder | 支持200个字符输入，将展示在活动页面 | Supports up to 200 characters, displayed on activity page |
| shop.activity.period | 活动周期 | Activity Period |
| shop.activity.startTime | 开始日期 | Start Date |
| shop.activity.endTime | 结束日期 | End Date |
| shop.activity.audience | 活动人群 | Audience |
| shop.activity.audienceUnlimited | 不限 | Unlimited |
| shop.activity.audienceSpecificTags | 指定标签客户 | Specific Tag Customers |
| shop.activity.image | 活动图 | Activity Image |
| shop.activity.uploadImage | 上传图片 | Upload Image |
| shop.activity.supportCouponOverlay | 支持优惠券叠加 | Support Coupon Overlay |
| shop.activity.supportAccountPayment | 支持挂账支付 | Support Account Payment |
| shop.activity.allowOrderLowStock | 库存不足可下单 | Allow Order if Stock Insufficient |
| shop.activity.remarks | 活动备注 | Activity Remarks |
| shop.activity.remarksPlaceholder | 支持200个字符输入，仅用于内部展示 | Supports up to 200 characters, for internal use only |
| shop.activity.tagRequired | 请选择指定客户标签 | Please select specific customer tags |
| shop.activity.products.activityPrice | 活动价 | Activity Price |
| shop.activity.products.minOrderQuantityPerCustomer | 单客户起订数 | Min Order Quantity Per Customer |
| shop.activity.products.maxPurchaseQuantityPerCustomer | 单客户限购数 | Max Purchase Quantity Per Customer |
| shop.activity.products.totalPurchaseQuantity | 总限购数 | Total Purchase Quantity |
| shop.activity.products.sortOrder | 排序 | Sort |
| shop.activity.products.purchasedQuantity | 购本品数 | Purchased Quantity |
| shop.activity.products.giftQuantity | 赠本品数 | Gift Quantity |
| shop.activity.products.totalMaxPurchaseQuantity | 商品总限购数 | Total Max Purchase Quantity |
| shop.activity.products.totalMaxGiftQuantity | 赠品总限购数 | Total Max Gift Quantity |
| shop.activity.products.type.productCombination | 商品组合 | Product Combination |
| shop.activity.products.type.discountPackage | 优惠套餐 | Discount Package |
| shop.activity.products.promotionPrice | 活动价 | Activity Price |
| shop.activity.products.quantity | 数量 | Quantity |
| shop.activity.products.limit | 购买限制 | Limit |
| shop.activity.products.packageActivityPrice | 套餐内单价 | Package Activity Price |
| shop.activity.products.minOrderPackageNum | 单客户起订套数 | Min Order Package Num |
| shop.activity.products.singleCustomerLimitPackageNum | 单客户限购套数 | Single Customer Limit Package Num |
| shop.activity.products.totalLimitPackageNum | 商品总限购套数 | Total Limit Package Num |
| shop.activity.products.totalPurchasePrice | 单买合计 | Total Purchase Price |
| shop.activity.products.packageTotalPrice | 套餐总价 | Package Total Price |
| shop.activity.products.discountPrice | 优惠价格 | Discount Price |
| shop.activity.products.required | 此项为必填项 | This field is required |
| shop.activity.products.activityPriceRequired | 活动价为必填项 | Activity price is required |
| shop.activity.products.numberMinZero | 必须为大于或等于0的数字 | Must be greater than or equal to 0 |
| shop.activity.products.numberPositive | 必须为大于0的数字 | Must be greater than 0 |
| shop.activity.products.taskDesc.specialPrice | 限时特价活动商品导入 | special price activity product import |
| shop.activity.products.taskDesc.everyFullGift | 阶梯满赠/每满赠活动商品导入 | every full gift activity product import |
| shop.activity.products.taskDesc.ladderFullGift | 阶梯满赠活动商品导入 | ladder full gift / per full gift activity product import |
| shop.activity.products.taskDesc.buyGiftSelf | 买赠活动商品导入 | buy gift self activity product import |
| shop.activity.products.taskDesc.suiteItem | 组合套餐活动商品导入 | suite item activity product import |
| shop.activity.giftInfo.ladderMin | 至少需要一个阶梯 | At least one tier is required |
| shop.activity.giftInfo.title | 赠品设置 | Gift Settings |
| shop.activity.giftInfo.giftRule | 赠送规则 | Gift Rule |
| shop.activity.giftInfo.perFull | 每满赠 | Per Full |
| shop.activity.giftInfo.tieredGifting | 阶梯赠 | Tiered Gifting |
| shop.activity.giftInfo.giftCondition | 赠送条件 | Gift Condition |
| shop.activity.giftInfo.fullItems | 满件 | Full Items |
| shop.activity.giftInfo.fullAmount | 满元 | Full Amount |
| shop.activity.giftInfo.conditionUnitItems | 件 | Items |
| shop.activity.giftInfo.conditionUnitAmount | 元 | Yuan |
| shop.activity.giftInfo.addGift | 添加赠品 | Add Gift |
| shop.activity.giftInfo.giftQuantity | 赠送数量 | Gift Quantity |
| shop.activity.giftInfo.totalMaxBuyQuantity | 总限购数 | Total Max Buy Quantity |
| shop.activity.giftInfo.ladder | 阶梯 | Tier |
| shop.activity.giftInfo.addLadder | 添加阶梯 | Add Tier |
| shop.activity.giftInfo.enableAmountRequired | 请输入条件金额/数量 | Please enter condition amount/quantity |
| shop.activity.giftInfo.giftQuantityRequired | 请输入赠送数量 | Please enter gift quantity |
| shop.activity.giftInfo.maxBuyQuantityRequired | 请输入总限购数量 | Please enter total max buy quantity |
| shop.activity.giftInfo.giftQuantityMin | 赠送数量不能小于1 | Gift quantity cannot be less than 1 |
| shop.activity.giftInfo.maxBuyQuantityMin | 总限购数量不能小于1 | Total max buy quantity cannot be less than 1 |
| shop.activity.giftInfo.giftItemsRequired | 每个阶梯至少添加一件赠品 | Each tier must have at least one gift item |
| shop.coupon.list.button.batchSend | 批量发券 | Batch Send Coupon |
| shop.coupon.list.batchSend.methodChoose | 选择客户 | Choose Customer |
| shop.coupon.list.batchSend.methodInput | 手动输入 | Manual Input |
| shop.coupon.list.batchSend.methodInput.placeholder | 请输入客户id，并用英文逗号隔开，最多输入20个客户id | Please enter customer id, separated by commas, up to 20 customer ids |
| shop.coupon.list.couponId | 优惠券ID | Coupon ID |
| shop.coupon.list.couponName | 优惠券名称 | Coupon Name |
| shop.coupon.list.startingAmount | 起用金额 | Starting Amount |
| shop.coupon.list.couponAmount | 优惠券金额 | Coupon Amount |
| shop.coupon.list.totalQuantity | 总数量 | Total Quantity |
| shop.coupon.list.remainingQuantity | 剩余数量 | Remaining Quantity |
| shop.coupon.list.validityPeriod | 有效期 | Validity Period |
| shop.coupon.list.status | 状态 | Status |
| shop.coupon.list.updater | 更新人 | Updater |
| shop.coupon.list.updateTime | 更新时间 | Update Time |
| shop.coupon.list.effectiveTime | 生效时间 | Effective Time |
| shop.coupon.list.expiryTime | 失效时间 | Expiry Time |
| shop.coupon.list.operation.valid | 生效 | Valid |
| shop.coupon.list.operation.invalid | 失效 | Invalid |
| shop.coupon.couponStatus.wait | 待生效 | Wait |
| shop.coupon.couponStatus.active | 生效中 | Active |
| shop.coupon.couponStatus.invalid | 失效 | Invalid |
| shop.couponRecord.list.used | 已使用 | Used |
| shop.couponRecord.list.valid | 已领取 | Valid |
| shop.couponRecord.list.expired | 已过期 | Expired |
| shop.couponRecord.list.invalid | 已作废 | Invalid |
| shop.couponRecord.list.notUsed | 未使用 | Not Used |
| shop.couponRecord.list.operation.invalid | 作废 | Invalid |
| shop.couponRecord.list.operation.batchInvalid | 批量作废 | Batch Invalid |
| shop.couponRecord.message.select.notUsed | 请选择未使用的优惠券 | Please select unused coupons |
| shop.couponRecord.confirm.batchInvalid | 确定要批量作废选中的 {count} 张优惠券吗？ | Are you sure you want to batch invalidate the selected {count} coupons? |
| shop.couponRecord.list.recordId | 记录ID | Record ID |
| shop.couponRecord.list.couponId | 优惠券ID | Coupon ID |
| shop.couponRecord.list.couponName | 优惠券名称 | Coupon Name |
| shop.couponRecord.list.couponValidity | 优惠券有效期 | Coupon Validity |
| shop.couponRecord.list.customerCode | 客户编码 | Customer Code |
| shop.couponRecord.list.customerName | 客户名称 | Customer Name |
| shop.couponRecord.list.status | 状态 | Status |
| shop.couponRecord.list.issuerReceiver | 发放/领取人 | Issuer/Receiver |
| shop.couponRecord.list.receiveTime | 领取时间 | Receive Time |
| shop.couponRecord.list.useTime | 使用时间 | Use Time |
| shop.couponRecord.list.orderNumber | 订单号 | Order Number |
| shop.coupon.basicInfo | 基础信息 | Basic Information |
| shop.coupon.applicableProducts | 适用商品 | Applicable Products |
| shop.coupon.couponName | 优惠券名称 | Coupon Name |
| shop.coupon.totalStock | 发放总数 | Total Stock |
| shop.coupon.singleAccountNum | 单客户领取上限 | Claim Limit |
| shop.coupon.singleAccountNum.addonBefore | 单个账号 | Per Account |
| shop.coupon.singleAccountNum.addonAfter | 个 | Each |
| shop.coupon.discountAmount | 优惠金额 | Discount Amount |
| shop.coupon.enableAmount | 起用金额 | Enable Amount |
| shop.coupon.validityType | 有效期类型 | Validity Type |
| shop.coupon.validityTypeAbsolute | 起止时间 | Absolute Time |
| shop.coupon.validityTypeRelative | 有效期 | Relative Time |
| shop.coupon.validityBeginTime | 生效时间 | Effective Time |
| shop.coupon.validityEndTime | 失效时间 | Expiry Time |
| shop.coupon.validityDays | 有效期 | Validity (Days) |
| shop.coupon.validityDaysPlaceholder | 请输入天数 | Please enter days |
| shop.coupon.validityDaysPlaceholder.addonBefore | 领取后 | Expire in |
| shop.coupon.validityDaysPlaceholder.addonAfter | 天有效 | Days |
| shop.coupon.useDesc | 使用说明 | Usage Description |
| shop.coupon.useDescPlaceholder | 支持200个字符输入，将展示给客户 | Supports up to 200 characters, displayed to customers |
| shop.coupon.remark | 活动备注 | Activity Remarks |
| shop.coupon.remarkPlaceholder | 支持200个字符输入，仅用于内部展示 | Supports up to 200 characters, for internal use only |
| shop.coupon.validity.scopeBrandCategoryListRequired | 请至少选择一个品牌或品类 | Please select at least one brand or category |
| shop.coupon.goodsRange | 商品范围 | Product Scope |
| shop.coupon.goodsRangeSpecific | 指定商品 | Specific Products |
| shop.coupon.goodsRangeGeneral | 通用 | General |
| shop.coupon.productSettings | 商品设置 | Product Settings |
| shop.coupon.itemScopeTypeSpecific | 指定商品 | Specific Products |
| shop.coupon.itemScopeTypeBrandCategory | 指定品牌品类 | Specific Brands & Categories |
| shop.coupon.minAmountValidation | 起用金额不能小于0 | Enable amount cannot be less than 0 |
| shop.coupon.minStockValidation | 发放总数不能小于0 | Total stock cannot be less than 0 |
| shop.coupon.maxDaysValidation | 有效期天数不能超过365天 | Validity days cannot exceed 365 days |
| shop.coupon.minDaysValidation | 有效期天数不能小于1天 | Validity days cannot be less than 1 day |
| shop.coupon.dateRangeValidation | 请选择生效和失效时间 | Please select start and end time |
| shop.category.add | 新增一级分类 | Create Top-level Category |
| shop.category.updateSort | 更新排序 | Save Sorting  |
| shop.category.expandAll | 全部展开 | Expand All |
| shop.category.collapseAll | 全部收起 | Collapse All |
| shop.category.categoryName | 分类名称 | Category Name |
| shop.category.jumpType | 跳转类型 | Redirect Type |
| shop.category.jumpContent | 跳转内容 | Redirect Content |
| shop.category.status | 状态 | Status |
| shop.category.sort | 排序 | Sort |
| shop.category.opt | 操作 | Operation |
| shop.category.add.child | 添加子分类 | Add Subcategory |
| shop.category.parent | 上级分类 | Parent Category |
| shop.category.image | 分类图片 | Category Image |
| shop.category.updateImage | 上传图片 | Upload Image |
| shop.category.addNew | 新增分类 | Add Category |
| shop.category.edit | 编辑分类 | Edit Category |
| shop.category.keyword | 关键词 | Keywords |
| shop.category.show | 显示 | Visible |
| shop.category.hide | 隐藏 | Hidden |
