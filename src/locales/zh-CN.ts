import common from "./zh-CN/common";
import customer from "./zh-CN/customer";
import finance from "./zh-CN/finance";
import goods from "./zh-CN/goods";
import home from "./zh-CN/home";
import personnel from "./zh-CN/personnel";
import purchase from "./zh-CN/purchase";
import report from "./zh-CN/report";
import sales from "./zh-CN/sales";
import shop from "./zh-CN/shop";
import stocks from "./zh-CN/stocks";
import system from "./zh-CN/system";
import topic from "./zh-CN/topic";


export default {
    layout: {
        tabActions: {
            refresh: '刷新',
            close: '关闭',
            closeOther: '关闭其他',
        },
        sidebar: {
            fold: '收起侧边栏',
        },
        button: {
            logout: '退出登录',
        },
    },
    login: {
        title: 'GRIPX智慧汽配系统',
        phoneNumber: '手机号',
        phoneRequired: '手机号必填',
        password: '密码',
        passwordRequired: '密码必填',
        captchaLogin: '验证码登录',
        passwordLogin: '密码登录',
        forgotPassword: '忘记密码',
        captchaPlaceholder: '请输入验证码',
        captchaRequired: '请输入验证码！',
        sendCaptcha: '发送验证码',
        captchaError: '获取验证码错误',
        captchaSent: '验证码发送成功!',
        loginFailed: '登录失败，请重试！',
        resetSuccess: '重置成功！',
        setNewPassword: '请设置新密码',
        setInitialPassword: '请设置初始密码',
        enterPhoneNumber: '请输入手机号',
        phoneNumberRequired: '手机号码必填',
        enterPassword: '请输入登录密码',
        passwordRule: '8~12位字母,数字,符号,必须包含3种类型',
        passwordRuleError: '密码不符合规则',
        confirmPassword: '请输入确认密码',
        confirmPasswordRequired: '确认密码必填',
        passwordMismatch: '两次密码不一致!',
    },
    common,
    stocks,
    goods,
    customer,
    finance,
    ...home,
    purchase,
    sales,
    system,
    report,
    topic,
    ...shop,
    ...personnel
};
