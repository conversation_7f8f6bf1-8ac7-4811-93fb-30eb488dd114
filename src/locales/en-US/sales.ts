export default {
  order: {
    list: {
      // Table column titles
      orderNo: 'Order No.',
      customerName: 'Customer Name',
      orderTime: 'Order Time',
      orderAmount: 'Order Amount',
      grossProfit: 'Gross Profit',
      orderStatus: 'Order Status',
      settlementAccount: 'Settlement Account',
      settlementStatus: 'Settlement Status',
      paymentStatus: 'Payment Status',
      payTime: 'Payment Time',
      outboundTime: 'Outbound Time',
      finishTime: 'Finish Time',
      productInfo: 'Product Info',
      salesStore: 'Sales Store',
      deliveryWarehouse: 'Delivery Warehouse',
      creator: 'Creator',

      storePC: 'PC工作台',
      storeH5: '移动工作台',
      ecommerce: '电商',

      // Search placeholders
      productSearchPlaceholder: 'Product Code/Product Name/OE/Brand Part Number',

      // Button texts
      addSales: 'Add Sales',
      import: 'Import',

      // Action buttons
      edit: 'Edit',
      withdraw: 'Withdraw',
      void: 'Void',

      // Confirmation messages
      confirmWithdraw: 'Confirm withdrawal?',
      confirmVoid: 'Are you sure to void this order?',

      // Success messages
      withdrawSuccess: 'Withdrawal successful',
      voidSuccess: 'Void successful',

      // Tab labels
      all: 'All',

      // Summary texts
      totalOrderCount: 'Total Orders',
      totalSalesAmount: 'Total Sales Amount',
      totalGrossProfit: 'Total Gross Profit',

      // Account
      creditAccount: 'Credit Account',

      // Order status
      status: {
        draft: 'Draft',
        waitToOutbound: 'Pending Outbound',
        outboundFinish: 'Outbound Complete',
        tradeSuccess: 'Completed',
        tradeClose: 'Voided',
      },

      // Payment status
      payStatus: {
        waitToPay: 'Pending Settlement',
        partPay: 'Partial Settlement',
        allPay: 'Settled',
        paying: 'Settling',
      },

      // Receipt payment status
      receiptPaymentStatus: {
        unPay: 'Unpaid',
        partPay: 'Partial Payment',
        allPay: 'Paid',
      },

      // Receipt status
      receiptStatus: {
        notInvoiced: 'Not Invoiced',
        invoiced: 'Invoiced',
      },
    },

    detail: {
      // Basic information
      customerDetail: 'Customer Details',
      viewCustomerDetail: 'View Customer Details',
      customerName: 'Customer Name',
      orderStatus: 'Order Status',
      orderAmount: 'Order Amount',
      settlementMethod: 'Settlement Method',
      salesStore: 'Sales Store',
      belongingStoreName: '业绩归属门店',
      deliveryWarehouse: 'Delivery Warehouse',
      orderTime: 'Order Time',
      estimatedDeliveryTime: 'Estimated Delivery Time',
      creator: 'Creator',

      orderRemark: 'Order Remark',
      urgency: 'Urgency',

      // Action buttons
      edit: 'Edit',
      withdraw: 'Withdraw',
      audit: 'Audit',
      void: 'Void',
      print: 'Print',
      receive: '客户收款',
      confirmSettlement: 'Confirm Settlement',
      oneClickOutbound: 'One-Click Outbound',

      // Confirmation messages
      confirmWithdraw: 'Are you sure to withdraw?',
      confirmVoid: 'Are you sure to void this order?',
      confirmOneClickOutbound: 'Are you sure to proceed with one-click outbound?',

      // Success messages
      withdrawSuccess: 'Withdrawal successful',
      voidSuccess: 'Void successful',
      outboundSuccess: 'Outbound successful',
      settlementSuccess: 'Settlement successful',

      // Goods details
      goodsDetail: 'Goods Details',
      productCode: 'Product Code',
      productName: 'Product Name',
      oeNumber: 'OE Number',
      brandPartNumber: 'Brand Part Number',
      brand: 'Brand',
      category: 'Category',
      unit: 'Unit',
      salePrice: 'Sale Price',
      discountPrice: 'Discount Price',
      saleQuantity: 'Sale Quantity',
      subtotal: 'Subtotal',

      // Settlement records
      settlementRecord: 'Settlement Records',
      settlementTime: 'Settlement Time',
      settlementAccount: 'Settlement Account',
      settlementAmount: 'Settlement Amount',

      // Delivery information
      deliveryInfo: 'Delivery Information',
      deliveryMethod: 'Delivery Method',
      logisticsCompany: 'Logistics Company',
      logisticsNumber: 'Logistics Number',
      deliveryAddress: 'Delivery Address',

      // Operation records
      operationRecord: 'Operation Records',
      operationTime: 'Operation Time',
      orderNode: 'Order Node',
      operator: 'Operator',

      auditGoods: 'Audit Items',
      auditMan: '审核人',
      auditDate: '审核时间',
      auditReason: '审核不通过原因',
    },

    edit: {
      // Form labels
      customerName: 'Customer Name',
      salesStore: 'Sales Store',
      deliveryWarehouse: 'Delivery Warehouse',
      discountType: 'Discount Type',
      deliveryMethod: 'Delivery Method',
      estimatedDeliveryTime: 'Estimated Delivery Date',
      remark: 'Remark',
      deliveryAmount: 'Shipping Fee',
      saleRemark: 'Sale Remark',
      returnRemark: '退货备注',
      innerRemark: 'Internal Remark',
      printRemark: 'Print Remark',

      // Placeholders
      selectCustomer: 'Please select customer',
      selectSalesStore: 'Please select sales store',
      selectDeliveryWarehouse: 'Please select delivery warehouse',
      selectDeliveryAddress: 'Please select delivery address',
      selectDeliveryMan: '请选择配送员',
      inputLogisticsCompany: 'Please enter logistics company',
      inputLogisticsNumber: 'Please enter logistics number',
      inputDiscount: 'Please enter discount',
      inputAmount: 'Please enter amount',
      setDeliveryAmount: 'Please set shipping fee',
      estimatedDeliveryTimePlaceholder: 'Estimated delivery time',
      inputWithMaxLength: 'Please enter, max {maxLength} characters',

      //
      adjustAmount: 'Adjust Amount',
      adjustAmountNone: 'None',
      adjustAmountRound: '整单抹零',
      adjustAmountCustom: 'Other',
      adjustAmountCustomPrice: 'Adjust Amount',
      adjustAmountCustomReason: 'Adjust Reason',

      // Sales details
      salesDetail: 'Sales Details',
      salesOrderNo: 'Sales Order No.',
      salesStatus: 'Sales Status',

      // Table column headers
      index: 'No.',
      productCode: 'Product Code',
      productName: 'Product Name',
      oe: 'OE',
      brandPartNo: 'Brand Part No.',
      brand: 'Brand',
      category: 'Category',
      origin: 'Origin',
      specification: 'Specification',
      unit: 'Unit',
      vehicleRemark: 'Vehicle Remark',
      productRemark: 'Product Remark',
      localStock: 'Local Stock',
      location: 'Location',
      suggestedPrice: 'Suggested Price',
      lastSalePrice: 'Last Sale Price',
      lowestPrice: 'Lowest Price',
      costPrice: 'Cost Price',
      grossMargin: 'Gross Margin',
      salePrice: 'Sale Price',
      saleQuantity: 'Sale Quantity',
      operation: 'Operation',

      // Button texts
      delete: 'Delete',
      add: 'Add',
      submit: 'Submit',
      confirmSettlement: 'Confirm Settlement',
      oneClickOutbound: 'One-Click Outbound',
      submitAndPrint: 'Submit and Print',

      // Confirmation messages
      confirmAddOverStock: 'Sale quantity exceeds available stock, confirm to add?',
      confirmDeleteWithDiscount: 'Please clear discount type before deleting products',

      // Success messages
      addSuccess: 'Added successfully',

      // Discount types
      discountTypes: {
        none: 'No Discount',
        orderDiscount: 'Order Discount',
        orderDeduction: 'Order Deduction',
        coupon: 'Coupon',
      },

      // Delivery methods
      deliveryMethods: {
        selfPickup: 'Self Pickup',
        merchantDelivery: 'Merchant Delivery',
        expressLogistics: 'Express Logistics',
      },

      // Units
      discountUnit: 'off',
      amountUnit: 'yuan',

      // Summary information
      totalQuantity: 'Total Quantity',
      totalAmount: 'Total Amount',
      discountAmount: 'Discount Amount',
      deliveryFee: 'Shipping Fee',
      actualAmount: 'Actual Amount',

      // Customer details
      customerDetail: 'Customer Details',
      creditCustomer: 'Credit Customer',
      contact: 'Contact',
      contactMethod: 'Contact Method',
      creditTerm: 'Credit Term',
      creditLimit: 'Credit Limit',
      used: 'Used',
      available: 'Available',
      days: 'days',
    },
  },

  returns: {
    list: {
      // Table column titles
      returnOrderNo: 'Return Order No.',
      customerName: 'Customer Name',
      customer: 'Customer',
      returnTime: 'Return Time',
      orderStatus: 'Order Status',
      returnStore: 'Return Store',
      receiveWarehouse: 'Receive Warehouse',
      refundAmount: 'Refund Amount',
      settlementMethod: 'Settlement Method',
      settlementStatus: 'Settlement Status',
      settlementAccount: 'Settlement Account',
      productInfo: 'Product Info',
      creator: 'Creator',
      operation: 'Operation',

      // Search placeholders
      productSearchPlaceholder: 'Product Name/Code/OE/Brand Part Number',

      // Button texts
      addSalesReturn: 'Add Sales Return',
      export: 'Export',
      edit: 'Edit',
      withdraw: 'Withdraw',
      void: 'Void',

      // Confirmation messages
      confirmWithdraw: 'Are you sure to withdraw this order?',
      confirmVoid: 'Are you sure to void this order?',

      // Summary texts
      totalReturnCount: 'Total Return Orders',
      totalReturnAmount: 'Total Return Amount',

      // Export task description
      exportTaskDesc: 'Retailer After-sale Order Export',
    },

    detail: {
      // Basic information
      customerDetail: 'Customer Details',
      viewCustomerDetail: 'View Customer Details',
      customerName: 'Customer Name',
      orderStatus: 'Order Status',
      refundAmount: 'Refund Amount',
      settlementMethod: 'Settlement Method',
      returnStore: 'Return Store',
      receiveWarehouse: 'Receive Warehouse',
      orderTime: 'Order Time',
      creator: 'Creator',
      remark: 'Remark',
      returnImage: 'Refund Images',

      // Action buttons
      edit: 'Edit',
      withdraw: 'Withdraw',
      void: 'Void',
      print: 'Print',
      oneClickInbound: 'One-Click Inbound',
      confirmSettlement: 'Confirm Settlement',

      // Confirmation messages
      confirmWithdraw: 'Are you sure to withdraw this order?',
      confirmVoid: 'Are you sure to void this order?',
      confirmOneClickInbound: 'Are you sure to proceed with one-click inbound?',

      // Table titles
      goodsDetail: 'Goods Details',
      settlementRecord: 'Settlement Records',
      operationRecord: 'Operation Records',
      settlementTime: 'Settlement Time',

      // Summary information
      totalQuantity: 'Total Quantity',
      totalReturnAmount: 'Total Return Amount',

      // Edit page name
      editPageName: 'Sales Return Edit',

      // Return reason
      returnReason: 'Return Reason',
    },

    // Return operation page
    operation: {
      // Form labels
      customer: 'Customer',
      selectCustomer: 'Please select customer',
      returnStore: 'Return Store',
      selectReturnStore: 'Please select return store',
      returnWarehouse: 'Return Warehouse',
      selectReturnWarehouse: 'Please select return warehouse',
      settlementMethod: 'Settlement Method',
      remark: 'Remark',
      maxCharacters: 'Maximum 200 characters supported!',

      // Return methods
      salesOrderReturn: 'Sales Order Return',
      salesOrderReturnDesc: 'Select products from sales order to return',
      goodsReturn: 'Goods Return',
      goodsReturnDesc: 'Select goods to return',

      // Table titles
      returnDetails: 'Return Details',
      returnOrderNo: 'Return Order No.',
      returnStatus: 'Return Status',

      // Action buttons
      delete: 'Delete',
      submit: 'Submit',
      return: 'Return',

      // Messages
      addSuccess: 'Added successfully',
      operationSuccess: 'Operation successful',
      submitSuccess: 'Order submitted successfully!',
      maxGoodsWarning: 'Number of added goods cannot exceed 200',
      inputReturnAmount: 'Please enter return amount!',
      inputReturnQuantity: 'Please enter return quantity!',
      fillReturnAmount: 'Please fill in return amount!',
      fillReturnQuantity: 'Please fill in return quantity!',
      selectDifferentAccount: 'Please select different account!',
      selectAccount: 'Please select account',
      inputAmount: 'Please enter amount',

      // Settlement related
      confirmSettlement: 'Confirm Settlement',
      directInbound: 'Direct Inbound',
      printAfterSubmit: 'Print After Submit',
      usedAvailable: 'Used {used}/Available {available}',

      // Summary information
      totalQuantity: 'Total Quantity',
      totalGoodsAmount: 'Total Items Amount',
      totalRefundAmount: 'Total Refund Amount',

      // Search related
      productInfo: 'Product Information',
      productSearchPlaceholder: 'Product name/code/OE/brand part number',

      // Table column headers

      salesOrderNo: 'Sales Order No.',
      salesTime: 'Sales Time',
      productCode: 'Product Code',
      productName: 'Product Name',
      oe: 'OE No.',
      brandPartNo: 'Brand Part No.',
      brand: 'Brand',
      salesStore: 'Sales Store',
      deliveryWarehouse: 'Delivery Warehouse',
      paymentMethod: 'Payment Method',
      actualUnitPrice: 'Actual Unit Price',
      salesQuantity: 'Sales Quantity',
      refundableQuantity: 'Refundable Quantity',
      refundAmount: 'Refund Amount',
      returnQuantity: 'Return Quantity',

      causeType: {
        PRODUCT_NOT_MATCH: '产品不适配',
        PRODUCT_QUALITY: '产品质量问题',
        BUYER_NOT_WANT: '车主不要了',
        INVENTORY_RETURN: '库存退货',
        PACKAGE_BROKEN: '包装破损',
        OTHER: '其他',
      },

      returnType: {
        label: '退货性质',
        good: '好件',
        broken: '坏件',
      },
    },

    settlement: {
      // Settlement method modal
      title: 'Settlement Method',
      settlementAmount: 'Settlement Amount',
      settlementMethod: 'Settlement Method',
      remainingCredit: 'Remaining Credit',
      used: 'Used',
      available: 'Available',
      selectAccount: 'Please select account',
      inputAmount: 'Please enter amount',
      confirm: 'Confirm',
      duplicateAccountError: 'Please select different accounts!',
    },
  },
};
