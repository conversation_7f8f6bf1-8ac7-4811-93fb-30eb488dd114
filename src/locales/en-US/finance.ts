
export default {
    collection: {
        title: 'Receivables Management',
        detail: 'Receivable Details',
        detailTitle: 'Receivable Details',
        totalReceivableAmount: 'Total Receivable Amount',
        onlyShowReceivable: 'Only show existing receivables amount > 0',
        onlyShowRemaining: 'Only show existing receivables amount > 0',
        exportDescription: 'Retailer receivables management data export',
        exportDetailDescription: 'Retailer receivables management detail data export',
        columns: {
            customer: 'Receivable Object',
            store: 'Store',
            overdueStatus: 'Overdue Status',
            receivableAmount: 'Receivable Amount',
            overdueAmount: 'Overdue Amount',
            totalAmount: 'Total Credit',
            availableAmount: 'Available Credit',
            creditTerms: 'Credit Terms (Days)',
            arrearsDay: 'Arrears Days',
            remainTerms: 'Remaining Terms (Days)',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            receivedAmount: 'Received Amount',
            remainReceivableAmount: 'Outstanding Amount',
            customerCode: 'Unit Code',
            contact: 'Contact',
            contactPhone: 'Contact Phone',
            contactAddress: 'Contact Address',
            settleType: 'Settle Type',
            currency: 'Currency',
            rate: 'Rate',
        },
        summary: {
            orderTotal: 'Order Total',
            receivedTotal: 'Received Total',
            receivableTotal: 'Outstanding Total',
            overdueTotal: 'Overdue Total',
        },
    },
    receive: {
        title: 'Payment Management',
        detail: 'Payment Details',
        add: 'Add Payment',
        confirm: 'Confirm Payment',
        autoAssign: 'Auto Assign',
        receivedAmount: 'Received Amount',
        currentWriteOff: 'Current Write-off Total',
        writeOffOrder: 'Write-off Orders',
        writeOffAmountMismatch: 'Current write-off total must equal received amount',
        noAmountError: 'Received amount cannot be empty',
        negativeAmountError: 'Received amount is less than or equal to 0, please manually assign write-off amount',
        noOrderSelectedWarning: 'No specific orders selected for this payment, please check!',
        amountMismatchError: 'Total received amount from orders does not match input received amount, please modify!',
        columns: {
            serialNumber: 'Payment No.',
            store: 'Payment Store',
            receiveTime: 'Payment Time',
            customer: 'Receiver',
            customerName: 'Customer',
            receivedAccount: 'Payment Account',
            receivedAmount: 'Received Amount',
            creator: 'Creator',
            remark: 'Remark',
            receiveType: 'Payment Type',
            storeName: 'Store Name',
            businessOrderNo: 'Business Order No.',
            orderCompleteTime: 'Order Complete Time',
            transactionCompleteTime: 'Transaction Complete Time',
            orderAmount: 'Order Amount',
            receivedAmountPaid: 'Received Amount',
            unreceived: 'Unreceived Amount',
            currentWriteOff: 'Current Write-off',
            writeOffAmount: 'Write-off Amount',
            adjustAmount: 'Adjust Amount',
            status: 'Status',
            advanceAmount: 'Advance Amount',
            currency: 'Currency',
            rate: 'Rate',
            lossAmount: 'Loss Amount',
            image: 'Image',
            writeOffTotal: 'Write-off Total',
        },
        placeholders: {
            businessOrderNo: 'Business Order No.',
            enterAmount: 'Please enter',
        },
        status: {
            cancelled: 'Cancelled',
            aborted: 'Aborted',
            draft: 'Draft',
            pending: 'Pending',
            unpayment: 'Unpayment',
            payment: 'Payment',
        },
        audit: {
            title: 'Audit',
            result: 'Audit Result',
            approve: 'Approve',
            reject: 'Reject',
            opinion: 'Audit Opinion',
            'reject.reason.placeholder': 'Please enter the reason for rejection',
        },
        cancel: {
            title: 'Cancel Receipt',
            content: 'Are you sure you want to cancel this receipt?',
            confirm: 'Confirm Cancel',
            giveup: 'Give Up',
        },
        multiCurrencyError: 'Only orders of one currency can be written off at a time. Please create separate receipts for orders of different currencies.',
        totalReceivable: 'Total Receivable',
        receiveStore: 'Receiving Store',
        receiveCurrency: 'Currency',
        rate: 'Rate',
        adjustAmount: 'Adjustment Amount',
        adjustReason: 'Adjustment Reason',
        adjustType: {
            none: 'None',
            round: 'Rounding',
            discount: 'Discount',
        },
        filter: {
            due: 'Show Due Only',
            overdue: 'Show Overdue Only',
        },
        receiptImage: 'Receipt Image',
        summary: {
            totalReceived: 'Received Amount',
            totalAdjust: 'Adjustment Amount',
            totalWriteOff: 'Total Write-off',
            totalAdvance: 'To Advance',
            local: {
                totalReceived: 'Received (Local)',
                totalAdjust: 'Adjustment (Local)',
                totalWriteOff: 'Write-off (Local)',
                totalAdvance: 'To Advance (Local)',
                loss: 'Currency Loss',
            },
        },
    },
    payment: {
        title: 'Payables Management',
        detail: 'Payable Details',
        detailTitle: 'Payable Details',
        totalPayableAmount: 'Total Payable Amount',
        onlyShowPayable: 'Only show payables amount > 0',
        onlyShowRemaining: 'Only show payables amount > 0',
        exportDescription: 'Retailer payables management data export',
        exportDetailDescription: 'Retailer payables management detail data export',
        columns: {
            supplier: 'Supplier',
            payableObject: 'Payable Object',
            supplierCode: 'Unit Code',
            store: 'Store',
            payableAmount: 'Payable Amount',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            paidAmount: 'Paid Amount',
            remainPayableAmount: 'Payable Amount',
            contact: 'Contact',
            contactPhone: 'Contact Phone',
            contactAddress: 'Contact Address',
        },
        summary: {
            orderTotal: 'Order Total',
            paidTotal: 'Paid Total',
            payableTotal: 'Payable Total',
        },
    },
    supplierPayment: {
        title: 'Supplier Payment',
        detailTitle: 'Payment Details',
        add: 'Add Payment',
        confirm: 'Confirm Payment',
        autoAssign: 'Auto Assign',
        paymentAmount: 'Payment Amount',
        currentWriteOff: 'Current Write-off Total',
        writeOffOrder: 'Write-off Orders',
        writeOffAmountMismatch: 'Current write-off total must equal payment amount',
        noAmountError: 'Payment amount cannot be empty and must be greater than 0!',
        negativeAmountError: 'Payment amount is less than or equal to 0, please manually assign write-off amount',
        noOrderSelectedWarning: 'No specific documents selected for this payment, please check!',
        amountMismatchError: 'Total payment amount for each document is inconsistent with input payment amount, please modify!',
        totalPayable: 'Total Payable',
        paymentCurrency: 'Payment Currency',
        multiCurrencyError: 'Only orders of one currency can be written off at a time. Please create separate payments for orders of different currencies.',
        multiCurrencynotSupportError: 'Multi-currency orders are not supported for automatic allocation. Please manually assign write-off amount.',
        summary: {
            paymentAmountLocal: 'Payment Amount (Local)',
            currentWriteOffLocal: 'Current Write-off (Local)',
            lossAmount: 'Loss Amount',
        },
        columns: {
            serialNumber: 'Payment No.',
            paymentStore: 'Payment Store',
            paymentTime: 'Payment Time',
            supplier: 'Payment Object',
            supplierName: 'Payment Object',
            paymentAccount: 'Payment Account',
            paymentAmount: 'Payment Amount',
            creator: 'Creator',
            remark: 'Payment Remark',
            storeName: 'Store Name',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            paidAmount: 'Paid Amount',
            unpaidAmount: 'Unpaid Amount',
            currentWriteOff: 'Current Write-off',
            writeOffAmount: 'Write-off Amount',
            status: 'Payment Status',
            lossAmount: 'Loss Amount',
            image: 'Image',
            paymentPic: 'Payment Pic',
        },
        placeholders: {
            businessOrderNo: 'Business Order No.',
            enterAmount: 'Please enter',
        },
        detail: {
            columns: {
                storeName: 'Store Name',
                orderNo: 'Order No.',
                billDate: 'Bill Date',
                orderAmount: 'Order Amount',
                paymentAmount: 'Payment Amount',
            }
        }
    },
    cost: {
        title: 'Income & Expense Management',
        income: 'Income',
        expend: 'Expense',
        otherIncome: 'Other Income',
        otherExpend: 'Other Expense',
        add: 'Add',
        invalidateSuccess: 'Invalidated successfully',
        confirmInvalidate: 'Confirm to invalidate?',
        invalidate: 'Invalidate',
        columns: {
            store: 'Store',
            incomeType: 'Income Type',
            expendType: 'Expense Type',
            createTime: 'Create Time',
            serialNumber: 'Transaction No.',
            incomeStore: 'Income Store',
            expendStore: 'Payment Store',
            incomeAmount: 'Income Amount',
            expendAmount: 'Payment Amount',
            settlementAccount: 'Settlement Account',
            creator: 'Creator',
            incomeRemark: 'Income Remark',
            expendRemark: 'Payment Remark',
            incomeNature: 'Income Nature',
            expendNature: 'Expense Nature',
            // 付款对象
            incomeObject: 'Income Object',
            // 收款对象
            expendObject: 'Payment Object',
        },
        form: {
            incomeStore: 'Income Store',
            expendStore: 'Payment Store',
            incomeType: 'Income Type',
            expendType: 'Expense Type',
            incomeAmount: 'Income Amount',
            expendAmount: 'Payment Amount',
            settlementAccount: 'Settlement Account',
            incomeRemark: 'Income Remark',
            expendRemark: 'Payment Remark',
            yuan: 'Yuan',
            pic: 'Image',
        },
        status: {
            cancelled: 'Cancelled',
            aborted: 'Aborted',
            pending: 'Pending',
            confirmed: 'Confirmed',
        },
        type: {
            receivable: 'Receiveable',
            payable: 'Payable',
            income: 'Income',
            expend: 'Expend',
        },
        recordType: 'Record as {type}',
        detailTitle: '{type} Details',
    },
    flow: {
        title: 'Capital Flow',
        totalIncome: 'Total Income',
        totalExpend: 'Total Expenditure',
        columns: {
            businessOrderNo: 'Business Order No.',
            occurTime: 'Occurrence Time',
            customerOrSupplier: 'Customer/Supplier/Other Company',
            incomeExpendType: 'Income/Expense Type',
            store: 'Store',
            settlementAccount: 'Settlement Account',
            income: 'Income',
            expend: 'Expenditure',
        },
    },
    customer: {
        title: 'Account Management',
        addAccount: 'Add Account',
        editAccount: 'Edit Account',
        confirmDelete: 'Confirm to delete?',
        columns: {
            accountName: 'Account Name',
            bankName: 'Bank Name',
            bankCardNumber: 'Bank Card Number',
            belongToStore: 'Belongs to Store',
            accountBalance: 'Account Balance',
            initialBalance: 'Initial Balance',
            currency: 'Currency',
        },
        form: {
            accountName: 'Account Name',
            bankName: 'Bank Name',
            bankCardNumber: 'Bank Card Number',
            belongToStore: 'Belongs to Store',
            initialBalance: 'Initial Balance',
            currency: 'Currency',
        },
    },
    tag: {
        title: 'Income & Expense Type Management',
        incomeExpenseType: 'Income & Expense Type',
        addIncomeExpenseType: 'Add Income & Expense Type',
        editIncomeExpenseType: 'Edit Income & Expense Type',
        columns: {
            incomeExpenseType: 'Income & Expense Type',
            incomeDirection: 'Income Direction',
            source: 'Source',
            isEnabled: 'Is Enabled',
        },
        form: {
            incomeExpenseType: 'Income & Expense Type',
            incomeExpenseDirection: 'Income & Expense Direction',
            income: 'Income',
            expense: 'Expense',
            pleaseInput: 'Please input',
        },
    },
    currency: {
        title: 'Currency Management',
        name: 'Currency Name',
        symbol: 'Currency Symbol',
        exchangeRate: 'Exchange Rate',
        'exchangeRate.tooltip': 'Amount of the coin * exchange rate = Amount of other coins',
        isBase: 'standard coin',
        status: 'Status',
        source: 'Source',
        add: 'Add Currency'
    },
    accountFlow: {
        title: 'Advance Details',
        totalAdvanceBalance: 'Total Advance Balance',
        columns: {
            bizNo: 'Business No.',
            bizTime: 'Time',
            customerName: 'Customer',
            bizTypeName: 'Business Type',
            storeName: 'Store',
            amount: 'Amount',
            accountBalance: 'Balance',
            createPerson: 'Operator',
        },
        bizType: {
            RECEIVED_INCOME: 'Advance Top-up',
            ADVANCE_RECEIVED_IN: 'Payment using advance',
            ADVANCE_SALE_OUT: 'Sales using advance',
            ADVANCE_REFUND_IN: 'Refund to advance',
        }
    },
    otherRelated: {
        search: {
            info: {
                placeholder: 'Unit Info/Contact/Contact Phone',
            },
        },
        columns: {
            companyCode: 'Unit Code',
            companyName: 'Unit Name',
            shortName: 'Unit Short Name',
            abn: 'ABN',
            defaultContact: 'Default Contact',
            defaultContactPhone: 'Default Contact Phone',
            defaultAddress: 'Default Address',
            remark: 'Remark',
            status: 'Status',
            operation: 'Operation',
            companyInfo: 'Company Info',
            contact: 'Contact',
            contactPhone: 'Contact Phone',
        },
        confirm: {
            enable: 'Confirm to enable?',
            disable: 'Confirm to disable?',
        },
        detail: {
            title: 'Unit Details',
            basicInfo: 'Basic Information',
            contactInfo: 'Contact Information',
            addressInfo: 'Address Information',
            contact: {
                post: 'Position',
                isDefault: 'Default Contact',
            },
            address: {
                isDefault: 'Default Address',
                number: 'Address {number}',
            },
        },
        form: {
            title: {
                add: 'Add Other Related Unit',
                edit: 'Edit Other Related Unit',
            },
            basicInfo: 'Basic Information',
            companyName: 'Unit Name',
            companyNamePlaceholder: 'Supplier Code/Name/Short Name',
            shortName: 'Unit Short Name',
            companyCode: 'Unit Code',
            companyCodePlaceholder: 'Will be automatically generated if not entered',
            abn: 'ABN',
            remark: 'Remark',
            contactInfo: 'Contact Information',
            addressInfo: 'Address Information',
            button: {
                addContact: 'Add Contact',
                addAddress: 'Add Address',
            },
            contact: {
                firstName: 'First Name',
                lastName: 'Last Name',
                contactPerson: 'Contact Person',
                phone: 'Phone',
                post: 'Position',
                email: 'Email',
                remark: 'Remark',
                isDefault: 'Set as Default',
            },
            address: {
                postCode: 'Post Code',
                suburb: 'Suburb',
                state: 'State',
                detail: 'Detail Address',
                contact: 'Contact',
                phone: 'Phone',
                isDefault: 'Set as Default',
            },
        },
    },
    bill: {
        detail: {
            title: 'Bill Detail',
            billDetail: 'Bill Detail',
            columns: {
                orderNo: 'Order No',
                billDate: 'Bill Date',
                storeName: 'Store Name',
                orderAmount: 'Order Amount',
                receivedAmount: 'Received Amount',
                remainReceivableAmount: 'Outstanding Amount',
            },
            summary: {
                billTotalAmount: 'Bill Total Amount',
                billReceivedAmount: 'Bill Received Amount',
                remainAmount: 'Outstanding Total Amount',
            }
        },
        columns: {
            billNo: 'Bill No',
            customerName: 'Customer',
            customerId: 'Customer Code',
            storeName: 'Store',
            billCycle: 'Bill Cycle',
            cycleType: 'Cycle Type',
            billTotalAmount: 'Bill Total Amount',
            billReceivedAmount: 'Bill Received Amount',
            remainAmount: 'Outstanding Amount',
            billDate: 'Bill Date',
            overdueDate: 'Overdue Date',
            status: 'Status',
        },
        status: {
            billed: 'Billed',
            overdue: 'Overdue',
            cleared: 'Cleared',
        },
    },
    budget: {
        title: 'Budget Management',
        button: {
            add: 'Add Budget',
            viewUsage: 'View Usage',
        },
        modal: {
            add: 'Add Budget',
            edit: 'Edit Budget',
        },
        column: {
            budgetName: 'Budget Name',
            totalBudget: 'Budget Amount',
            type: 'Budget Type',
            itemNames: 'Related Expense Items',
            cycle: 'Budget Cycle',
            updater: 'Updater',
            updateTime: 'Update Time',
        },
        form: {
            budgetName: 'Budget Name',
            totalBudget: 'Budget Amount',
            type: 'Budget Type',
            itemIds: 'Related Expense Items',
            cycleType: 'Budget Cycle',
            year: 'Annual',
            month: 'Monthly',
        },
        type: {
            purchase: 'Purchase Expense',
            other: 'Other Expense',
        },
        cycle: {
            annual: 'Annual',
            monthly: 'Monthly',
        },
        usage: {
            title: 'Budget Usage',
            detail: 'Usage Detail',
            column: {
                usedAmount: 'Used Amount',
                paidAmount: 'Paid Amount',
                totalAmount: 'Total Budget',
            },
        },
    },
};