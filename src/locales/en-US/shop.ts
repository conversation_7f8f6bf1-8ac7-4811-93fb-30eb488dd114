export default {
  'shop.common.button.addProduct': 'Add Product',
  'shop.common.button.addGift': 'Add Gift',
  'shop.common.message.needAddProduct': 'Please add at least one product',
  'shop.common.message.needAddGift': 'Please add at least one gift',

  // 专题页管理
  'shop.topic.topicStatus.draft': 'draft',
  'shop.topic.topicStatus.published': 'published',
  'shop.topic.topicPageSource.topic': 'topic',
  'shop.topic.topicPageSource.app': 'app home',

  'shop.topic.list.topicPageId': 'Topic Page ID',
  'shop.topic.list.topicPageName': 'Topic Page Name',
  'shop.topic.list.pageStatus': 'Page Status',
  'shop.topic.list.pageSource': 'Page Type',
  'shop.topic.list.updater': 'Updater',
  'shop.topic.list.updateTime': 'Update Time',
  'shop.topic.list.pageRemark': 'Page Remark',
  'shop.topic.list.copy': 'Copy',
  'shop.topic.list.publish': 'Publish',
  'shop.topic.list.unpublish': 'Unpublish',
  'shop.topic.list.operation.decoration': 'Decoration',

  // 活动管理
  'shop.activity.list.activityId': 'Activity ID',
  'shop.activity.list.activityName': 'Activity Name',
  'shop.activity.list.activityType': 'Activity Type',
  'shop.activity.list.activityStatus': 'Activity Status',
  'shop.activity.list.activityStep': 'Progress Status',
  'shop.activity.list.activityStartTime': 'Activity Start Time',
  'shop.activity.list.activityEndTime': 'Activity End Time',
  'shop.activity.list.updater': 'Updater',
  'shop.activity.list.updateTime': 'Update Time',
  'shop.activity.list.activityRemark': 'Activity Remark',
  'shop.activity.activityStatus.draft': 'Draft',
  'shop.activity.activityStatus.takeEffect': 'Take Effect',
  'shop.activity.activityStatus.invalid': 'Invalid',
  'shop.activity.activityType.everyFullGift': 'Every Full Gift',
  'shop.activity.activityType.ladderFullGift': 'Ladder Full Gift',
  'shop.activity.activityType.buyGiftSelf': 'Buy Gift Self',
  'shop.activity.activityType.specialPrice': 'Special Price',
  'shop.activity.activityType.suiteItem': 'Suite Item',
  'shop.activity.activityProgress.notStartActivity': 'Not Start',
  'shop.activity.activityProgress.inActivity': 'In Activity',
  'shop.activity.activityProgress.endActivity': 'End Activity',
  'shop.activity.list.operation.enable': 'Valid',
  'shop.activity.list.operation.disable': 'Invalid',

  'shop.activity.title.basicInfo': 'Basic Information',
  'shop.activity.title.applicableProducts': 'Applicable Products',
  'shop.activity.title.giftInfo': 'Gift Information',
  'shop.activity.name': 'Activity Name',
  'shop.activity.description': 'Activity Description',
  'shop.activity.descriptionPlaceholder':
    'Supports up to 200 characters, displayed on activity page',
  'shop.activity.period': 'Activity Period',
  'shop.activity.startTime': 'Start Date',
  'shop.activity.endTime': 'End Date',
  'shop.activity.audience': 'Audience',
  'shop.activity.audienceUnlimited': 'Unlimited',
  'shop.activity.audienceSpecificTags': 'Specific Tag Customers',
  'shop.activity.image': 'Activity Image',
  'shop.activity.uploadImage': 'Upload Image',
  'shop.activity.supportCouponOverlay': 'Support Coupon Overlay',
  'shop.activity.supportAccountPayment': 'Support Account Payment',
  'shop.activity.allowOrderLowStock': 'Allow Order if Stock Insufficient',
  'shop.activity.remarks': 'Activity Remarks',
  'shop.activity.remarksPlaceholder': 'Supports up to 200 characters, for internal use only',
  'shop.activity.tagRequired': 'Please select specific customer tags',

  'shop.activity.products.activityPrice': 'Activity Price',
  'shop.activity.products.minOrderQuantityPerCustomer': 'Min Order Quantity Per Customer',
  'shop.activity.products.maxPurchaseQuantityPerCustomer': 'Max Purchase Quantity Per Customer',
  'shop.activity.products.totalPurchaseQuantity': 'Total Purchase Quantity',
  'shop.activity.products.sortOrder': 'Sort',
  'shop.activity.products.purchasedQuantity': 'Purchased Quantity',
  'shop.activity.products.giftQuantity': 'Gift Quantity',
  'shop.activity.products.totalMaxPurchaseQuantity': 'Total Max Purchase Quantity',
  'shop.activity.products.totalMaxGiftQuantity': 'Total Max Gift Quantity',
  'shop.activity.products.type.productCombination': 'Product Combination',
  'shop.activity.products.type.discountPackage': 'Discount Package',
  'shop.activity.products.promotionPrice': 'Activity Price',
  'shop.activity.products.quantity': 'Quantity',
  'shop.activity.products.limit': 'Limit',
  'shop.activity.products.packageActivityPrice': 'Package Activity Price',
  'shop.activity.products.minOrderPackageNum': 'Min Order Package Num',
  'shop.activity.products.singleCustomerLimitPackageNum': 'Single Customer Limit Package Num',
  'shop.activity.products.totalLimitPackageNum': 'Total Limit Package Num',
  'shop.activity.products.totalPurchasePrice': 'Total Purchase Price',
  'shop.activity.products.packageTotalPrice': 'Package Total Price',
  'shop.activity.products.discountPrice': 'Discount Price',

  'shop.activity.products.required': 'This field is required',
  'shop.activity.products.activityPriceRequired': 'Activity price is required',
  'shop.activity.products.numberMinZero': 'Must be greater than or equal to 0',
  'shop.activity.products.numberPositive': 'Must be greater than 0',

  'shop.activity.products.taskDesc.specialPrice': 'special price activity product import',
  'shop.activity.products.taskDesc.everyFullGift': 'every full gift activity product import',
  'shop.activity.products.taskDesc.ladderFullGift':
    'ladder full gift / per full gift activity product import',
  'shop.activity.products.taskDesc.buyGiftSelf': 'buy gift self activity product import',
  'shop.activity.products.taskDesc.suiteItem': 'suite item activity product import',

  'shop.activity.giftInfo.title': 'Gift Settings',
  'shop.activity.giftInfo.giftRule': 'Gift Rule',
  'shop.activity.giftInfo.perFull': 'Per Full',
  'shop.activity.giftInfo.tieredGifting': 'Tiered Gifting',
  'shop.activity.giftInfo.giftCondition': 'Gift Condition',
  'shop.activity.giftInfo.fullItems': 'Full Items',
  'shop.activity.giftInfo.fullAmount': 'Full Amount',
  'shop.activity.giftInfo.conditionUnitItems': 'Items',
  'shop.activity.giftInfo.conditionUnitAmount': 'Yuan',
  'shop.activity.giftInfo.addGift': 'Add Gift',
  'shop.activity.giftInfo.giftQuantity': 'Gift Quantity',
  'shop.activity.giftInfo.totalMaxBuyQuantity': 'Total Max Buy Quantity',
  'shop.activity.giftInfo.ladder': 'Tier',
  'shop.activity.giftInfo.addLadder': 'Add Tier',
  'shop.activity.giftInfo.enableAmountRequired': 'Please enter condition amount/quantity',
  'shop.activity.giftInfo.giftQuantityRequired': 'Please enter gift quantity',
  'shop.activity.giftInfo.maxBuyQuantityRequired': 'Please enter total max buy quantity',
  'shop.activity.giftInfo.giftQuantityMin': 'Gift quantity cannot be less than 1',
  'shop.activity.giftInfo.maxBuyQuantityMin': 'Total max buy quantity cannot be less than 1',
  'shop.activity.giftInfo.giftItemsRequired': 'Each tier must have at least one gift item',
  'shop.activity.giftInfo.ladderMin': 'At least one tier is required',

  // 优惠券管理
  'shop.coupon.list.button.batchSend': 'Batch Send Coupon',
  'shop.coupon.list.batchSend.methodChoose': 'Choose Customer',
  'shop.coupon.list.batchSend.methodInput': 'Manual Input',
  'shop.coupon.list.batchSend.methodInput.placeholder':
    'Please enter customer id, separated by commas, up to 20 customer ids',
  'shop.coupon.list.couponId': 'Coupon ID',
  'shop.coupon.list.couponName': 'Coupon Name',
  'shop.coupon.list.startingAmount': 'Starting Amount',
  'shop.coupon.list.couponAmount': 'Coupon Amount',
  'shop.coupon.list.totalQuantity': 'Total Quantity',
  'shop.coupon.list.remainingQuantity': 'Remaining Quantity',
  'shop.coupon.list.validityPeriod': 'Validity Period',
  'shop.coupon.list.status': 'Status',
  'shop.coupon.list.updater': 'Updater',
  'shop.coupon.list.updateTime': 'Update Time',
  'shop.coupon.list.effectiveTime': 'Effective Time',
  'shop.coupon.list.expiryTime': 'Expiry Time',
  'shop.coupon.list.operation.valid': 'Valid',
  'shop.coupon.list.operation.invalid': 'Invalid',
  'shop.coupon.couponStatus.wait': 'Wait',
  'shop.coupon.couponStatus.active': 'Active',
  'shop.coupon.couponStatus.invalid': 'Invalid',

  // 优惠券使用记录
  'shop.couponRecord.list.used': 'Used',
  'shop.couponRecord.list.valid': 'Valid',
  'shop.couponRecord.list.expired': 'Expired',
  'shop.couponRecord.list.invalid': 'Invalid',
  'shop.couponRecord.list.notUsed': 'Not Used',
  'shop.couponRecord.list.operation.invalid': 'Invalid',
  'shop.couponRecord.list.operation.batchInvalid': 'Batch Invalid',
  'shop.couponRecord.message.select.notUsed': 'Please select unused coupons',
  'shop.couponRecord.confirm.batchInvalid':
    'Are you sure you want to batch invalidate the selected {count} coupons?',

  'shop.couponRecord.list.recordId': 'Record ID',
  'shop.couponRecord.list.couponId': 'Coupon ID',
  'shop.couponRecord.list.couponName': 'Coupon Name',
  'shop.couponRecord.list.couponValidity': 'Coupon Validity',
  'shop.couponRecord.list.customerCode': 'Customer Code',
  'shop.couponRecord.list.customerName': 'Customer Name',
  'shop.couponRecord.list.status': 'Status',
  'shop.couponRecord.list.issuerReceiver': 'Issuer/Receiver',
  'shop.couponRecord.list.receiveTime': 'Receive Time',
  'shop.couponRecord.list.useTime': 'Use Time',
  'shop.couponRecord.list.orderNumber': 'Order Number',

  'shop.coupon.basicInfo': 'Basic Information',
  'shop.coupon.applicableProducts': 'Applicable Products',
  'shop.coupon.couponName': 'Coupon Name',
  'shop.coupon.totalStock': 'Total Stock',
  'shop.coupon.singleAccountNum': 'Claim Limit',
  'shop.coupon.singleAccountNum.addonBefore': 'Per Account',
  'shop.coupon.singleAccountNum.addonAfter': 'Each',
  'shop.coupon.discountAmount': 'Discount Amount',
  'shop.coupon.enableAmount': 'Enable Amount',
  'shop.coupon.validityType': 'Validity Type',
  'shop.coupon.validityTypeAbsolute': 'Absolute Time',
  'shop.coupon.validityTypeRelative': 'Relative Time',
  'shop.coupon.validityBeginTime': 'Effective Time',
  'shop.coupon.validityEndTime': 'Expiry Time',
  'shop.coupon.validityDays': 'Validity (Days)',
  'shop.coupon.validityDaysPlaceholder': 'Please enter days',
  'shop.coupon.validityDaysPlaceholder.addonBefore': 'Expire in',
  'shop.coupon.validityDaysPlaceholder.addonAfter': 'Days',
  'shop.coupon.useDesc': 'Usage Description',
  'shop.coupon.useDescPlaceholder': 'Supports up to 200 characters, displayed to customers',
  'shop.coupon.remark': 'Activity Remarks',
  'shop.coupon.remarkPlaceholder': 'Supports up to 200 characters, for internal use only',
  'shop.coupon.validity.scopeBrandCategoryListRequired':
    'Please select at least one brand or category',

  'shop.coupon.goodsRange': 'Product Scope',
  'shop.coupon.goodsRangeSpecific': 'Specific Products',
  'shop.coupon.goodsRangeGeneral': 'General',
  'shop.coupon.productSettings': 'Product Settings',
  'shop.coupon.itemScopeTypeSpecific': 'Specific Products',
  'shop.coupon.itemScopeTypeBrandCategory': 'Specific Brands & Categories',

  'shop.coupon.minAmountValidation': 'Enable amount cannot be less than 0',
  'shop.coupon.minStockValidation': 'Total stock cannot be less than 0',
  'shop.coupon.maxDaysValidation': 'Validity days cannot exceed 365 days',
  'shop.coupon.minDaysValidation': 'Validity days cannot be less than 1 day',
  'shop.coupon.dateRangeValidation': 'Please select start and end time',

  // 商城目录
  'shop.category.add': 'Create Top-level Category',
  'shop.category.updateSort': 'Save Sorting ',
  'shop.category.expandAll': 'Expand All',
  'shop.category.collapseAll': 'Collapse All',

  'shop.category.categoryName': 'Category Name',
  'shop.category.jumpType': 'Redirect Type',
  'shop.category.jumpContent': 'Redirect Content',
  'shop.category.status': 'Status',
  'shop.category.sort': 'Sort',
  'shop.category.opt': 'Operation',

  'shop.category.add.child': 'Add Subcategory',
  'shop.category.parent': 'Parent Category',
  'shop.category.image': 'Category Image',
  'shop.category.updateImage': 'Upload Image',

  'shop.category.addNew': 'Add Category',
  'shop.category.edit': 'Edit Category',

  'shop.category.keyword': 'Keywords',
  'shop.category.show': 'Visible',
  'shop.category.hide': 'Hidden',
};
