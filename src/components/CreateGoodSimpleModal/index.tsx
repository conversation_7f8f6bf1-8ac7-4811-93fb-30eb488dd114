import { Button, Flex, Form, message, Modal } from 'antd';
import { useIntl } from 'umi';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormField, ProFormSelect } from '@ant-design/pro-form';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import { addBrandProperty, queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { addGoods } from '@/pages/goods/list/services';
import { ChannelCode } from '@/pages/goods/list/types/channel.code';

export interface CreateGoodSimpleModalProps {
  visible: boolean;
  onClose: () => void;
  onRefresh?: () => void;
}

export default function CreateGoodSimpleModal(props: CreateGoodSimpleModalProps) {
  const { visible, onClose, onRefresh } = props;
  const intl = useIntl();
  const [form] = Form.useForm();

  //品牌输入值
  const [searchBrandName, setSearchBrandName] = useState<string>();
  const [brandOptions, setBrandOptions] = useState<{ label: any; value: number }[]>([]);

  useAsyncEffect(async () => {
    if (!props.visible) {
      return;
    }
    const { data: brandData } = await queryGoodsPropertyPage(
      { brandName: searchBrandName, pageNo: 1, pageSize: 1000, brandStatus: '1' },
      'brand',
    );
    setBrandOptions(
      brandData.map((t: any) => ({
        label: t.brandName,
        value: t.brandId,
      })),
    );
  }, [searchBrandName, visible]);

  const addGoodsBrand = async () => {
    if (searchBrandName) {
      const result = await addBrandProperty({ brandName: searchBrandName });
      if (result) {
        // @ts-ignore
        const brandId = parseInt(result[0]);
        setBrandOptions((pre) => [{ value: brandId, label: searchBrandName }, ...pre]);
        form.setFieldValue('brandId', brandId);
        message.success(
          intl.formatMessage(
            { id: 'goods.createForm.addBrandSuccess' },
            { brandName: searchBrandName },
          ),
        );
      }
    } else {
      message.error(intl.formatMessage({ id: 'goods.createForm.inputBrandNameRequired' }));
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      title={intl.formatMessage({ id: 'goods.list.createGoodsTitle' })}
      footer={false}
    >
      <ProForm
        form={form}
        className="py-5"
        onFinish={async (formData: any) => {
          return addGoods({ ...formData, channelCodeList: [ChannelCode.Store] }).then(() => {
            message.success(intl.formatMessage({ id: 'common.message.save.success' }));
            onRefresh?.();
            onClose?.();
            form.resetFields();
          });
        }}
      >
        <ProFormText
          rules={[REQUIRED_RULES]}
          name="itemName"
          required
          label={intl.formatMessage({ id: 'goods.createForm.itemNameLabel' })}
        />
        <ProFormField
          rules={[REQUIRED_RULES]}
          label={intl.formatMessage({ id: 'goods.createForm.brandPartNoLabel' })}
          name="brandPartNos"
          transform={(value: string) => {
            return { brandPartNos: !isEmpty(value) ? [value] : value };
          }}
        />
        <ProFormSelect
          rules={[REQUIRED_RULES]}
          name="brandId"
          showSearch
          label={intl.formatMessage({ id: 'goods.createForm.brandLabel' })}
          options={brandOptions}
          fieldProps={{
            filterOption: false,
            onSearch: (value) => setSearchBrandName(value),
            notFoundContent: (
              <Flex vertical align="center" justify="center">
                <span className="mb-4">
                  {intl.formatMessage({ id: 'goods.createForm.searchEmpty' })}
                </span>
                <Button type="link" onClick={addGoodsBrand}>
                  {intl.formatMessage({ id: 'goods.createForm.addBrandAndSelectButton' })}
                </Button>
              </Flex>
            ),
          }}
        />
      </ProForm>
    </Modal>
  );
}
