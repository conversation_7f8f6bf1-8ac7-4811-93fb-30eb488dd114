import { Image, Space } from 'antd';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import { useState } from 'react';
export interface ImageListProps {
  urls?: string[];
  width?: number;
  itemName?: ReactNode;
}

export default (props: ImageListProps) => {
  const { urls = [], width = 26, itemName = '' } = props;
  const [visible, setVisible] = useState(false);

  return (
    <Image.PreviewGroup
      items={urls}
      preview={{
        visible,
        onVisibleChange: (value) => {
          setVisible(value);
        },
      }}
    >
      <Space align="center" size={8}>
        {!isEmpty(urls) && <Image width={width} height={width} src={urls?.[0]} />}
        {itemName}
      </Space>
    </Image.PreviewGroup>
  );
};
