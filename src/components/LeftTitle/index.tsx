import { ReactNode } from 'react';
import classNames from 'classnames';

export default (props: { title: ReactNode; extra?: ReactNode; className?: string }) => {
  const { title, extra, className } = props;
  return (
    <div className={classNames('flex flex-row items-center justify-between', className)}>
      <div className="flex items-center">
        <span className="h-[12px] w-[4px] bg-primary mr-1.5" />
        <span className="font-semibold text-lg">{title}</span>
      </div>
      {extra}
    </div>
  );
};
