import Checked from '../../assets/icons/checked.svg';

const Selector: React.FC<{
    options: {
        key: string | number;
        label: string;
        icon?: React.FC<{ size: number }>;
    }[];
    activeKey: string | number;
    onSelect: (key: string | number) => void;
    className?: string;
    isFullWidth?: boolean;
    size: 'small' | 'middle' | 'large';
}> = ({ options, activeKey, onSelect, className = '', isFullWidth = false, size = 'middle' }) => {


    const handleSelect = (key: string) => {
        onSelect(key);
    };

    return (
        <div className={`flex gap-4 ${className} ${isFullWidth ? 'w-full justify-between' : ''}`}>
            {
                options.map(item => {
                    const isActive = activeKey === item.key;
                    return (
                        <div key={item.key}
                            className={`
                                cursor-pointer relative text-center
                                border-solid border-1 border
                                hover:border-primary hover:bg-primary-light transition-colors duration-200
                                ${isActive ? 'border-primary bg-primary-light' : 'border-gray-200'}
                                ${isFullWidth ? 'flex-1' : ''}
                                ${size === 'middle' ? 'px-[15px] py-[6px] rounded' : ''}
                                ${size === 'large' ? 'px-[30px] py-[14px] rounded text-lg font-semibold' : ''}
                                `
                            }
                            onClick={() => handleSelect(item.key)}
                        >
                            {item.icon && <img src={item.icon} alt="icon" className="w-4 h-4 mr-2" />}
                            <span>
                                {item.label}
                            </span>
                            {isActive && <span className="absolute left-0 top-0 w-5 h-5"
                                style={{
                                    backgroundImage: `url(${Checked})`,
                                    backgroundSize: 'cover'
                                }}
                            />}
                        </div>
                    )
                })
            }
        </div>

    );
};


export default Selector;