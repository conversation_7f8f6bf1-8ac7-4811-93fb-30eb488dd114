import { PayKind, payKindOptions } from '@/components/PaymentForm/types/PayKind';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-components';
import { ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import type { FormListFieldData, FormListOperation } from 'antd';
import { ConfigProvider, Tooltip } from 'antd';
import React, { useState, useImperativeHandle, forwardRef } from 'react';
import styles from './index.module.scss';
import { defaultTo } from 'lodash';
import { useIntl } from 'umi';
import {
  AdvanceAmountList,
  CustomerEntity,
  ReceivableAmountList,
} from '@/pages/customer/list/types/CustomerEntity';
import type { MemberAccountEntity } from '@/pages/finance/customer/types/MemberAccountEntity';
import { useDebounceEffect } from 'ahooks';
import _ from 'lodash';

export interface PaymentFormProps {
  cstDetail?: CustomerEntity;
  storeId?: string; // 门店ID
  currency?: string; // 币种
}

export const ADVANCE_ACCOUNT = 'ADVANCE_ACCOUNT';

export interface GetAccountListProps {
  list?: MemberAccountEntity[];
  cstDetail?: CustomerEntity;
  currency?: string;
}

export const getAccountList = (
  props: GetAccountListProps,
): { list: any[]; canUseAdvance: number } => {
  const { list = [], cstDetail, currency = 'AUD' } = props;

  if (!cstDetail) {
    return { list: [], canUseAdvance: 0 };
  }

  // 计算当前币种可用预收余额
  const advance = cstDetail?.settle?.advanceAmountList?.find(
    (item: AdvanceAmountList) => item.currency === currency,
  )?.amount;
  const receivable = cstDetail?.settle?.receivableAmountList?.find(
    (item: ReceivableAmountList) => item.currency === currency,
  )?.amount;
  const canUseAdvance = _.round(_.subtract(advance ?? 0, receivable ?? 0), 2);

  console.log('advance', advance);
  console.log('canUseAdvance', canUseAdvance);

  let result: any[] = [
    {
      label: '预收余额',
      value: ADVANCE_ACCOUNT,
    },
  ];
  if (list?.length > 0) {
    result = result.concat(
      list.map((item) => ({
        label: item.memberAccountName,
        value: item.id,
      })),
    );
  }
  return { list: result, canUseAdvance };
};

const PaymentForm = forwardRef((props: PaymentFormProps, ref) => {
  const { cstDetail, storeId, currency } = props;
  const intl = useIntl();
  const [accountList, setAccountList] = useState<any[]>([]);

  const advanceInfo = cstDetail?.settle?.advanceAmountList?.find(
    (item: AdvanceAmountList) => item.currency === currency,
  ) as AdvanceAmountList;
  const receivable = cstDetail?.settle?.receivableAmountList?.find(
    (item: ReceivableAmountList) => item.currency === currency,
  ) as ReceivableAmountList;

  useDebounceEffect(
    () => {
      if (storeId && cstDetail && currency) {
        queryMemberAccountPage({
          belongToStore: [storeId],
        }).then((result) => {
          // @ts-ignore
          const account = getAccountList({ list: result.data, cstDetail, currency });
          setAccountList(account.list);
        });
      }
    },
    [storeId, cstDetail, currency],
    { wait: 50 },
  );

  /**
   * 对外暴露
   */
  useImperativeHandle(
    ref,
    () => {
      return {
        getState() {
          return { accountList };
        },
      };
    },
    [accountList],
  );

  /**
   * 自定义按钮
   * @param field
   * @param action
   * @param defaultActionDom
   * @param count
   */
  const actionRender = (
    field: FormListFieldData,
    action: FormListOperation,
    defaultActionDom: React.ReactNode[],
    count: number,
  ) => {
    const btn = [];
    switch (count) {
      case 1:
        btn.push(
          <Tooltip title="新增一行">
            <PlusOutlined className="cursor-pointer ml-2" onClick={() => action.add()} />
          </Tooltip>,
        );
        break;
      default:
        btn.push(
          <Tooltip title="删除此行">
            <DeleteOutlined
              className="cursor-pointer ml-2"
              onClick={() => action.remove(field.name)}
            />
          </Tooltip>,
        );
        break;
    }
    return btn;
  };

  return (
    <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
      <ProFormSelect
        label={
          <div>
            <span className="font-semibold">结算方式</span>
            {Boolean(advanceInfo) && (
              <span className="ml-2 text-[12px] text-gray-500">
                (预收余额: {advanceInfo?.currencySymbol}
                {advanceInfo?.amount}, 应收: {receivable?.currencySymbol}
                {receivable?.amount})
              </span>
            )}
          </div>
        }
        name="payKind"
        options={payKindOptions(cstDetail?.settle?.credit === true, cstDetail?.settle?.status!)}
        allowClear={false}
      />
      <ProFormDependency name={['payKind']}>
        {({ payKind }) => {
          if (payKind === PayKind.Credit) {
            return (
              <div className="text-gray-500 availableAmount">
                {intl.formatMessage({ id: 'sales.order.edit.available' })}$
                {defaultTo(cstDetail?.settle?.availableAmount, '-')}
              </div>
            );
          }
          if (payKind === PayKind.Cash) {
            return (
              <ProFormList
                name="payDetailList"
                max={2}
                min={1}
                initialValue={[{}]}
                creatorButtonProps={false}
                actionRender={actionRender}
                className={styles.group}
              >
                <ProFormGroup key="group">
                  <div className="flex">
                    <ProFormSelect
                      name="payeeAcount"
                      options={accountList}
                      placeholder="请选择账户"
                      width={120}
                      allowClear={false}
                    />
                    <ProFormDigit
                      min={0}
                      name="payAmount"
                      fieldProps={{
                        controls: false,
                        precision: 2,
                        addonAfter: '元',
                      }}
                      allowClear={false}
                    />
                  </div>
                </ProFormGroup>
              </ProFormList>
            );
          }
        }}
      </ProFormDependency>
    </ConfigProvider>
  );
});

export default PaymentForm;
