export enum PayKind {
  Cash = 1, // 现款
  Credit, // 挂账
}

export enum PayKindName {
  Cash = '现款',
  Credit = '挂账',
}

export const payKindOptions = (canCredit: boolean, creditStatus: number) => {
  const options: any[] = [
    {
      label: PayKindName.Cash,
      value: PayKind.Cash,
    },
  ];
  if (canCredit) {
    options.push({
      label: PayKindName.Credit,
      value: PayKind.Credit,
      disabled: [2, 3].includes(creditStatus),
    });
  }
  return options;
};
