import { getTagList } from "@/pages/customer/list/services";
import { ProFormSelect } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";


const ProFormCustomerTag = (props) => {
    const intl = useIntl();

    return (
        <ProFormSelect
            labelAlign="left"
            debounceTime={300}
            label={intl.formatMessage({ id: 'customer.customerProperty.tab.customerTag' })}
            showSearch={true}
            fieldProps={{
                filterOption: false,
                mode: 'multiple',
            }}
            request={(query) =>
                getTagList({ tagNameWord: query.keyWords, tagStatus: 0, tagType: 1 }).then((result) => {
                    const list = result.map((item) => ({ label: item.tagName, value: item.id }));
                    return list;
                })
            }
            {...props}
        />

    )
}

export default ProFormCustomerTag;
