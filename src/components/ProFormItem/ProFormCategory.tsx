import { queryGoodsPropertyPage } from "@/pages/goods/property/services";
import { transformCategoryTree } from "@/utils/transformCategoryTree";
import { ProFormTreeSelect } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";

const ProFormCategory = (props) => {
    const inlt = useIntl();
    return (
        <ProFormTreeSelect
            name={props.name || "categoryIdList"}
            label={props.label || inlt.formatMessage({ id: 'common.field.category' })}
            placeholder={inlt.formatMessage({ id: 'common.placeholder.select' })}
            fieldProps={{
                style: { width: '100%' },
                treeCheckable: true,
                maxTagCount: 3,
                filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
            }}
            request={() => {
                return queryGoodsPropertyPage(
                    { pageSize: 999, pageNo: 1, isReturnTree: true },
                    'category',
                ).then((result) => transformCategoryTree(result.data));
            }}
            {...props}
        />
    )
}

export default ProFormCategory