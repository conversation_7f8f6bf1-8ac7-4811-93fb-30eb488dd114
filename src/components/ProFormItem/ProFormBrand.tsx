import { queryGoodsPropertyPage } from "@/pages/goods/property/services";
import { ProFormSelect } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";

const ProFormBrand = (props) => {
    const inlt = useIntl();
    return (
        <ProFormSelect
            name={props.name || "brandIdList"}
            label={props.label || inlt.formatMessage({ id: 'common.field.brand' })}
            mode="multiple"
            placeholder={inlt.formatMessage({ id: 'common.placeholder.select' })}
            fieldProps={{ style: { width: '100%' } }}
            request={async ({ keyWords: brandName }) => {
                const { data } = await queryGoodsPropertyPage(
                    { brandName, pageNo: 1, pageSize: 1000 },
                    'brand',
                );
                return data.map((t) => ({
                    label: t.brandName,
                    dataType: t.dataType,
                    value: t.brandId,
                }));
            }}
            {...props}
        />
    )
}

export default ProFormBrand