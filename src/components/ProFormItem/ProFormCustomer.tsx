import { getCstList } from "@/pages/customer/list/services";
import { REQUIRED_RULES } from "@/utils/RuleUtils";
import { ProFormSelect, ProFormSelectProps } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";


interface ProFormCustomerProps extends ProFormSelectProps {
    valueKey?: string;
}
export const ProFormCustomerList = (props: ProFormCustomerProps) => {
    const { valueKey = 'cstId', ...rest } = props;
    const intl = useIntl();

    return (
        <ProFormSelect
            labelAlign="left"
            debounceTime={300}
            label={intl.formatMessage({ id: 'sales.order.edit.customerName' })}
            placeholder={intl.formatMessage({ id: 'sales.order.edit.selectCustomer' })}
            name="cstId"
            showSearch={true}
            rules={[REQUIRED_RULES]}
            fieldProps={{
                filterOption: false,
                mode: 'multiple',
            }}
            request={(query) =>
                getCstList({ keyword: query.keyWords, cstStatus: 0 }).then((result) => {
                    const list = result.map((item) => ({ label: item.cstName, value: item[valueKey] }));
                    return list;
                })
            }
            {...rest}
        />

    )
}
