import { PlusOutlined } from "@ant-design/icons";
import { createField } from "@ant-design/pro-form/lib/BaseForm";
import { useIntl } from "@umijs/max";
import { Image, Upload, UploadChangeParam, UploadFile, UploadProps } from "antd";
import React, { useEffect, useState } from "react";

interface ProFormUploadSingleCardProps {
    value?: UploadFile[]; // ProForm.Item passes the field value
    onChange?: (fileList: UploadFile[] | undefined) => void; // ProForm.Item expects onChange to update the field value
    // Include other props that ProForm.Item might pass or are needed for the component
    label?: React.ReactNode;
    name?: string;
}

const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
    });

export const ProFormUploadSingleCard = createField(React.forwardRef<unknown, ProFormUploadSingleCardProps>((props) => {
    const intl = useIntl();
    const [fileList, setFileList] = useState<UploadFile[]>(props.value ?? []);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');

    useEffect(() => {
        if (Array.isArray(props.value)) {
            setFileList(props.value);
        } else {
            setFileList([]);
        }
    }, [props.value]);

    const uploadButton = (
        <div className="flex flex-col items-center justify-center text-gray-500">
            <PlusOutlined />
            <div className="mt-2 text-sm">{intl.formatMessage({ id: 'shop.activity.uploadImage' })}</div>
        </div>
    );
    // Accept UploadChangeParam and extract newFileList
    const handleImageChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
        const newFileList = info.fileList;
        setFileList(newFileList);
        props.onChange?.(newFileList);
    };


    const handlePreview = async (file: UploadFile) => {
        // Use optional chaining for originFileObj
        if (!file.url && !file.preview) {
            // Use the global File type
            file.preview = await getBase64(file.originFileObj as File);
        }

        setPreviewImage(file.url || (file.preview as string));
        setPreviewOpen(true);
    };


    return (
        <>
            <Upload
                listType="picture-card"
                maxCount={1} // 只允许上传一张图片
                multiple={false}
                accept=".jpg,.png,.jpeg,.gif,.webp"
                action="/apigateway/public/upload/object/batch"
                onChange={handleImageChange}
                fileList={fileList}
                onPreview={handlePreview}
            >
                {fileList && fileList.length > 0 ? null : uploadButton}
            </Upload>
            {
                previewImage && (
                    <Image
                        wrapperStyle={{ display: 'none' }}
                        preview={{
                            visible: previewOpen,
                            onVisibleChange: (visible) => setPreviewOpen(visible),
                            afterOpenChange: (visible) => !visible && setPreviewImage(''),
                        }}
                        src={previewImage}
                    />
                )
            }
        </>
    )
})) 