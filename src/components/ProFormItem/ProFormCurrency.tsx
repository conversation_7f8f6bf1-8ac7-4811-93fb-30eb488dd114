import { queryAllCurrency } from '@/pages/finance/tag/services';
import {
  ProForm,
  ProFormDependency,
  ProFormItem,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { InputNumber } from 'antd';


interface ProFormCurrencyProps {
  disabled?: boolean;
  fieldsName?: {
    currency: string;
    rate: string;
  };
  onChange?: (value: {
    [x: string]: any;
    currencySymbol?: any;
  }) => void;
  label?: string;
}
const ProFormCurrency = (props: ProFormCurrencyProps) => {
  const { disabled, fieldsName = {
    currency: 'currency',
    rate: 'rate',
  }, onChange, label, ...rest } = props;

  const form = ProForm.useFormInstance();
  const intl = useIntl();
  const t = (id) => intl.formatMessage({ id });

  return (
    <ProForm.Group
      {...rest}
      size={0}
      spaceProps={{
        style: {
          flexWrap: 'nowrap',
        },
      }}
    >
      <div className="flex-1">
        <ProFormSelect
          name={fieldsName.currency}
          label={label ?? t('common.field.currency')}
          allowClear={false}
          disabled={disabled}
          request={async (query) => {
            const data = await queryAllCurrency({ currencyName: query.keyWords });
            const result = data?.map(({ targetCurrency, rate, currencySymbol }) => ({
              value: targetCurrency,
              label: targetCurrency,
              rate: rate,
              currencySymbol,
            }));
            const defaultValue = form.getFieldValue(fieldsName.currency);
            if (!defaultValue) {
              form.setFieldsValue({
                [fieldsName.currency]: result[0]?.value,
                currencySymbol: result[0]?.currencySymbol,
                [fieldsName.rate]: result[0]?.rate,
              });
            }
            return result;
          }}
          onChange={(value, option) => {
            console.log(value, option);
            form.setFieldsValue({ [fieldsName.rate]: option.rate, currencySymbol: option.currencySymbol });
            onChange?.({
              [fieldsName.currency]: option.value,
              [fieldsName.rate]: option.rate,
              currencySymbol: option.currencySymbol
            });
          }}
          rules={props.rules}
        />
      </div>
      <ProFormText name="currencySymbol" hidden />
      <div className="flex-1">
        <ProFormDependency name={[fieldsName.currency]}>
          {({ currency }) => {
            return (
              <ProFormItem label=" " colon={false} name={fieldsName.rate}>
                <InputNumber
                  addonBefore={t('common.field.rate')}
                  disabled={disabled || currency == 'AUD'}
                  width={100}
                  onChange={(value) => {
                    onChange?.({
                      [fieldsName.rate]: value,
                    });
                  }}
                />
              </ProFormItem>
            );
          }}
        </ProFormDependency>
      </div>
    </ProForm.Group>
  );
};

export default ProFormCurrency;
