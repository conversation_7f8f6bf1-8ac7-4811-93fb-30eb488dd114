import { ProFormDatePicker, ProFormSelect } from "@ant-design/pro-components";
import React from "react";

// 1-年度 2-月度
export enum PerformanceDateType {
  Year = 1,
  Month = 2,
}

const ProFormPerformanceDate = (props) => {

  const [type, setType] = React.useState<PerformanceDateType>();
  return <div className="flex">
    <div className="flex-1">
      <ProFormSelect
        name={'type'}
        width={'100%'}
        label={props.label}
        options={[
          { label: '年度', value: PerformanceDateType.Year },
          { label: '月度', value: PerformanceDateType.Month },
        ]}
        onChange={(value) => {
          setType(value);
          props.form.setFieldsValue({
            dateMonth: undefined,
          });
        }}
      />
    </div>
    <div className="flex-1">
      {
        type === PerformanceDateType.Year && <ProFormDatePicker.Year name={'dateMonth'} width={200}
          fieldProps={{
            format: 'YYYY',
            onChange: (date) => {
              props.form.setFieldsValue({
                dateMonth: date.format('YYYY'),
              });
            },
          }}
        />
      }
      {
        type === PerformanceDateType.Month && <ProFormDatePicker.Month name={'dateMonth'} width={200}
          fieldProps={{
            format: 'YYYY-MM',
            onChange: (date) => {
              props.form.setFieldsValue({
                dateMonth: date.format('YYYY-MM'),
              });
            },
          }}
        />
      }
    </div>
  </div>;
};

export default ProFormPerformanceDate;
