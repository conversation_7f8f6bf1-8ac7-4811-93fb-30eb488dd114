import { FormInstance, ProFormDatePicker, ProFormSelect } from "@ant-design/pro-components";
import { FormattedMessage } from "@umijs/max";
import React from "react";

// 1-年度 2-月度
export enum PerformanceDateType {
  Year = 1,
  Month = 2,
}

interface ProFormPerformanceDateProps {
  typeName?: string;
  dateName?: string;
  form: FormInstance<any>;
}
const ProFormPerformanceDate = (props: ProFormPerformanceDateProps) => {
  const { typeName = 'type', dateName = 'dateMonth' } = props;

  const [type, setType] = React.useState<PerformanceDateType>();
  return <div className="flex">
    <div className="flex-1">
      <ProFormSelect
        name={typeName}
        width={'100%'}
        label={props.label}
        options={[
          { label: <FormattedMessage id="finance.budget.cycle.annual" />, value: PerformanceDateType.Year },
          { label: <FormattedMessage id="finance.budget.cycle.monthly" />, value: PerformanceDateType.Month },
        ]}
        onChange={(value) => {
          setType(value);
          props.form?.setFieldsValue({
            [dateName]: undefined,
          });
        }}
      />
    </div>
    <div className="flex-1">
      {
        type === PerformanceDateType.Year && <ProFormDatePicker.Year name={dateName} width={200}
          fieldProps={{
            format: 'YYYY',
            onChange: (date) => {
              props.form?.setFieldsValue({
                [dateName]: date.format('YYYY'),
              });
            },
          }}
        />
      }
      {
        type === PerformanceDateType.Month && <ProFormDatePicker.Month name={dateName} width={200}
          fieldProps={{
            format: 'YYYY-MM',
            onChange: (date) => {
              props.form.setFieldsValue({
                [dateName]: date.format('YYYY-MM'),
              });
            },
          }}
        />
      }
    </div>
  </div>;
};

export default ProFormPerformanceDate;
