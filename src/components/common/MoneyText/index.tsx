import _, { isNumber } from 'lodash';

const MoneyText = (props: { text: number | string | undefined }) => {
  if (isEmptyOrNil(props.text)) {
    return '-';
  }
  return <span>{'$' + _.round(Number(props.text), 2).toFixed(2)}</span>;
};

export const MoneyFormat = (props: { money: number }) => {
  const { money } = props;
  if (!money) return <span>-</span>;
  if (isNumber(money)) {
    return <span>{money.toFixed(2)}</span>;
  }
  return <span>{money}</span>;
};

const isEmptyOrNil = (value: any) => {
  return _.isNil(value) || (_.isString(value) && _.isEmpty(value));
};
export default MoneyText;
