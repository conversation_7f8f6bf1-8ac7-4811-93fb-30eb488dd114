import type { PageRequestParamsType } from '@/types/PageRequestParamsType';

export interface QueryPurchaseGoodsPageRequest extends PageRequestParamsType {
  /**
   * 查询条件-创建时间开始
   */
  beginOrderTime?: string;
  /**
   * 查询条件-创建时间结束
   */
  endOrderTime?: string;
  /**
   * etc零售商id
   */
  etcMemberId?: string;
  /**
   * 商品名称（模糊）商品编码（精确）OE（模糊）供应商编码（模糊）查询
   */
  itemInfo?: string;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 操作人工号
   */
  operatorNo?: string;
  /**
   * 单个销售单号，精确匹配
   */
  orderNo?: string;
  /**
   * 多个销售单状态，精确匹配
   */
  orderStatusList?: number[];
  /**
   * 1平台采购2外部采购
   */
  queryType?: number;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
}
