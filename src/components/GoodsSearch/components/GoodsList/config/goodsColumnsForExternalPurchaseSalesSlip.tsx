import ColumnRender from '@/components/ColumnRender';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';
import type { PurchaseGoodsEntity } from '../types/purchase.goods.entity';

export interface GoodsColumnsForExternalPurchaseSalesSlip {
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemlist: any[]) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
  /**
   * 国际化对象
   */
  intl: any;
}

export const goodsColumnsForExternalPurchaseSalesSlip = (
  props: GoodsColumnsForExternalPurchaseSalesSlip,
) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.orderNo' }),
      dataIndex: 'orderNo',
      width: 160,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.createTime' }),
      dataIndex: 'orderCreateTime',
      width: 140,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.storeName' }),
      dataIndex: 'storeName',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.warehouseName' }),
      dataIndex: 'warehouseName',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.itemSn' }),
      dataIndex: 'itemSn',
      width: 140,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.itemName' }),
      dataIndex: 'itemName',
      width: 180,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.oeNos' }),
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandName' }),
      dataIndex: 'brandName',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.categoryName' }),
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.unitName' }),
      dataIndex: 'unitName',
      width: 50,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.saleNum' }),
      dataIndex: 'saleNum',
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.avaNum' }),
      dataIndex: 'avaNum',
      width: 80,
      editable: false,
      render: (text, record: PurchaseGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId!],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.lowerLimit' }),
      dataIndex: 'lowerLimit',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.upperLimit' }),
      dataIndex: 'upperLimit',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchasePrice' }),
      dataIndex: 'purchasePrice',
      width: 80,
      editable: false,
      valueType: 'money',
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseUnitPrice' }),
      dataIndex: 'price',
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0.01}
            max={MAX_AMOUNT}
            controls={false}
            precision={2}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.record?.itemSn as string)}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseQuantity' }),
      dataIndex: 'number',
      width: 120,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={config.record?.minOrderNum ?? 1}
            precision={0}
            max={MAX_COUNT}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.record?.itemSn as string)}
            onPressEnter={() => props.handleAdd([config.record as PurchaseGoodsEntity])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.action' }),
      width: 60,
      editable: false,
      fixed: 'right',
      align: 'center',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes((row as PurchaseGoodsEntity).itemSn)}
          onClick={() => props.handleAdd([row as PurchaseGoodsEntity])}
        >
          {props.intl.formatMessage({ id: 'goods.search.table.add' })}
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
