import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';
import { GoodsDetailDrawerProps, GoodsDetailType } from '@/components/GoodsDetailDrawer';

export interface GoodsColumnsForSalesReturn {
  cstId?: string;
  storeId?: string;
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;

  /**
   * 查看商品详情
   */
  setGoodsDrawer: (data: GoodsDetailDrawerProps) => void;

  /**
   * 国际化对象
   */
  intl: any;
}

export const goodsColumnsForSalesReturn = (props: GoodsColumnsForSalesReturn) =>
  [
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.skuName' }),
      dataIndex: 'skuName',
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.itemSn' }),
      dataIndex: 'itemSn',
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandName' }),
      dataIndex: 'brandName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.avaNum' }),
      dataIndex: 'avaNum',
      width: 60,
      editable: false,
      render: (text, record) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.setGoodsDrawer({
              visible: true,
              groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
              type: GoodsDetailType.LocalStock,
              itemId: record.itemId,
              storeId: props.storeId,
              cstId: props.cstId,
              suggestPrice: record.suggestPrice,
              lowPrice: record.lowPrice,
              costPrice: record.costPrice,
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.suggestPrice' }),
      dataIndex: 'suggestPrice',
      width: 80,
      valueType: 'money',
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.refundAmount' }),
      dataIndex: 'price',
      width: 100,
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0.01}
            max={MAX_AMOUNT}
            controls={false}
            precision={2}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.returnQuantity' }),
      dataIndex: 'number',
      width: 100,
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={1}
            max={MAX_COUNT}
            precision={0}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
            onPressEnter={() => props.handleAdd([config.record as StoreGoodsEntity])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.action' }),
      width: 60,
      editable: false,
      fixed: 'right',
      align: 'center',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes(row.itemSn)}
          onClick={() => props.handleAdd([row as StoreGoodsEntity])}
        >
          {props.intl.formatMessage({ id: 'goods.search.table.add' })}
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
