import { request } from '@/utils/request';
import { LastSalePriceRequest } from './types/last.sale.price.request';
import { LastSalePriceResponse } from './types/last.sale.price.response';
import { PurchaseRecordEntity } from './types/purchase.record.entity';

export const lastSalePrice = async (params: LastSalePriceRequest) => {
  return request<LastSalePriceResponse>(`/ipmsconsole/goods/price/lastSalePrice`, {
    data: params,
  });
};

export const queryPurchaseRecord = async (itemId: string) => {
  return request<PurchaseRecordEntity[]>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/queryPurchaseRecord`,
    {
      data: { itemId },
    },
  );
};
