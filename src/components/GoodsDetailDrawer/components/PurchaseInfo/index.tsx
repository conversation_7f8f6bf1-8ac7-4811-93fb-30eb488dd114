import { queryPurchaseRecord } from '../../services';
import { PurchaseRecordEntity } from '../../types/purchase.record.entity';
import FunProTable from '@/components/common/FunProTable';
import { useIntl } from '@umijs/max';
import { useEffect, useState } from 'react';
import { Card } from 'antd';
import LeftTitle from '@/components/LeftTitle';

export interface PurchaseInfoProps {
  itemId?: string;
}

const PurchaseInfo = (props: PurchaseInfoProps) => {
  const intl = useIntl();
  const { itemId } = props;
  const [detail, setDetail] = useState<PurchaseRecordEntity[]>();

  useEffect(() => {
    if (itemId) {
      queryPurchaseRecord(itemId).then((result) => {
        if (result) {
          // @ts-ignore
          setDetail(result);
        }
      });
    }
  }, [itemId]);

  console.log('queryPurchaseRecord', detail);

  return (
    <div className="!bg-gray-100 -m-[16px] p-[16px] flex flex-col gap-4">
      <Card className="border-none rounded-xl">
        <LeftTitle title={intl.formatMessage({ id: 'common.priceInfoDrawer.lastTenPurchases' })} />
        <FunProTable
          dataSource={detail}
          ghost={true}
          search={false}
          pagination={false}
          options={false}
          scroll={{ x: true }}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseOrderNo' }),
              dataIndex: 'orderNo',
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.supplier' }),
              dataIndex: 'supplierName',
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseQuantity' }),
              dataIndex: 'sumQuantity',
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchasePrice' }),
              valueType: 'money',
              dataIndex: 'sumAmount',
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseTime' }),
              dataIndex: 'orderTime',
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default PurchaseInfo;
