import { lastSalePrice } from '../../services';
import type { LastSalePriceResponse } from '../../types/last.sale.price.response';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useEffect, useState } from 'react';
import { Card } from 'antd';
import LeftTitle from '@/components/LeftTitle';

export interface PriceInfoProps {
  itemId?: string;
  cstId?: string;
  storeId?: string;
  suggestPrice?: number;
  lowPrice?: number;
  costPrice?: number;
}

const PriceInfo = (props: PriceInfoProps) => {
  const intl = useIntl();
  const { itemId, cstId, storeId, suggestPrice, lowPrice, costPrice } = props;
  const [detail, setDetail] = useState<LastSalePriceResponse>();

  const priceInfo = [
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.salePrice' }),
      value: suggestPrice,
    },
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.lowestPrice' }),
      value: lowPrice,
    },
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.costPrice' }),
      value: costPrice,
    },
  ];

  useEffect(() => {
    if (itemId && storeId) {
      lastSalePrice({ itemId, storeId, cstId }).then((result) => {
        if (result) {
          // @ts-ignore
          setDetail(result);
        }
      });
    }
  }, [itemId, storeId, cstId]);

  return (
    <div className="!bg-gray-100 -m-[16px] p-[16px] flex flex-col gap-4">
      <Card className="border-none rounded-xl">
        <LeftTitle title={intl.formatMessage({ id: 'common.priceInfoDrawer.goodsPrice' })} />
        <div className="grid grid-cols-3 gap-4 mt-5">
          {priceInfo.map((item) => (
            <ProCard key={item.label} bordered={true}>
              <div className="text-2xl text-[#f83331]">
                <MoneyText text={item.value} />
              </div>
              <div className="text-gray-500 ml-2">{item.label}</div>
            </ProCard>
          ))}
        </div>
      </Card>
      {cstId && (
        <Card className="border-none rounded-xl">
          <LeftTitle
            title={intl.formatMessage({ id: 'common.priceInfoDrawer.customerLastFivePrices' })}
          />
          <FunProTable
            ghost={true}
            search={false}
            options={false}
            scroll={{ x: true }}
            pagination={false}
            dataSource={detail?.cstLastFiveSalePrice ?? []}
            columns={[
              {
                title: intl.formatMessage({ id: 'common.priceInfoDrawer.orderNo' }),
                dataIndex: 'orderNo',
                width: 160,
              },
              {
                title: intl.formatMessage({ id: 'common.priceInfoDrawer.price' }),
                dataIndex: 'salePrice',
                valueType: 'money',
                width: 80,
              },
              {
                title: intl.formatMessage({ id: 'common.priceInfoDrawer.quantity' }),
                dataIndex: 'saleNum',
                width: 80,
              },
              {
                title: intl.formatMessage({ id: 'common.priceInfoDrawer.time' }),
                valueType: 'money',
                dataIndex: 'sortTime',
              },
            ]}
          />
        </Card>
      )}
      {/*<Card className="border-none rounded-xl">*/}
      {/*  <LeftTitle*/}
      {/*    title={intl.formatMessage({ id: 'common.priceInfoDrawer.storeLastFivePrices' })}*/}
      {/*  />*/}
      {/*  <FunProTable*/}
      {/*    ghost={true}*/}
      {/*    search={false}*/}
      {/*    scroll={{ x: true }}*/}
      {/*    options={false}*/}
      {/*    pagination={false}*/}
      {/*    dataSource={detail?.storeLastFiveSalePrice ?? []}*/}
      {/*    columns={[*/}
      {/*      {*/}
      {/*        title: intl.formatMessage({ id: 'common.priceInfoDrawer.orderNo' }),*/}
      {/*        dataIndex: 'orderNo',*/}
      {/*        width: 160,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        title: intl.formatMessage({ id: 'common.priceInfoDrawer.price' }),*/}
      {/*        dataIndex: 'salePrice',*/}
      {/*        valueType: 'money',*/}
      {/*        width: 80,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        title: intl.formatMessage({ id: 'common.priceInfoDrawer.quantity' }),*/}
      {/*        dataIndex: 'saleNum',*/}
      {/*        width: 80,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        title: intl.formatMessage({ id: 'common.priceInfoDrawer.customerName' }),*/}
      {/*        dataIndex: 'cstName',*/}
      {/*        width: 220,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        title: intl.formatMessage({ id: 'common.priceInfoDrawer.time' }),*/}
      {/*        valueType: 'money',*/}
      {/*        dataIndex: 'sortTime',*/}
      {/*      },*/}
      {/*    ]}*/}
      {/*  />*/}
      {/*</Card>*/}
    </div>
  );
};

export default PriceInfo;
