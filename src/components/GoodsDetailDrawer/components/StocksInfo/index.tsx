import FunProTable from '@/components/common/FunProTable';
import { ProCard, ProForm, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Card, Form, message, Tabs, Tag } from 'antd';
import { useEffect, useState } from 'react';
import { queryStockRecord } from './services';
import type { QueryPurchaseRecordRequest } from './types/query.purchase.record.request';
import {
  QueryPurchaseRecordResponse,
  StockInventoryRo,
} from './types/query.purchase.record.response';

import LeftTitle from '@/components/LeftTitle';
import { TransferBillType } from '@/pages/stocks/transfer/list/types/TransferStatusEnum';
import { transferCreateOrUpdatePost } from '@/pages/stocks/transfer/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { history } from '@@/core/history';
import { InfoCircleOutlined } from '@ant-design/icons';
import blueIcon from './imgs/blue.svg';
import greenIcon from './imgs/green.svg';
import redIcon from './imgs/red.svg';

export interface StocksInfoProps extends QueryPurchaseRecordRequest {
  warehouseId?: string;
  storeId?: string;
  saleOrderNo?: string;
}

const StocksInfo = (props: StocksInfoProps) => {
  const intl = useIntl();
  const { warehouseId, storeId, saleOrderNo, ...rest } = props;
  const [transferFrom] = Form.useForm();
  const [detail, setDetail] = useState<QueryPurchaseRecordResponse>();
  const [formWarehouse, setFromWarehouse] = useState<any>([]);
  const [toWarehouse, setToWarehouse] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  const showTransfer = Boolean(saleOrderNo);

  useEffect(() => {
    if (rest.itemIdList?.length) {
      queryStockRecord(rest).then((result) => {
        if (result) {
          // @ts-ignore
          setDetail(result);
        }
      });
    }
  }, [rest.itemIdList]);

  useEffect(() => {
    if (detail && warehouseId) {
      const toItem = detail.stockInventoryRos?.find((item) => item.warehouseId === warehouseId);
      if (toItem) {
        setToWarehouse([{ label: toItem.warehouseName, value: toItem.warehouseId }]);
        transferFrom.setFieldValue('warehouseIdIn', toItem.warehouseId);
      }
    }
  }, [detail, warehouseId]);

  if (!detail) {
    return null;
  }

  const stocksInfo = [
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.totalInventory' }),
      value: detail?.totalInventoryNum ?? 0,
      icon: blueIcon,
    },
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.occupiedInventory' }),
      value: detail?.totalLockedNum ?? 0,
      icon: redIcon,
    },
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.availableInventory' }),
      value: detail?.totalAvaNum ?? 0,
      icon: greenIcon,
    },
  ];

  const handleTransfer = (values: any) => {
    setLoading(true);
    const { transferNum, ...rest } = values;
    const storeIdOut = detail?.stockInventoryRos?.find(
      (item) => item.warehouseId === values.warehouseIdOut,
    )?.storeIdList?.[0];
    transferCreateOrUpdatePost({
      ...rest,
      storeIdOut,
      storeIdIn: storeId,
      billType: TransferBillType.SALE_TRANSFER,
      origBillNo: saleOrderNo,
      stockTransferDetailCmdList: props.itemIdList?.map((item) => ({ itemId: item, transferNum })),
    })
      .then((data) => {
        if (data) {
          message.success(`创建调拨成功`);
          history.push(
            '/purchase/transfer/operation?transferId=' + data.id + '&bizBillNo=' + data.bizBillNo,
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="!bg-gray-100 -m-[16px] p-[16px] flex flex-col gap-4">
      <ProCard gutter={20} className="rounded-xl">
        {stocksInfo.map((item) => (
          <ProCard key={item.label} bordered={true}>
            <div className="flex items-center">
              <img src={item.icon} width={40} height={40} />
              <div className="ml-4">
                <div className="text-2xl font-semibold">{item.value}</div>
                <div className="text-gray-500">{item.label}</div>
              </div>
            </div>
          </ProCard>
        ))}
      </ProCard>
      <Card className="border-none rounded-xl">
        <LeftTitle
          title={intl.formatMessage({ id: 'common.stocksInfoDrawer.stockDistribution' })}
        />
        <FunProTable
          search={false}
          scroll={{ x: true }}
          pagination={false}
          options={false}
          ghost={true}
          dataSource={detail?.stockInventoryRos ?? []}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.warehouseName' }),
              dataIndex: 'warehouseName',
              renderText: (text, record) => (
                <span>
                  {text}
                  {record.warehouseId === warehouseId ? (
                    <Tag color="orange" className="ml-2">
                      {intl.formatMessage({ id: 'common.stocksInfoDrawer.selected' })}
                    </Tag>
                  ) : (
                    ''
                  )}
                </span>
              ),
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.totalStock' }),
              dataIndex: 'inventoryNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.occupiedStock' }),
              dataIndex: 'lockedNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.availableStock' }),
              dataIndex: 'avaNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.purchaseInTransit' }),
              dataIndex: 'purchaseTransitNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.upperLimit' }),
              dataIndex: 'upperLimit',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.lowerLimit' }),
              dataIndex: 'lowerLimit',
            },
            ...(showTransfer
              ? [
                {
                  title: '操作',
                  render: (_text: any, record: StockInventoryRo) =>
                    record.warehouseId !== warehouseId && (
                      <a
                        onClick={() => {
                          setFromWarehouse([
                            { label: record.warehouseName, value: record.warehouseId },
                          ]);
                          transferFrom.setFieldValue('warehouseIdOut', record.warehouseId);
                          transferFrom.validateFields(['warehouseIdOut']);
                        }}
                      >
                        调货
                      </a>
                    ),
                },
              ]
              : []),
          ]}
        />
      </Card>
      {showTransfer && (
        <Card className="border-none rounded-xl" styles={{ body: { paddingTop: 0 } }}>
          <Tabs
            items={[
              {
                key: 'transfer',
                label: '调货申请',
                children: (
                  <div>
                    <div className="text-gray-500 flex gap-1 mb-3">
                      <InfoCircleOutlined />
                      From仓库由库存分布中点击调货自动选择
                    </div>
                    <ProForm
                      grid={true}
                      submitter={false}
                      form={transferFrom}
                      onFinish={handleTransfer}
                    >
                      <ProFormSelect
                        label="From"
                        name="warehouseIdOut"
                        rules={[REQUIRED_RULES]}
                        colProps={{ span: 8 }}
                        options={formWarehouse}
                        disabled={true}
                      />
                      <ProFormSelect
                        label="TO"
                        name="warehouseIdIn"
                        rules={[REQUIRED_RULES]}
                        options={toWarehouse}
                        colProps={{ span: 8 }}
                        disabled={true}
                      />
                      <ProFormDigit
                        fieldProps={{ precision: 0, min: 1 }}
                        name="transferNum"
                        rules={[REQUIRED_RULES]}
                        label="调货数量"
                        colProps={{ span: 8 }}
                      />
                      <div className="flex w-full justify-end">
                        <Button type={'primary'} loading={loading} htmlType={'submit'}>
                          确认调货
                        </Button>
                      </div>
                    </ProForm>
                  </div>
                ),
              },
              { key: 'purchase', label: '采购申请', children: <div>方案待定 @吴星驰</div> },
            ]}
          />
        </Card>
      )}
    </div>
  );
};

export default StocksInfo;
