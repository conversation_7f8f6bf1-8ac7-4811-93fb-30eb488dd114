import { request } from '@/utils/request';
import { QueryPurchaseRecordRequest } from '@/components/StocksInfoDrawer/types/query.purchase.record.request';
import { QueryPurchaseRecordResponse } from '@/components/StocksInfoDrawer/types/query.purchase.record.response';

export const queryStockRecord = async (params: QueryPurchaseRecordRequest) => {
  return request<QueryPurchaseRecordResponse>(`/ipmsconsole/stockinventory/queryList`, {
    data: params,
  });
};
