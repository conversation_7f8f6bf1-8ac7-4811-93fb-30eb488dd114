export interface QueryPurchaseRecordResponse {
  /**
   * None
   */
  memberId?: string;
  /**
   * 库存明细
   */
  stockInventoryRos?: StockInventoryRo[];
  /**
   * 总可用库存
   */
  totalAvaNum?: number;
  /**
   * 总账面库存
   */
  totalInventoryNum?: number;
  /**
   * 总占用库存
   */
  totalLockedNum?: number;
}

export interface StockInventoryRo {
  /**
   * 可用库存
   */
  avaNum?: number;
  storeIdList?: string[];
  warehouseName?: string;
  /**
   * 账面库存
   */
  inventoryNum?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 冻结库存
   */
  lockedNum?: number;
  /**
   * 安全库存下限
   */
  lowerLimit?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 采购在途
   */
  purchaseTransitNum?: number;
  /**
   * 调拨库存
   */
  transitNum?: number;
  /**
   * 安全库存上限
   */
  upperLimit?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
}
