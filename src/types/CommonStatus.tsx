import { FormattedMessage } from "@umijs/max";

/**
 * 状态 0-禁用  1-启用
 */
export const CommonStatusValueEnum = {
  0: { text: <FormattedMessage id="common.option.disable" />, status: 'Error' },
  1: { text: <FormattedMessage id="common.option.enable" />, status: 'Success' },
};
/**
 状态 0-否 1-是
 */
export const CommonYesNoValueEnum = {
  0: { text: <FormattedMessage id="common.option.no" />, status: 'Error' },
  1: { text: <FormattedMessage id="common.option.yes" />, status: 'Success' },
};
/**
 * 优惠类型
 */
export const DiscountTypeValueEnum = {
  0: { text: <FormattedMessage id="discount.type.none" />, status: 'Error' },
  1: { text: <FormattedMessage id="discount.type.wholeOrderDiscount" />, status: 'Success' },
  2: { text: <FormattedMessage id="discount.type.wholeOrderReduction" />, status: 'Default' },
};
/**
 * 付款方式
 */
export const PayWayValueEnum = {
  '0': { text: <FormattedMessage id="payment.method.account" />, status: 'Error' },
  '1': { text: <FormattedMessage id="payment.method.cash" />, status: 'Error' },
};
/**
 * 配送方式
 */
export const DeliverWayValueEnum = {
  0: { text: <FormattedMessage id="delivery.method.express" />, status: 'Error' },
};

/**
 * 查询类型
 */
export const SearchTypeValueEnum = {
  0: { text: <FormattedMessage id="search.type.exact" />, status: 'Error' },
  1: { text: <FormattedMessage id="search.type.inStockOnly" />, status: 'Success' },
};
/**
 * 0表示否定 1为肯定
 */
export type YesOrNoType = 0 | 1;

export const ReverseCommonStatusValueEnum = {
  0: { text: <FormattedMessage id="common.option.enable" />, status: 'Success' },
  1: { text: <FormattedMessage id="common.option.disable" />, status: 'Error' },
  2: { text: <FormattedMessage id="common.option.peddingAudit" />, status: 'Warning' },
};
/**逾期状态 */
export const OverdueStatusValueEnum = {
  1: { text: <FormattedMessage id="common.overdue.status.normal" />, status: 'Success' },
  2: { text: <FormattedMessage id="common.overdue.status.overdue" />, status: 'Error' },
  3: { text: <FormattedMessage id="common.overdue.status.disabled" />, status: 'Error' },
};
/**
 * 0:现款 10:挂账
 */
export enum RefundTypeEnum {
  /**
   * 现款
   */
  Cash = 0,
  /**
   * 挂账
   */
  Account = 10,
}
/**
 * 退款方式-现款&挂账
 */
export const RefundTypeValueEnum = [
  { label: <FormattedMessage id="common.payment.method.cash" />, value: 0 },
  { label: <FormattedMessage id="common.payment.method.account" />, value: 10 },
  // { label: '原路退', value: 20 },
];
/**
 * 退款方式-现款
 */
export const CashRefundTypeValueEnum = [
  { label: <FormattedMessage id="common.payment.method.cash" />, value: 0 },
  // { label: '原路退', value: 20 },
];

/**
 * 额度变更日志类型
 */
export const LogTypeMap: Record<number, JSX.Element> = {
  1: <FormattedMessage id="common.credit.log.type.addAccount" />,
  2: <FormattedMessage id="common.credit.log.type.modifyAccount" />,
  3: <FormattedMessage id="common.credit.log.type.enable" />,
  4: <FormattedMessage id="common.credit.log.type.lock" />,
  5: <FormattedMessage id="common.credit.log.type.cancel" />,
};

/**
 * 财务属性来源 0-系统预置  1-自建
 */
export const FinanceTagSourceValueEnum = {
  0: <FormattedMessage id="common.finance.tag.source.system" />,
  1: <FormattedMessage id="common.finance.tag.source.custom" />,
};

/**
 * 财务属性收支方向 1-收入  2-支出
 */
export const FinanceLedgerTypeValueEnum = {
  1: <FormattedMessage id="common.finance.ledger.type.income" />,
  2: <FormattedMessage id="common.finance.ledger.type.expense" />,
};

/**
 * 财务属性状态
 */
// export const FinanceTagStatusValueEnum = {
//   0: CommonStatusValueEnum[0],
//   1: CommonStatusValueEnum[1],
// };
