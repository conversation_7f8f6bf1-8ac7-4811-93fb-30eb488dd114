import { FormattedMessage } from "@umijs/max";

export enum EnablePostStatus {
  ENABLE = 'common.option.enable',
  NOT_ENABLE = 'common.option.disable',
}

export const EnablePostStatusOptions = {
  '1': { text: <FormattedMessage id={EnablePostStatus.ENABLE} />, status: 'Success' },
  '0': { text: <FormattedMessage id={EnablePostStatus.NOT_ENABLE} />, status: 'Error' },
};

export const statusAttribute: Record<string, string> = {
  '0': EnablePostStatus.ENABLE,
  '1': EnablePostStatus.NOT_ENABLE,
};

export const setStatusValue: Record<string, string> = {
  '0': '1',
  '1': '0',
};
