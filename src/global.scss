@import "./assets/richText";

html,
body,
#root {
  font-size: 14px;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

canvas {
  display: block;
}

html {
  background: #f1f1f1;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

p {
  margin: 0;
}

/**
 * 多页TAB样式调整
 */
.runtime-keep-alive-tabs-layout {
  position: relative;
  z-index: 40;
  margin-bottom: 0 !important;
}

/**
 * 内容区页面边距调整
 */
.ant-pro-page-container{
  height: 100%;
}
.ant-pro-page-container-children-container {
  padding: 16px;
}

.ant-descriptions .ant-descriptions-item-label {
  color: #000000D9;
}

/**
*记住密码阴影
**/
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition-delay: 5000s;
  transition: color 5000s ease-out, background-color 5000s ease-out;
  -webkit-transition-delay: 5000s;
  -webkit-transition: color 5000s ease-out, background-color 5000s ease-out;
}

// @supports (-webkit-appearance: none) {
// ::-webkit-scrollbar {
//   width: 7px;
//   height: 8px;
// }

// ::-webkit-scrollbar-track {
//   background-color: rgba(182, 168, 168, 0.801);
// }

// ::-webkit-scrollbar-thumb {
//   background-color: rgba(248, 52, 49, 0.1);
//   border-radius: 2px;
//   box-shadow: inset 0 0 6px rgba(180, 174, 174, 0.747);
// }

// ::-webkit-scrollbar-thumb:hover {
//   background-color: rgba(248, 52, 49, 0.1);
// }}

.is-dragging {
  pointer-events: none;
  z-index: 9999;
}

.salesOrderListTable .ant-pro-table-extra {
  margin-block-end: 0;

}
.salesOrderListTable .ant-pro-table+.ant-pro-card {
  margin-bottom: 40px;
}
.salesReturnListTable .ant-pro-card {
  margin-bottom: 40px;
}

.customer-price .ant-form-item .ant-form-item-control-input-content {
  color: #F49C1F;
  font-size: 24px;
  line-height: 24px;
  height: 24px;
  font-weight: 500;
}

.reset-ant-pro-table-search .ant-pro-table-search {
  margin-block-end: 0;
}

.customer-ant-form-item-label .ant-form-item .ant-form-item-label>label {
  color: #000000D9;
  font-weight: 600;
}

// .ant-select-item .ant-select-item-option .ant-select-item-option-active {
//   background-color: #FFEDEDFF;
// }

// 自定义关联远端商品弹窗样式
.cust-proList {
  .ant-pro-card .ant-pro-card-body {
    padding-inline: 0;
  }

  .ant-pro-checkcard-content {
    padding: 0;
  }

  .ant-pro-list-row-card {
    margin: 0;
    padding: 0;
  }

  .ant-descriptions {
    .ant-descriptions-row>td {
      padding-bottom: 0px;
    }

    .ant-descriptions-header {
      margin-bottom: 12px;
    }
  }
}

.cust-circle {
  height: 24px;
  width: 24px;
  border-radius: 9999px;
  border: 1px solid #00000014;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cust-result .ant-result {
  padding: 0;
}


/* 横向滚动条 */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 5px;
}

/* 纵向滚动条 */
::-webkit-scrollbar-vertical {
  width: 10px;
}

/* 美化滚动条 */
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Chrome */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

*::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 5px;
}

.cust-ant-empty-normal .ant-empty-normal {
  margin-block: 0;
}

.reset-ant-form-item {
  .ant-form-item {
    margin-bottom: 8px;
  }

  .ant-pro-form-list-action {
    margin-bottom: 8px;
  }
}

.ant-pro-query-filter-row {
  row-gap: 16px;
}

.ant-pro-card .ant-pro-card-body {
  padding-bottom: 24px;
}


.notice-unread {
  position: absolute;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #F49C1F;
  top: 8px;
  left: -12px;
}
.form-item-switch .ant-form-item-row{
  flex-direction: row;
  align-items: center;
  .ant-form-item-label{
    padding-bottom: 0;
  }
}
