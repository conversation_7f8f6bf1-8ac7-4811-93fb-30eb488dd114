import type { Rule } from 'antd/es/form';

const REQUIRED_RULES = { required: true };
const REQUIRED_RULES_NOT_ALLOW_EMPTY = {
  pattern: /^\S+$/,
  message: '不允许输入空字符',
};
const NOT_REQUIRED_RULES = { required: false };
const REG_ONLY_ALPHA_AND_DIGIT_RULE = {
  pattern: /^[0-9a-zA-Z]{0,}$/,
  message: '仅支持英文和数字!',
};
const REG_ONLY_ALPHA_AND_DIGIT_AND_CHINESE_RULE = {
  pattern: /^[0-9a-zA-Z\u4e00-\u9fa5]{0,}$/,
  message: '仅支持中文英文和数字!',
};
/**
 * 最多支持30个字符
 */
const REG_LENGTH_RULE: Rule = {
  message: '最多支持30个字符！',
  max: 30,
};
/**
 * 最多支持200个字符
 */
const REG_LENGTH_REMARK_RULE: Rule = {
  message: '最多支持200个字符！',
  max: 200,
};

const REG_EMAIL_RULE: Rule = {
  pattern:
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  message: "Invalid email address",
};

export {
  NOT_REQUIRED_RULES, REG_EMAIL_RULE, REG_LENGTH_REMARK_RULE,
  REG_LENGTH_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_AND_CHINESE_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_RULE,
  REQUIRED_RULES,
  REQUIRED_RULES_NOT_ALLOW_EMPTY
};

