
import imageCompression from 'browser-image-compression';

export const compressImage = async (file: File, maxSizeMB: number = 1) => {
    try {
        const compressedFile = await imageCompression(file, {
            maxSizeMB, // 最大1MB
            maxWidthOrHeight: 1920, // 最大宽度或高度
            useWebWorker: true, // 使用web worker
        });
        return compressedFile;
    } catch (error) {
        console.error('图片压缩失败:', error);
        return Promise.reject(error);
    }
};