import NP from 'number-precision';

/**
 * 根据特定规则调整价格的最后两位小数，支持负数。
 * 规则应用于价格的绝对值:
 * - 98/99 分向上取整到下一个整数 (00)
 * - 96/97 分向下取整到 95
 * - 01/02 分向下取整到 00
 * - 03/04 分向上取整到 05
 * - 其他值保持不变
 *
 * @param price 原始价格 (可以是负数)
 * @returns 调整后的价格
 */
export const adjustPriceByRule = (price: number): number => {
  if (typeof price !== 'number' || isNaN(price)) {
    return price;
  }

  const sign = price < 0 ? -1 : 1;
  const absPrice = Math.abs(price);

  // 使用分来处理以避免浮点数问题
  const priceInCents = Math.round(absPrice * 100);
  const lastTwoDigits = priceInCents % 100;
  const base = priceInCents - lastTwoDigits;

  let adjustedCents = priceInCents;

  if (lastTwoDigits >= 98 && lastTwoDigits <= 99) {
    adjustedCents = base + 100;
  } else if (lastTwoDigits >= 96 && lastTwoDigits <= 97) {
    adjustedCents = base + 95;
  } else if (lastTwoDigits >= 1 && lastTwoDigits <= 2) {
    adjustedCents = base;
  } else if (lastTwoDigits >= 3 && lastTwoDigits <= 4) {
    adjustedCents = base + 5;
  }

  return (adjustedCents / 100) * sign;
};


export const localCurrencyTransfer = (amount: number, rate = 1) => {
  return NP.divide(amount, rate);
}
