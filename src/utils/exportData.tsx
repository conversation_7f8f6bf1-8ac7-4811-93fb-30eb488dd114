import { createExportTask } from '@/services/systerm';
import { history } from '@@/core/history';
import { Modal } from 'antd';

export interface ExportDataProps {
  moduleId: string;
  systemId: string;
  taskDesc: string;
  params?: any;
  intl?: any; // 国际化对象
}

export const exportData = (props: ExportDataProps) => {
  const { params = {}, intl, ...rest } = props;
  const formatParams: any[] = [];
  for (let key in params) {
    if (typeof params[key] !== 'undefined') {
      if (Array.isArray(params[key])) {
        formatParams.push({
          key,
          values: params[key],
        });
      } else {
        formatParams.push({
          key,
          value: params[key]?.toString(),
        });
      }
    }
  }
  createExportTask({
    ...rest,
    params: formatParams,
  }).then((result) => {
    if (result?.taskId) {
      Modal.success({
        title: intl ? intl.formatMessage({ id: 'common.export.title' }) : 'Notice',
        centered: true,
        content: intl ? intl.formatMessage({ id: 'common.export.content' }) : 'Export task has been created successfully. Do you want to view the export results?',
        okText: intl ? intl.formatMessage({ id: 'common.export.okText' }) : 'View Results',
        onOk: () => {
          history.push('/system/job?jobType=Export');
        },
        closable: true,
      });
    }
  });
};
