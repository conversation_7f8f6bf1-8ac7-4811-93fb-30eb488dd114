.richText {
  word-wrap: break-word;
  line-height: 1.625;

  p,
  blockquote,
  ul,
  ol,
  dl,
  table,
  pre,
  code {
    margin-bottom: 1rem;
  }

  hr {
    height: 0.25rem;
    margin: 0.5rem 0;
    background-color: #d1d5db;

    @media (prefers-color-scheme: dark) {
      background-color: #4b5563;
    }
  }

  blockquote {
    padding: 2rem 2.5rem;
    position: relative;

    &::before,
    &::after {
      position: absolute;
      font-size: 4.5rem;
      color: #9ca3af;
      height: 5rem;
      font-family: monospace;
    }

    &::before {
      content: "“";
      top: 0;
      left: 0;
    }

    &::after {
      content: "”";
      bottom: 0;
      right: 0;
    }

    p {
      margin-bottom: 0;
    }
  }

  kbd {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
    color: #6b7280;
    background-color: #d1d5db;
    border: 1px solid #d1d5db;
    border-radius: 0.125rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    @media (prefers-color-scheme: dark) {
      background-color: #4b5563;
      border-color: #4b5563;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  h1 {
    font-size: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;

    @media (prefers-color-scheme: dark) {
      border-bottom-color: #4b5563;
    }
  }

  h2 {
    font-size: 1.25rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.75rem;

    @media (prefers-color-scheme: dark) {
      border-bottom-color: #4b5563;
    }
  }

  h3,
  h4,
  h5,
  h6 {
    font-size: 1.125rem;
  }

  ul,
  ol {
    padding-left: 0.5rem;
    margin-left: 1rem;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  table {
    width: 100%;
    overflow-x: auto;
    display: block;
    border-collapse: collapse;

    th {
      font-weight: 600;
    }

    th,
    td {
      padding: 0.5rem 1rem;
      border: 1px solid #9ca3af;

      @media (prefers-color-scheme: dark) {
        border-color: #4b5563;
      }
    }

    tr:nth-child(even) {
      background-color: #e5e7eb;

      @media (prefers-color-scheme: dark) {
        background-color: #6b7280;
      }
    }
  }

  img {
    max-width: 100%;
    display: block;
    margin: 0.75rem 0;
  }

  code,
  tt {
    padding: 0.5rem 1rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;

    @media (prefers-color-scheme: dark) {
      border-color: #4b5563;
    }
  }

  p:first-child {
    img:first-child {
      margin-top: 0;
    }
  }

  p:last-child {
    margin-bottom: 0;

    img:last-child {
      margin-bottom: 0;
    }
  }
}
