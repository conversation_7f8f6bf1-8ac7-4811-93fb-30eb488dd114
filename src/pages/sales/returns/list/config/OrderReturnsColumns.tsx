import AuthButton from '@/components/common/AuthButton';
import { getCstList } from '@/pages/customer/list/services';
import { MemberAccountEntity } from '@/pages/finance/customer/types/MemberAccountEntity';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/personnel/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Space, Tag } from 'antd';
import type { MutableRefObject } from 'react';
import type { ReturnsOrderType } from '../../operation/types/ReturnsOrderType';
import type { ReturnsTableEntity } from '../../operation/types/ReturnsTableEntity';
import { ReturnsOrderStatusValueEnum } from '../types/ReturnsOrderStatusValueEnum';
import { ReturnsRefundStatusValueEnum } from '../types/ReturnsRefundStatusValueEnum';
import { ReturnType, returnTypeOptions } from '@/pages/sales/returns/operation/types/return.type';
import { RefundOrderStatus } from '@/pages/sales/returns/list/types/refund.order.status';
export default (
  openUndoModal: (params: ReturnsOrderType) => void,
  openCancelModal: (params: ReturnsOrderType) => void,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  accountList: MemberAccountEntity[] | undefined,
) => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnOrderNo' }),
      dataIndex: 'orderNo',
      width: 200,
      fixed: 'left',
      render: (orderNo, record) => {
        return (
          <a
            onClick={() => {
              history.push(
                `/sales/returns/detail?orderId=${record.orderId}&orderNo=${record.orderNo}`,
              );
            }}
          >
            <span className="mr-1">{orderNo}</span>
            {record.tagIds?.includes(ReturnType.Good) && (
              <Tag color="green">
                {intl.formatMessage({ id: 'sales.returns.operation.returnType.good' })}
              </Tag>
            )}
            {record.tagIds?.includes(ReturnType.Broken) && (
              <Tag color="red">
                {intl.formatMessage({ id: 'sales.returns.operation.returnType.broken' })}
              </Tag>
            )}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.orderStatus' }),
      dataIndex: 'orderStatusName',
      width: 80,
      valueType: 'select',
      formItemProps: {
        name: 'orderStatus',
      },
      search: {
        transform: (value) => {
          return { orderStatusList: [value] };
        },
      },
      valueEnum: ReturnsOrderStatusValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnStore' }),
      dataIndex: 'storeName',
      width: 100,
      formItemProps: {
        name: 'storeIds',
      },
      valueType: 'select',
      search: {
        transform: (value) => {
          return { storeIds: [value] };
        },
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
        onChange: () => {
          formRef.current?.setFieldValue('backWarehouseIds', undefined);
        },
      },
      request: async () => await queryStoreByAccount({ status: 1 }),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.receiveWarehouse' }),
      dataIndex: 'backWarehouseName',
      formItemProps: {
        name: 'backWarehouseIds',
      },
      width: 120,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      search: {
        transform: (value) => {
          return { backWarehouseIds: [value] };
        },
      },
      dependencies: ['storeIds'],
      request: ({ storeIds }) =>
        warehouseList({ storeIdList: storeIds }).then((t) => t.warehouseSimpleRoList),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.customerName' }),
      dataIndex: 'cstName',
      width: 120,
      valueType: 'select',
      search: {
        transform: (value) => {
          return { cstIds: [value] };
        },
      },
      formItemProps: {
        name: 'cstIds',
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'cstName', value: 'cstId' },
      },
      request: () => getCstList({}),
    },
    {
      dataIndex: 'keyword',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'sales.returns.list.productSearchPlaceholder' }),
      },
      formItemProps: {
        label: intl.formatMessage({ id: 'sales.returns.list.productInfo' }),
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnTime' }),
      dataIndex: 'orderCreateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: 'Reference No.',
      dataIndex: 'ReferenceNo',
      width: 140,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.returnType.label' }),
      dataIndex: 'tagIds',
      width: 100,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: returnTypeOptions,
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.creator' }),
      dataIndex: 'salesmanName',
      width: 60,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      formItemProps: {
        name: 'salesmanId',
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnTime' }),
      dataIndex: 'orderCreateTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.refundAmount' }),
      dataIndex: 'orderAmount',
      search: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementMethod' }),
      dataIndex: 'refundTypeName',
      search: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementStatus' }),
      dataIndex: 'refundStatus',
      width: 80,
      valueEnum: ReturnsRefundStatusValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementAccount' }),
      dataIndex: 'refundDetailList',
      width: 120,
      ellipsis: true,
      renderText: (refundDetailList) => {
        return refundDetailList?.map((item: any) => item.accountName).join(',');
      },
      transform: (value: any) => {
        return { accountIds: value };
      },
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        name: 'accountIds',
        options: (accountList ?? [])?.map((item) => ({
          label: item.memberAccountName,
          value: item.id,
        })),
      },
    },
    {
      title: intl.formatMessage({ id: 'common.column.remark' }),
      width: 80,
      ellipsis: true,
      hideInTable: true,
      formItemProps: {
        name: 'remark',
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.operation' }),
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <Space>
          <AuthButton
            isHref
            visible={record.orderStatus === RefundOrderStatus.DRAFT}
            authority="editSaleReturn"
            key="editor"
            onClick={() => {
              history.push(
                `/sales/returns/operation?orderId=${record.orderId}&orderNo=${record.orderNo}`,
              );
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.edit' })}
          </AuthButton>
          <AuthButton
            key="undo"
            isHref
            authority="withdrawSaleReturn"
            visible={record.orderStatus === RefundOrderStatus.TO_IN}
            onClick={() => {
              openUndoModal({ orderId: record.orderId, orderNo: record.orderNo });
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.withdraw' })}
          </AuthButton>
          <AuthButton
            isHref
            authority="deleteSaleReturn"
            key="cancel"
            visible={[RefundOrderStatus.DRAFT, RefundOrderStatus.TO_IN].includes(
              record.orderStatus!,
            )}
            onClick={() => {
              openCancelModal({ orderId: record.orderId, orderNo: record.orderNo });
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.void' })}
          </AuthButton>
        </Space>
      ),
    },
  ] as ProColumns<ReturnsTableEntity>[];
};
