import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import _ from 'lodash';
import type { AfterSaleOrderGoodsRo } from '../../operation/types/ReturnsAfterSaleDetailEntity';
import { CauseType, causeTypeOptions } from '@/pages/sales/returns/operation/types/cause.type';

export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productName' }),
      dataIndex: 'itemName',
      width: 140,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity?.brandPartNos),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brand' }),
      dataIndex: 'brandName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.category' }),
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.unit' }),
      dataIndex: 'unitName',
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.list.orderNo' }),
      dataIndex: 'orgOrderNo',
      width: 180,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.refundAmount' }),
      dataIndex: 'unitAmount',
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.returnQuantity' }),
      dataIndex: 'refundNum',
      width: 80,
      valueType: 'digit',
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.preTaxSubtotal' }),
      dataIndex: 'unitAmount',
      valueType: 'money',
      width: 80,
      render: (_text, record) => (
        <span>${_.round(_.multiply(record.unitAmount ?? 0, record.refundNum ?? 0), 2)}</span>
      ),
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.afterTaxSubtotal' }),
      dataIndex: 'unitAmountTax',
      valueType: 'money',
      width: 80,
      render: (_text, record) => (
        <span>${_.round(_.multiply(record.unitAmountTax ?? 0, record.refundNum ?? 0), 2)}</span>
      ),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.detail.returnReason' }),
      width: 120,
      ellipsis: true,
      dataIndex: 'cause',
      render: (_, record) =>
        record.causeType === CauseType.OTHER
          ? record.cause
          : causeTypeOptions.find((item) => item.value === record.causeType)?.label,
    },
  ] as ProColumns<AfterSaleOrderGoodsRo>[];
};
