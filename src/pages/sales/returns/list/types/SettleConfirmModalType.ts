import type { AfterSaleOrderRo } from '@/pages/sales/returns/operation/types/ReturnsAfterSaleDetailEntity';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';

export interface SettleConfirmModalType {
  visible: boolean;
  onCancel?: () => void;
  onOk?: () => void;
  returnOrderDetail?: AfterSaleOrderRo;
  cstDetail?: CustomerEntity;
  currency?: string;
  onChange?: () => void;
}
