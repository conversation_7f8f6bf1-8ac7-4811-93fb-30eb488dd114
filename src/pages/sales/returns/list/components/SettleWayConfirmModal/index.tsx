import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { CashRefundTypeValueEnum, RefundTypeEnum, RefundTypeValueEnum } from '@/types/CommonStatus';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Spin, message, type FormInstance } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import { defaultTo, isEmpty, isUndefined, set } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { modifyOrder } from '../../../operation/services';
import type {
  ReturnsOrderModifyRefund,
  ReturnsOrderModifyRefundDetail,
  ReturnsOrderModifyType,
} from '../../../operation/types/ReturnsGoodsCreateItemType';
import type { SettleConfirmModalType } from '../../types/SettleConfirmModalType';
import { ADVANCE_ACCOUNT, getAccountList } from '@/components/PaymentForm';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import MoneyText from '@/components/common/MoneyText';
import {
  AdvanceAmountList,
  ReceivableAmountList,
} from '@/pages/customer/list/types/CustomerEntity';

export default (props: SettleConfirmModalType) => {
  const intl = useIntl();
  const { visible, onOk, onCancel, returnOrderDetail, cstDetail, onChange } = props;
  // 加载中
  const [loading, setLoading] = useState<boolean>(false);
  const [refundOptions, setRefundOptions] = useState<DefaultOptionType[]>([]);

  const [accountList, setAccountList] = useState<DefaultOptionType[]>([]);

  const formRef = useRef<FormInstance<ReturnsOrderModifyRefund>>();

  const refundAmount = returnOrderDetail?.main.refundAmount ?? 0;

  useEffect(() => {
    if (!props.visible) {
      formRef.current?.resetFields();
    }
  }, [props.visible]);

  useAsyncEffect(async () => {
    if (cstDetail?.base?.storeId && props.visible && cstDetail && returnOrderDetail) {
      queryMemberAccountPage({
        belongToStore: [cstDetail?.base?.storeId],
      }).then((result) => {
        if (result?.data) {
          setAccountList(
            getAccountList({
              // @ts-ignore
              list: result.data,
              cstDetail,
              currency: returnOrderDetail?.main?.currency?.toString(),
            }).list,
          );
        }
      });
    }
  }, [cstDetail?.base?.storeId, props.visible, cstDetail, returnOrderDetail]);

  useEffect(() => {
    if (returnOrderDetail && visible && cstDetail && formRef.current) {
      setRefundOptions(cstDetail?.settle?.credit ? RefundTypeValueEnum : CashRefundTypeValueEnum);
      formRef.current?.setFieldValue('refundType', returnOrderDetail.refunds[0].refundType);
      if (returnOrderDetail.refunds[0].refundType == RefundTypeEnum.Cash) {
        if (returnOrderDetail?.refundDetails) {
          formRef.current?.setFieldValue(
            'refundDetails',
            returnOrderDetail.refundDetails.map((t) => {
              if (t.refundChannel === PayChannel.ADVANCE) {
                return {
                  accountId: ADVANCE_ACCOUNT,
                  refundAmount: t.refundAmount,
                };
              } else {
                return {
                  accountId: t.accountId,
                  refundAmount: t.refundAmount,
                };
              }
            }),
          );
        }
      }
    }
  }, [returnOrderDetail, visible, cstDetail, formRef.current]);

  /**
   * 更新退货订单
   */
  const updateOrder = async (params: ReturnsOrderModifyType, reload?: boolean) => {
    setLoading(true);
    const result = await modifyOrder(params);
    if (result) {
      setLoading(false);
      onChange?.();
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const advanceInfo = cstDetail?.settle?.advanceAmountList?.find(
    (item: AdvanceAmountList) => item.currency === returnOrderDetail?.main.currency,
  ) as AdvanceAmountList;
  const receivable = cstDetail?.settle?.receivableAmountList?.find(
    (item: ReceivableAmountList) => item.currency === returnOrderDetail?.main.currency,
  ) as ReceivableAmountList;

  return (
    <ModalForm<ReturnsOrderModifyRefund>
      title={intl.formatMessage({ id: 'sales.returns.settlement.title' })}
      open={visible}
      width={360}
      layout={'vertical'}
      validateTrigger="onBlur"
      modalProps={{
        destroyOnClose: true,
        okText: intl.formatMessage({ id: 'sales.returns.settlement.confirm' }),
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
        classNames: {
          header: 'flex flex-row justify-center',
          footer: 'flex flex-row-reverse justify-center gap-4',
        },
      }}
      formRef={formRef}
      onValuesChange={async (
        changeValues: ReturnsOrderModifyRefund,
        allValues: ReturnsOrderModifyRefund,
      ) => {
        try {
          if (changeValues.refundType) return;
          const bottomValues = await formRef.current?.validateFields?.();
          console.log(changeValues, allValues, bottomValues);
          if (!bottomValues?.refundDetails) return;
          // 如果有非法结算信息则不保存
          const validArray: ReturnsOrderModifyRefundDetail[] = [];
          const inValidArray: ReturnsOrderModifyRefundDetail[] = [];

          const array = bottomValues.refundDetails;
          for (let i = 0; i < array.length; i++) {
            const item = array[i];
            if (
              !isUndefined(item) &&
              isUndefined(item?.accountId) &&
              isUndefined(item?.refundAmount)
            ) {
              continue;
            }
            if (isEmpty(item) || isEmpty(item?.accountId) || (item.refundAmount ?? 0) <= 0) {
              inValidArray.push(item);
            } else {
              if (item.accountId === ADVANCE_ACCOUNT) {
                validArray.push({
                  refundAmount: item.refundAmount,
                  refundChannel: PayChannel.ADVANCE,
                });
              } else {
                validArray.push({ ...item, refundChannel: PayChannel.CASH });
              }
            }
          }
          if (!isEmpty(inValidArray) || isEmpty(validArray)) return;
          if (validArray.length !== bottomValues.refundDetails?.length) return;
          const reqParams = {
            orderId: returnOrderDetail?.main?.id,
            orderNo: returnOrderDetail?.main?.orderNo,
            refund: { refundType: allValues.refundType, refundDetails: validArray },
          };
          updateOrder(reqParams, true);
        } catch (e) {
          console.log(e);
        }
      }}
      onFinish={async () => {
        onOk?.();
        return true;
      }}
    >
      <ProForm.Item>
        <div className="flex items-center gap-3">
          <span>{intl.formatMessage({ id: 'sales.returns.settlement.settlementAmount' })}:</span>
          <div className="text-[24px] font-medium text-[#F83431]">
            <MoneyText text={refundAmount} />
          </div>
        </div>
      </ProForm.Item>
      <Spin spinning={loading}>
        <ProFormSelect<number>
          label={
            <div>
              <span className="font-semibold">
                {intl.formatMessage({ id: 'sales.returns.settlement.settlementMethod' })}
              </span>
              {Boolean(advanceInfo) && (
                <span className="ml-2 text-[12px] text-gray-500">
                  (预收余额: {advanceInfo?.currencySymbol}
                  {advanceInfo?.amount}, 应收: {receivable?.currencySymbol}
                  {receivable?.amount})
                </span>
              )}
            </div>
          }
          options={refundOptions}
          name="refundType"
          allowClear={false}
          onChange={async (value) => {
            const reqParams: ReturnsOrderModifyType = {
              orderId: returnOrderDetail?.main?.id,
              orderNo: returnOrderDetail?.main?.orderNo,
              refund: { refundType: value },
            };
            // 结算方式修改为挂账时直接更新
            if (value == 0) {
              const defaultAccountId = accountList?.filter((item) => !item.disabled)?.[0]?.value;
              const refundDetails = [
                {
                  accountId: defaultAccountId,
                  refundAmount: returnOrderDetail?.main.refundAmount,
                  refundChannel: PayChannel.CASH,
                },
              ];
              if (refundDetails[0].accountId === ADVANCE_ACCOUNT) {
                delete refundDetails[0].accountId;
                refundDetails[0].refundChannel = PayChannel.ADVANCE;
              }
              set(reqParams, 'refund.refundDetails', refundDetails);
              formRef?.current?.setFieldValue('refundDetails', refundDetails);
            }
            updateOrder(reqParams, true);
          }}
        />
        <ProFormDependency name={['refundType']}>
          {({ refundType }) => {
            if (refundType === 10) {
              return (
                <div className="text-gray-500 availableAmount">
                  {intl.formatMessage({ id: 'sales.returns.settlement.available' })}
                  {defaultTo(cstDetail?.settle?.availableAmount, '-')}
                </div>
              );
            } else if (refundType === 0) {
              return (
                <ProFormList
                  className="reset-ant-form-item"
                  name="refundDetails"
                  max={2}
                  min={1}
                  initialValue={[{}]}
                  creatorButtonProps={false}
                  copyIconProps={false}
                  deleteIconProps={false}
                  actionRender={(field, action, _, count) => [
                    <div key="action" className="ml-4">
                      {count == 1 && (
                        <PlusOutlined
                          className="text-lg"
                          key="add"
                          onClick={() => action.add({})}
                        />
                      )}
                      {count == 2 && (
                        <DeleteOutlined
                          className="text-lg text-primary"
                          key="remove"
                          onClick={() => action.remove(field.name)}
                        />
                      )}
                    </div>,
                  ]}
                >
                  <ProFormMoney
                    name="refundAmount"
                    fieldProps={{
                      addonBefore: (
                        <ProFormSelect
                          noStyle
                          name="accountId"
                          allowClear={false}
                          options={accountList}
                          placeholder={intl.formatMessage({
                            id: 'sales.returns.settlement.selectAccount',
                          })}
                          normalize={(value, prevValue, prevValues) => {
                            const res = prevValues?.refundDetails?.find(
                              (t: ReturnsOrderModifyRefundDetail) => t?.accountId == value,
                            );
                            if (!isEmpty(res)) {
                              message.error(
                                intl.formatMessage({
                                  id: 'sales.returns.settlement.duplicateAccountError',
                                }),
                              );
                              return prevValue ?? '';
                            }
                            return value;
                          }}
                        />
                      ),
                      controls: false,
                      precision: 2,
                      // max: totalAmount,
                      placeholder: intl.formatMessage({
                        id: 'sales.returns.settlement.inputAmount',
                      }),
                    }}
                  />
                </ProFormList>
              );
            }
          }}
        </ProFormDependency>
      </Spin>
    </ModalForm>
  );
};
