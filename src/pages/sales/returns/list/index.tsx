import ConfirmModal from '@/components/ConfirmModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import type { GetProps } from 'antd';
import { Space } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import * as NP from 'number-precision';
import { useMemo, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { cancelOrder, drawOrder } from '../operation/services';
import type { ReturnsOrderType } from '../operation/types/ReturnsOrderType';
import type { ReturnsTableEntity } from '../operation/types/ReturnsTableEntity';
import OrderReturnsColumns from './config/OrderReturnsColumns';
import { getAfterSaleOrderPaged } from './services';

// CANCEL 作废
// UNDO 撤回
type OperatorType = 'CANCEL' | 'UNDO';
const SalesReturnList = () => {
  const formRef = useRef<ProFormInstance | undefined>();
  const actionRef = useRef<ActionType>();
  const intl = useIntl();
  // 撤回或者作废
  const [operatorType, setOperatorType] = useState<OperatorType>('CANCEL');
  const [orderParams, setOrderParams] = useState<ReturnsOrderType>({ orderId: '', orderNo: '' });

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const [tableData, setTableData] = useState<ReturnsTableEntity[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const selectedRows = useMemo(() => {
    return tableData.filter((item) => selectedRowKeys.includes(item.orderId));
  }, [selectedRowKeys, tableData]);

  const rowSelection: TableRowSelection<ReturnsTableEntity> = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  const { isSidebarFold } = useModel('layoutModel');

  const { data: accountData = [] } = useRequest(() => queryMemberAccountPage({ pageSize: 100 }));
  const accountList = [
    { id: '0', memberAccountName: intl.formatMessage({ id: 'sales.order.edit.creditAccount' }) },
    ...(accountData?.data ?? []),
  ];

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 打开【作废】确认对话框
   */
  const openCancelModal = (params: ReturnsOrderType) => {
    setOrderParams(params);
    setOperatorType('CANCEL');
    setConfirmModalProps((preModalProps) => ({
      ...preModalProps,
      open: true,
      okText: intl.formatMessage({ id: 'sales.returns.list.void' }),
      tips: intl.formatMessage({ id: 'sales.returns.list.confirmVoid' }),
    }));
  };
  /**
   * 关闭【作废】确认对话框
   */
  const closeCancelModal = () => {
    setConfirmModalProps((preModalProps) => ({
      ...preModalProps,
      open: false,
    }));
  };
  /**
   * 确认【作废】
   */
  const onConfirm = async () => {
    let result = false;
    if (operatorType === 'CANCEL') {
      result = await cancelOrder(orderParams);
    } else if (operatorType === 'UNDO') {
      result = await drawOrder(orderParams);
    }
    if (result) {
      closeCancelModal();
      actionRef.current?.reload(true);
    }
  };

  /**
   * 打开【撤回】对话框
   */
  const openUndoModal = (params: ReturnsOrderType) => {
    setOrderParams(params);
    setOperatorType('UNDO');
    setConfirmModalProps((preModalProps) => ({
      ...preModalProps,
      open: true,
      okText: intl.formatMessage({ id: 'sales.returns.list.withdraw' }),
      tips: intl.formatMessage({ id: 'sales.returns.list.confirmWithdraw' }),
    }));
  };

  return (
    <PageContainer>
      <FunProTable<ReturnsTableEntity>
        className="salesReturnListTable"
        rowKey="orderId"
        formRef={formRef}
        rowSelection={rowSelection}
        onLoad={(dataSource) => {
          setTableData(dataSource);
        }}
        requestPage={(query) => {
          setSelectedRowKeys([]);
          // @ts-ignore
          return getAfterSaleOrderPaged({ ...query, tagIds: query.tagIds ? [query.tagIds] : [] });
        }}
        headerTitle={
          <Space size={16}>
            <AuthButton
              key="create"
              authority="addSaleReturn"
              type="primary"
              onClick={() => {
                history.push(`/sales/returns/operation`, {
                  orderId: undefined,
                  orderNo: undefined,
                  name: intl.formatMessage({ id: 'sales.returns.list.addSalesReturn' }),
                });
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.list.addSalesReturn' })}
            </AuthButton>
            <AuthButton
              key="export"
              authority="exportSaleReturn"
              danger
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'sales.returns.list.exportTaskDesc' }),
                  moduleId: 'MEMBER_AFTERSALE_ORDER_EXPRORT',
                  params: formRef.current?.getFieldsFormatValue?.(),
                });
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.list.export' })}
            </AuthButton>
          </Space>
        }
        options={false}
        actionRef={actionRef}
        columns={OrderReturnsColumns(openUndoModal, openCancelModal, formRef, accountList)}
      />
      <ConfirmModal {...confirmModalProps} onOk={onConfirm} onCancel={closeCancelModal} />

      <div
        className={`bg-white px-6 py-3 fixed bottom-[0] right-[0] z-50 shadow-[0_-2px_4px_rgba(0,0,0,0.05)] ${
          isSidebarFold ? 'left-[48px]' : 'left-[180px]'
        }`}
      >
        <div className="font-bold text-[14px] mb-[4px]">
          <span>
            {intl.formatMessage({ id: 'sales.returns.list.totalReturnCount' })}：
            {selectedRowKeys.length}
          </span>
          <span className="ml-[40px]">
            {intl.formatMessage({ id: 'sales.returns.list.totalReturnAmount' })}：
            <b className="text-red-500 text-[20px]">
              {' '}
              {NP.plus(
                selectedRows.reduce((total, item) => NP.plus(total, item.orderAmount!), 0),
                0,
              ).toFixed(2)}
              元{' '}
            </b>
          </span>
        </div>
        <div>
          {accountList?.map((item) => {
            const total = selectedRows.reduce(
              (total, row) =>
                NP.plus(
                  total,
                  row?.refundDetailList?.find((pay) => pay.accountId === item.id)?.refundAmount ??
                    0,
                ),
              0,
            );
            if (total > 0) {
              return (
                <span key={item.id} className="mr-[16px]">
                  {item.memberAccountName}：¥
                  {total.toFixed(2)}元
                </span>
              );
            }
            return null;
          })}
        </div>
      </div>
    </PageContainer>
  );
};

export default withKeepAlive(SalesReturnList);
