import { FormattedMessage } from '@umijs/max';
import { OrderStatusName } from '@/pages/sales/order/list/types/OrderStatus';

export enum CauseType {
  PRODUCT_NOT_MATCH = 1,
  PRODUCT_QUALITY,
  BUYER_NOT_WANT,
  INVENTORY_RETURN,
  PACKAGE_BROKEN,
  OTHER = 99,
}

export const causeTypeOptions = [
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.PRODUCT_NOT_MATCH'} />,
    value: CauseType.PRODUCT_NOT_MATCH,
  },
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.PRODUCT_QUALITY'} />,
    value: CauseType.PRODUCT_QUALITY,
  },
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.BUYER_NOT_WANT'} />,
    value: CauseType.BUYER_NOT_WANT,
  },
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.INVENTORY_RETURN'} />,
    value: CauseType.INVENTORY_RETURN,
  },
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.PACKAGE_BROKEN'} />,
    value: CauseType.PACKAGE_BROKEN,
  },
  {
    label: <FormattedMessage id={'sales.returns.operation.causeType.OTHER'} />,
    value: CauseType.OTHER,
  },
];
