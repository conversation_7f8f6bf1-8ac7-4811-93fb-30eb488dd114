import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { AfterSaleOrderGoodsRo } from '../types/ReturnsAfterSaleDetailEntity';
import { history } from '@@/core/history';
import NP from 'number-precision';
import MoneyText from '@/components/common/MoneyText';
import { CauseType, causeTypeOptions } from '@/pages/sales/returns/operation/types/cause.type';
import { Form, Input, Select, Tag } from 'antd';

export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productName' }),
      dataIndex: 'itemName',
      width: 140,
      search: false,
      editable: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brand' }),
      dataIndex: 'brandName',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesOrderNo' }),
      dataIndex: 'orgOrderNo',
      search: false,
      width: 240,
      editable: false,
      render: (_text, record) => {
        return (
          <a
            className="inline-flex gap-1"
            onClick={() => {
              history.push(`/sales/order/detail?orderNo=${record.orgOrderNo}`);
            }}
          >
            {record.orgOrderNo}
            {record.isDiscount && <Tag color="red">优惠单</Tag>}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesStore' }),
      dataIndex: 'storeName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.refundAmount' }),
      dataIndex: 'unitAmount',
      key: 'unitAmount',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      editable: true,
      fieldProps() {
        return { precision: 2 };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.returnQuantity' }),
      dataIndex: 'refundNum',
      key: 'refundNum',
      search: false,
      width: 100,
      valueType: 'digit',
      fixed: 'right',
      editable: true,
      fieldProps() {
        return { precision: 0 };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.detail.returnReason' }),
      dataIndex: 'causeTypeAndCause',
      key: 'causeTypeAndCause',
      search: false,
      fixed: 'right',
      width: 200,
      renderFormItem: (_, { record, isEditable, recordKey }, form) => {
        const causeType = record?.causeType;
        form.setFieldValue([recordKey, 'causeType'], record?.causeType);
        form.setFieldValue([recordKey, 'cause'], record?.cause);
        return (
          <div className="my-1 flex flex-col gap-1">
            <Form.Item name={[recordKey as number, 'causeType']} noStyle>
              <Select options={causeTypeOptions} style={{ width: '100%' }} placeholder="请选择" />
            </Form.Item>
            {causeType === CauseType.OTHER && (
              <Form.Item name={[recordKey as number, 'cause']} noStyle>
                <Input style={{ width: '100%' }} placeholder="请输入" />
              </Form.Item>
            )}
          </div>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.preTaxSubtotal' }),
      dataIndex: 'unitAmount',
      width: 90,
      editable: false,
      fixed: 'right',
      render: (_text, record) => {
        return <MoneyText text={NP.times(record.unitAmount, record.refundNum)} />;
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.afterTaxSubtotal' }),
      dataIndex: 'unitAmountTax',
      width: 90,
      editable: false,
      fixed: 'right',
      render: (_text, record) => {
        return <MoneyText text={NP.times(record.unitAmountTax, record.refundNum)} />;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      width: 60,
    },
  ] as ProColumns<AfterSaleOrderGoodsRo>[];
};
