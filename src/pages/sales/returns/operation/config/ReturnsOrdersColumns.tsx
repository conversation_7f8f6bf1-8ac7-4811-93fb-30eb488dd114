import ColumnRender from '@/components/ColumnRender';
import { MAX_AMOUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import { useIntl, history } from '@umijs/max';
import { Button, message, Tag } from 'antd';
import type { ReturnsOrderEntity } from '../types/ReturnsOrderEntity';
export interface OrderReturnsColumnsProps {
  /**
   * 已选退款订单NO集合
   */
  addedItemPurchaseOrderNo: string[];
  /**
   * 添加商品事件
   * @param item
   */
  handleAdd: (item: ReturnsOrderEntity) => void;
  orderDataCache: ReturnsOrderEntity[];
}

export default (props: OrderReturnsColumnsProps) => {
  const intl = useIntl();

  const saveRow = (item?: ReturnsOrderEntity) => {
    if (!item) return;
    if ((item?.refundAmount ?? 0) < 0) {
      message.warning(intl.formatMessage({ id: 'sales.returns.operation.fillReturnAmount' }));
      return;
    }
    if (!item.refundNum) {
      message.warning(intl.formatMessage({ id: 'sales.returns.operation.fillReturnQuantity' }));
      return;
    }
    props.handleAdd(item);
  };

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
      editable: false,
    },
    {
      dataIndex: 'keyword',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'sales.returns.operation.productSearchPlaceholder' }),
      },
      formItemProps: {
        label: intl.formatMessage({ id: 'sales.returns.operation.productInfo' }),
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesOrderNo' }),
      dataIndex: 'orderNo',
      width: 240,
      editable: false,
      render: (_text, record: ReturnsOrderEntity) => {
        return (
          <a
            className="inline-flex gap-1"
            onClick={() => {
              history.push(`/sales/order/detail?orderNo=${record.orderNo}`);
            }}
          >
            {record.orderNo}
            {record.isDiscount && <Tag color="red">优惠单</Tag>}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesTime' }),
      dataIndex: 'orderFinishTime',
      width: 180,
      editable: false,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesTime' }),
      dataIndex: 'orderFinishTime',
      valueType: 'dateRange',
      editable: false,
      hideInTable: true,
      search: {
        transform: ([beginTime, endTime]) => ({ beginTime, endTime }),
      },
    },
    {
      title: '订单来源',
      dataIndex: 'itemSn',
      search: false,
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productCode' }),
      dataIndex: 'itemSn',
      search: false,
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productName' }),
      dataIndex: 'itemName',
      search: false,
      width: 140,
      editable: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      search: false,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender(text as string[]);
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brand' }),
      dataIndex: 'brandName',
      search: false,
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesStore' }),
      dataIndex: 'storeName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.deliveryWarehouse' }),
      dataIndex: 'warehouseName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.paymentMethod' }),
      dataIndex: 'payKindName',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.actualUnitPrice' }),
      dataIndex: 'unitAmount',
      search: false,
      width: 80,
      valueType: 'money',
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesQuantity' }),
      dataIndex: 'saleNum',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.refundableQuantity' }),
      dataIndex: 'refundableNum',
      search: false,
      width: 80,
      align: 'center',
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.refundAmount' }),
      dataIndex: 'refundAmount',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      editable: true,
      fieldProps(_, config) {
        const filterId = config.entity.returnsId;
        const isDisabled = props.addedItemPurchaseOrderNo.includes(filterId);
        return {
          disabled: isDisabled || (config.entity?.refundableNum ?? 0) <= 0,
          min: 0,
          max: MAX_AMOUNT,
          precision: 2,
          onPressEnter: (e) => {
            e.stopPropagation();
            const item = props.orderDataCache?.find((s) => s.returnsId == filterId);
            saveRow(item);
          },
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.returnQuantity' }),
      dataIndex: 'refundNum',
      search: false,
      width: 100,
      valueType: 'digit',
      fixed: 'right',
      editable: true,
      fieldProps(_, config) {
        const filterId = config.entity.returnsId;
        const isDisabled = props.addedItemPurchaseOrderNo.includes(filterId);
        return {
          disabled: isDisabled || (config.entity?.refundableNum ?? 0) < 1,
          max: config.entity.refundableNum,
          min: 1,
          precision: 0,
          onPressEnter: (e) => {
            e.stopPropagation();
            const item = props.orderDataCache?.find((s) => s.returnsId == filterId);
            saveRow(item);
          },
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      fixed: 'right',
      editable: false,
      align: 'center',
      width: 60,
      render: (_, row) => {
        const isDisabled = props.addedItemPurchaseOrderNo.includes(row.returnsId);
        return (
          <Button
            className="px-0"
            type="link"
            disabled={isDisabled || (row?.refundableNum ?? 0) <= 0}
            onClick={() => {
              const item = props.orderDataCache?.find((s) => s.returnsId == row.returnsId);
              saveRow(item);
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.operation.return' })}
          </Button>
        );
      },
    },
  ] as ProColumns<ReturnsOrderEntity>[];
};
