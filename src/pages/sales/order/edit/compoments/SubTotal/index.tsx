import MoneyText from '@/components/common/MoneyText';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { useIntl } from '@umijs/max';

export interface SubTotalProps {
  orderDetail?: OrderListItemEntity;
}

const SubTotal = (props: SubTotalProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  const getSaleNum = () => {
    let num = 0;
    orderDetail?.orderGoodsROList?.forEach((item) => {
      num += item?.saleNum ?? 0;
    });
    return num;
  };

  return (
    <div className="flex justify-between items-center font-semibold text-base text-[#000000D9]">
      <div className="flex gap-5">
        <span>
          {intl.formatMessage({ id: 'sales.order.edit.totalQuantity' })}：{getSaleNum()}
        </span>
        <span>
          {intl.formatMessage({ id: 'sales.order.edit.totalAmount' })}：
          <span>
            <MoneyText text={orderDetail?.orderPrice?.totalGoodsPriceAmountYuan} />
          </span>
        </span>
        <span>
          {intl.formatMessage({ id: 'sales.order.edit.discountAmount' })}：
          <MoneyText text={orderDetail?.orderPrice?.totalDiscountAmountYuan} />
        </span>
        <span>
          {intl.formatMessage({ id: 'sales.order.edit.deliveryFee' })}：
          <MoneyText text={orderDetail?.orderPrice?.deliveryAmountYuan} />
        </span>
        <span>
          GST：
          <MoneyText text={orderDetail?.orderPrice?.totalTaxationAmountYuan} />
        </span>
        <span>
          {intl.formatMessage({ id: 'sales.order.edit.adjustAmount' })}：
          <MoneyText text={-(orderDetail?.orderPrice?.adjustmentYuan ?? 0)} />
        </span>
      </div>
      <span>
        {intl.formatMessage({ id: 'sales.order.edit.actualAmount' })}：
        <span className="text-2xl text-[#F83431FF]">
          <MoneyText text={orderDetail?.orderPrice?.shouldTotalOrderAmountYuan} />
        </span>
      </span>
    </div>
  );
};

export default SubTotal;
