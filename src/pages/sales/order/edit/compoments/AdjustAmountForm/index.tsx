import {
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from 'umi';
import { adjustAmountOptions, AdjustAmountType } from '../../types/adjust.amount.type';
import { ProFormGroup } from '@ant-design/pro-form';
import { REQUIRED_RULES } from '@/utils/RuleUtils';

export default function AdjustAmountForm() {
  const intl = useIntl();

  return (
    <div>
      <ProFormSelect
        label={
          <span className="font-medium">
            {intl.formatMessage({ id: 'sales.order.edit.adjustAmount' })}
          </span>
        }
        options={adjustAmountOptions}
        name="adjustmentType"
        initialValue={AdjustAmountType.None}
      />
      <ProFormDependency name={['adjustmentType']}>
        {({ adjustmentType }) => {
          if (adjustmentType === AdjustAmountType.Custom) {
            return (
              <ProFormGroup>
                <ProFormDigit
                  min={0.01}
                  rules={[REQUIRED_RULES]}
                  name="adjustment"
                  fieldProps={{
                    precision: 2,
                  }}
                  placeholder={intl.formatMessage({
                    id: 'sales.order.edit.adjustAmountCustomPrice',
                  })}
                />
                <ProFormText
                  name="adjustmentReason"
                  placeholder={intl.formatMessage({
                    id: 'sales.order.edit.adjustAmountCustomReason',
                  })}
                />
              </ProFormGroup>
            );
          }
        }}
      </ProFormDependency>
    </div>
  );
}
