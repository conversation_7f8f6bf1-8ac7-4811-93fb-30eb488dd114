import { DiscountType, useDiscountTypeOptions } from '@/pages/sales/order/edit/types/DiscountType';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormDependency, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { CouponEntity } from '@/pages/sales/order/edit/types/coupon.entity';

export interface DiscountFormProps {
  couponList: CouponEntity[];
}

const DiscountForm = (props: DiscountFormProps) => {
  const intl = useIntl();
  const { couponList = [] } = props;
  let discountTypeOptions = useDiscountTypeOptions();

  if (couponList.length === 0) {
    discountTypeOptions.forEach((item) => {
      if (item.value === DiscountType.COUPON) {
        // @ts-ignore
        item.disabled = true;
      }
    });
  }

  return (
    <>
      <ProFormSelect
        name="discountType"
        options={discountTypeOptions}
        label={
          <div className="flex justify-between items-center w-[180px]">
            <span className="font-semibold">
              {intl.formatMessage({ id: 'sales.order.edit.discountType' })}
            </span>
            {couponList.length > 0 && (
              <span className="text-red-600 text-[12px]">存在可用优惠券</span>
            )}
          </div>
        }
      />
      <ProFormDependency name={['discountType']}>
        {({ discountType }) => {
          if (discountType === DiscountType.DISCOUNT_ON_ORDER) {
            return (
              <ProFormDigit
                rules={[REQUIRED_RULES]}
                name="discountRate"
                placeholder={intl.formatMessage({ id: 'sales.order.edit.inputDiscount' })}
                max={9.9}
                min={0.1}
                fieldProps={{
                  addonAfter: intl.formatMessage({ id: 'sales.order.edit.discountUnit' }),
                  precision: 1,
                }}
              />
            );
          }
          if (discountType === DiscountType.DEDUCTION_ON_ORDER) {
            return (
              <ProFormDigit
                rules={[REQUIRED_RULES]}
                name="discountMoney"
                min={0.01}
                placeholder={intl.formatMessage({ id: 'sales.order.edit.inputAmount' })}
                fieldProps={{
                  addonAfter: intl.formatMessage({ id: 'sales.order.edit.amountUnit' }),
                  precision: 2,
                }}
              />
            );
          }
          if (discountType === DiscountType.COUPON) {
            return (
              <ProFormSelect
                rules={[REQUIRED_RULES]}
                name="couponId"
                options={couponList.map((item) => ({
                  label: item.couponName,
                  value: item.userCouponId,
                }))}
              />
            );
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default DiscountForm;
