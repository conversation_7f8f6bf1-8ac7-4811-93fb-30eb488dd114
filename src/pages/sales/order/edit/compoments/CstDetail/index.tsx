import CustomerDetailDrawerForm from '@/pages/customer/list/components/CustomerDetailDrawerForm';
import { CustomerDetailDrawerFormType } from '@/pages/customer/list/types/CustomerDetailDrawerFormType';
import { useState } from 'react';

import { useIntl } from '@umijs/max';
import { Button, Tag } from 'antd';
import { defaultTo } from 'lodash';
import { FormattedMessage } from '@@/exports';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';

export interface CstDetailProps {
  cstDetail?: CustomerEntity;
}

const CstDetail = (props: CstDetailProps) => {
  const { cstDetail } = props;
  const intl = useIntl();

  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: cstDetail?.base?.id ?? '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: intl.formatMessage({ id: 'sales.order.edit.customerDetail' }),
  });

  const handleViewDetail = () => {
    setDetailModalProps((prev) => ({
      ...prev,
      visible: !prev.visible,
      recordId: prev.visible ? '' : cstDetail?.base?.id ?? '',
    }));
  };

  if (!cstDetail) {
    return;
  }

  const contact = cstDetail.contacts?.find((item) => item.isDefault);

  return (
    <div className="mt-4">
      <div className="py-5 px-6 rounded bg-[#f5f5f5]">
        <div className="flex justify-between items-center">
          <span className="text-[#000000D9] font-semibold text-xl flex items-center">
            <span className="mr-2">{cstDetail.base?.cstName}</span>
            {cstDetail.settle?.credit ? (
              <Tag color="orange">
                {intl.formatMessage({ id: 'sales.order.edit.creditCustomer' })}
              </Tag>
            ) : (
              ''
            )}
            {cstDetail.tags?.map((item) => (
              <Tag key={item.id} color="blue">
                {item.tagName}
              </Tag>
            ))}
          </span>
          <Button type={'link'} onClick={handleViewDetail}>
            查看详情
          </Button>
        </div>
        <div className="mt-2 text-[#000000D9] flex gap-x-10 gap-y-1 flex-wrap">
          <span>
            {intl.formatMessage({ id: 'customer.customerList.table.column.storeName' })}：
            {cstDetail.base?.storeName}
          </span>
          <span>
            {intl.formatMessage({ id: 'sales.order.edit.contact' })}：{contact?.firstName}{' '}
            {contact?.lastName}
          </span>
          <span>
            {intl.formatMessage({ id: 'sales.order.edit.contactMethod' })}：{contact?.phone}
          </span>
          <span>
            {intl.formatMessage({ id: 'customer.customerList.createForm.isGstExcluded' })}：
            {cstDetail?.settle?.gstExcluded ? (
              <FormattedMessage id="common.option.yes" />
            ) : (
              <FormattedMessage id="common.option.no" />
            )}
          </span>
          {cstDetail?.settle?.credit && (
            <>
              <span>
                {intl.formatMessage({ id: 'sales.order.edit.creditLimit' })}：
                {defaultTo(cstDetail.settle?.totalAmount, '-')} (
                {intl.formatMessage({ id: 'sales.order.edit.available' })}
                {defaultTo(cstDetail.settle?.availableAmount, '-')})
              </span>
            </>
          )}
          <span>
            {intl.formatMessage({ id: 'customer.customerList.table.column.receivableAmount' })}：
            {cstDetail.settle?.receivableAmountCurrency ?? '-'}
          </span>
          <span>
            {intl.formatMessage({ id: 'customer.customerList.table.column.advanceAmount' })}：
            {cstDetail.settle?.advanceAmountCurrency ?? '-'}
          </span>
        </div>
      </div>

      <CustomerDetailDrawerForm {...detailModalProps} />
    </div>
  );
};

export default CstDetail;
