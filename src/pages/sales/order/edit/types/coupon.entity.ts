export interface CouponEntity {
  /**
   * 优惠券有效开始时间
   */
  beginTime?: string;
  /**
   * 能否使用
   */
  canUse?: boolean;
  /**
   * 优惠金额，减XX元
   */
  couponAmount?: number;
  /**
   * 优惠券模版id
   */
  couponId?: number;
  /**
   * 优惠券名称
   */
  couponName?: string;
  /**
   * 1.已领取未用2.已使用3.过期4.已作废
   */
  couponStatus?: number;
  /**
   * 优惠券过期时间
   */
  deadline?: string;
  /**
   * 优惠券面额，满xx元
   */
  enableAmount?: number;
  /**
   * 不能使用原因
   */
  reasonsForNonUse?: string;
  /**
   * 优惠券使用说明
   */
  useDesc?: string;
  /**
   * 券实体id记录id
   */
  userCouponId?: number;
}
