import { FormattedMessage } from 'umi';

export enum AdjustAmountType {
  None,
  Round,
  Custom,
}

export const adjustAmountOptions = [
  {
    label: <FormattedMessage id="sales.order.edit.adjustAmountNone" />,
    value: AdjustAmountType.None,
  },
  {
    label: <FormattedMessage id="sales.order.edit.adjustAmountRound" />,
    value: AdjustAmountType.Round,
  },
  {
    label: <FormattedMessage id="sales.order.edit.adjustAmountCustom" />,
    value: AdjustAmountType.Custom,
  },
];
