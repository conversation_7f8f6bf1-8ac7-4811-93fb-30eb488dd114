import { useIntl } from '@umijs/max';

export enum DiscountType {
  NONE,
  DISCOUNT_ON_ORDER,
  DEDUCTION_ON_ORDER,
  COUPON,
}

export const useDiscountTypeOptions = () => {
  const intl = useIntl();

  return [
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.none' }),
      value: DiscountType.NONE,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.orderDiscount' }),
      value: DiscountType.DISCOUNT_ON_ORDER,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.orderDeduction' }),
      value: DiscountType.DEDUCTION_ON_ORDER,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.coupon' }),
      value: DiscountType.COUPON,
    },
  ];
};
