import { PayKind } from '@/components/PaymentForm/types/PayKind';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';

export interface ConfirmPayRequest {
  /**
   * 操作人姓名
   */
  operator?: string;
  /**
   * 操作人工号
   */
  operatorId?: string;
  /**
   * 订单号
   */
  orderId?: string;
  /**
   * 支付明细
   */
  payDetailList?: PayDetailList[];
  /**
   * 支付方式1现款2挂账
   */
  payKind?: PayKind;
}

export interface PayDetailList {
  /**
   * 支付金额
   */
  payAmount?: number;
  /**
   * 支付方式0挂账1现金
   */
  payChannel?: PayChannel;
  /**
   * 收款账户
   */
  payeeAcount?: string;
}
