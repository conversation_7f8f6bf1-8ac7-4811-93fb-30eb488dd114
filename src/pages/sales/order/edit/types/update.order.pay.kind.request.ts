export interface UpdateOrderPayKindRequest {
  /**
   * 操作人姓名
   */
  operator?: string;
  /**
   * 操作人工号
   */
  operatorId?: string;
  /**
   * 订单号
   */
  orderId?: string;
  /**
   * 支付明细
   */
  payDetailList?: PayDetailList[];
  /**
   * 付款方式1现款2挂账
   */
  payKind?: number;
}

export interface PayDetailList {
  /**
   * 支付金额
   */
  payAmount?: number;
  /**
   * 收款账户
   */
  payeeAcount?: string;
  /**
   * 支付方式0挂账 1现金 2预收
   */
  payType?: number;
}
