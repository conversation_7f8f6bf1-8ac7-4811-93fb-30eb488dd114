export interface UpdateOrderRemarkRequest {
  /**
   * 操作人姓名
   */
  operator?: string;
  /**
   * 操作人工号
   */
  operatorId?: string;
  /**
   * 订单id
   */
  orderId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 1：客户备注、2：门店对客备注、3：门店对内备注
   */
  remarkType?: RemarkType;
  /**
   * 0不打印1打印
   */
  isPrint?: number;
}

export enum RemarkType {
  Customer = 1,
  StoreToCustomer,
  StoreToInner,
}
