import { request } from '@/utils/request';
import { CreateDraftOrderRequest } from './types/create.draft.order.request';
import { UpdateOrderItemRequest } from './types/update.order.item.request';
import { UpdateOrderPayKindRequest } from './types/update.order.pay.kind.request';
import { UpdateOrderDeliveryInfoRequest } from './types/update.order.delivery.info.request';
import { UpdateOrderRemarkRequest } from './types/update.order.remark.request';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { UpdateOrderDiscountRequest } from '@/pages/sales/order/edit/types/update.order.discount.request';
import { UpdateExtendExpenseRequest } from '@/pages/sales/order/edit/types/update.extend.expense.request';
import { AllOutboundResponse } from '@/pages/sales/order/edit/types/AllOutboundResponse';
import { ConfirmPayRequest } from '@/pages/sales/order/edit/types/confirm.pay.request';
import { UpdateOrderAddressRequest } from '@/pages/sales/order/edit/types/update.order.address.request';
import { UpdateOrderMainRequest } from '@/pages/sales/order/edit/types/update.order.main.request';
import { message } from 'antd';
import { CouponEntity } from '@/pages/sales/order/edit/types/coupon.entity';
import { GetAvailableCouponListForEcRequest } from '@/pages/sales/order/edit/types/get.available.coupon.list.for.ec.request';
import { UpdateAdjustmentRequest } from '@/pages/sales/order/edit/types/update.adjustment.request';
import { UpdateOrderTagRequest } from '@/pages/sales/order/edit/types/update.order.tag.request';

/**
 * 创建销售草稿单
 */
export const createDraftOrder = async (params: CreateDraftOrderRequest) => {
  return request<{ orderNo: string }>(`/ipmssale/createDraftOrder`, {
    data: params,
  });
};

/**
 * 订单详情查询
 */
export const getOrderByOrderNoForDbReturnSelected = async (orderNo: string) => {
  return request<OrderListItemEntity>(`/ipmssale/getOrderByOrderNoForDbReturnSelected`, {
    data: { orderNo },
  });
};

/**
 * 修改商品明细
 */
export const updateOrderItem = async (params: UpdateOrderItemRequest) => {
  return request<boolean>(`/ipmssale/updateOrderItem`, {
    data: params,
  });
};

/**
 * 修改付款方式
 */
export const updateOrderPayKind = async (params: UpdateOrderPayKindRequest) => {
  return request<boolean>(`/ipmssale/updateOrderPayKind`, {
    data: params,
  });
};

/**
 * 修改配送信息
 */
export const updateOrderDeliveryInfo = async (params: UpdateOrderDeliveryInfoRequest) => {
  return request<boolean>(`/ipmssale/updateOrderDeliveryInfo`, {
    data: params,
  });
};

/**
 * 修改备注
 */
export const updateOrderRemark = async (params: UpdateOrderRemarkRequest) => {
  return request<boolean>(`/ipmssale/updateOrderRemark`, {
    data: params,
  });
};

/**
 * 提交订单
 */
export const submitOrder = async (orderId: string) => {
  return request<boolean>(`/ipmssale/submitOrder`, {
    data: { orderId },
  });
};

/**
 * 订单打印
 */
export const printOrder = async (orderId: string) => {
  return request<boolean>(`/ipmssale/printOrder`, {
    data: { orderId },
  });
};

/**
 * 修改优惠金额
 */
export const updateOrderDiscount = async (params: UpdateOrderDiscountRequest) => {
  return request<boolean>(`/ipmssale/updateOrderDiscount`, {
    data: params,
  });
};

/**
 * 修改运费
 */
export const updateExtendExpense = async (params: UpdateExtendExpenseRequest) => {
  return request<boolean>(`/ipmssale/updateExtendExpense`, {
    data: params,
  });
};

/**
 * 一键出库
 */
export const allOutbound = async (orderIds: string[]) => {
  return request<AllOutboundResponse[]>(`/ipmssale/allOutbound`, {
    data: { orderIdList: orderIds },
  }).then((result) => {
    if (result?.[0]?.success) {
      return true;
    } else {
      message.error(result?.[0]?.returnMsg);
    }
  });
};

/**
 * 确认支付
 */
export const confirmPay = async (params: ConfirmPayRequest) => {
  return request<boolean>(`/ipmssale/confirmPay`, {
    data: params,
  });
};

/**
 * 修改配送地址
 */
export const updateOrderAddress = async (params: UpdateOrderAddressRequest) => {
  return request<boolean>(`/ipmssale/updateOrderAddress`, {
    data: params,
  });
};

/**
 * 修改订单主体信息
 */
export const updateOrderMain = async (params: UpdateOrderMainRequest) => {
  return request<boolean>(`/ipmssale/updateOrderMain`, {
    data: params,
  });
};

/**
 * 查询可用优惠券
 */
export const getAvailableCouponListForEc = async (params: GetAvailableCouponListForEcRequest) => {
  return request<CouponEntity[]>(`/ipmspromotion/userCouponQuery/getAvailableCouponListForEc`, {
    data: params,
  });
};

/**
 * 调整金额
 */
export const updateAdjustment = async (params: UpdateAdjustmentRequest) => {
  return request<boolean>(`/ipmssale/orderSaleFacade/updateAdjustment`, {
    data: params,
  });
};

/**
 * 订单加急
 */
export const updateOrderTag = async (params: UpdateOrderTagRequest) => {
  return request<boolean>(`/ipmssale/orderSaleFacade/updateOrderTag`, {
    data: params,
  });
};
