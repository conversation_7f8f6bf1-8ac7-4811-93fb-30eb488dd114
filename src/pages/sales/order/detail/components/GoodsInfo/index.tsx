import FunProTable from '@/components/common/FunProTable';
import LeftTitle from '@/components/LeftTitle';
import SubTotal from '@/pages/sales/order/edit/compoments/SubTotal';
import type {
  OrderGoodsROList,
  OrderListItemEntity,
} from '@/pages/sales/order/list/types/order.list.item.entity';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Flex } from 'antd';
import { getDetailColumns } from '../../config/DetailColumns';

export interface GoodsInfoProps {
  orderDetail?: OrderListItemEntity;
}

const GoodsInfo = (props: GoodsInfoProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  return (
    <>
      <FunProTable<OrderGoodsROList, any>
        scroll={{ x: 800 }}
        headerTitle={
          <LeftTitle title={intl.formatMessage({ id: 'sales.order.detail.goodsDetail' })} />
        }
        search={false}
        pagination={false}
        dataSource={orderDetail?.orderGoodsROList ?? []}
        options={false}
        columns={getDetailColumns(intl)}
      />
      <ProCard bordered>
        <div key="summary">
          <SubTotal orderDetail={orderDetail} />
        </div>
      </ProCard>
    </>
  );
};

export default GoodsInfo;
