import { Form, message, Modal } from 'antd';
import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { CheckCard, ProForm } from '@ant-design/pro-components';
import { AuditStatus } from '@/pages/purchase/detail/types/AuditStatus';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'antd/es/form/Form';
import { useIntl } from '@umijs/max';
import { useEffect, useState } from 'react';
import { OrderGoodsROList } from '@/pages/sales/order/list/types/order.list.item.entity';
import { ReviewGoodEntity } from '@/pages/sales/order/list/types/review.good.entity';
import { checkLowestPrice, queryLowPriceReviewGoods } from '@/pages/sales/order/list/services';

export interface AuditModalProps {
  visible: boolean;
  onClose: () => void;
  onChange: () => void;
  orderId?: string;
}

export default function AuditModal(props: AuditModalProps) {
  const { visible, onClose, onChange, orderId } = props;
  const [form] = useForm();
  const intl = useIntl();
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [goods, setGoods] = useState<ReviewGoodEntity[]>([]);

  useEffect(() => {
    if (orderId && visible) {
      // @ts-ignore
      queryLowPriceReviewGoods(orderId).then((res) => setGoods(res));
    }
    return () => {
      setGoods([]);
    };
  }, [orderId, visible]);

  const resultChange = (e: any) => {
    if (e == AuditStatus.REJECT) {
      setIsCheck(true);
    } else {
      setIsCheck(false);
    }
  };

  /**
   * 提交
   */
  const handleOk = () => {
    form.validateFields?.().then(async (values) => {
      checkLowestPrice({
        orderId,
        checkResult: isCheck ? 2 : 1,
        checkReason: values.checkReason,
      }).then((res) => {
        if (res) {
          message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
          onChange?.();
          onClose?.();
        }
      });
    });
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'common.button.audit' })}
      open={visible}
      onCancel={onClose}
      width={800}
      onOk={handleOk}
    >
      <LeftTitle
        title={intl.formatMessage({ id: 'sales.order.detail.auditGoods' })}
        className="mt-6"
      />
      <FunProTable<ReviewGoodEntity>
        search={false}
        ghost={true}
        dataSource={goods}
        toolbar={{ settings: [] }}
        scroll={{ x: 700 }}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.column.index' }),
            valueType: 'index',
            width: 60,
          },
          {
            title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
            width: 100,
            dataIndex: 'itemSn',
          },
          {
            title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
            dataIndex: 'itemName',
            width: 180,
            ellipsis: true,
          },
          {
            title: intl.formatMessage({ id: 'purchase.stockUp.list.columns.sumQuantity' }),
            width: 80,
            dataIndex: 'saleNum',
          },
          {
            title: intl.formatMessage({ id: 'sales.returns.operation.actualUnitPrice' }),
            width: 90,
            dataIndex: 'actualSellingUnitPriceYuan',
            valueType: 'money',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.edit.lowestPrice' }),
            width: 90,
            dataIndex: 'lowPrice',
            valueType: 'money',
          },
        ]}
      />
      <LeftTitle
        title={intl.formatMessage({ id: 'finance.receive.audit.result' })}
        className="mt-6 mb-4"
      />
      <ProForm form={form} layout="horizontal" submitter={false} className="pb-4">
        <Form.Item
          name="result"
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'purchase.audit.validation.selectResult' }),
            },
          ]}
          style={{ marginBottom: 0 }}
        >
          <CheckCard.Group onChange={resultChange}>
            <CheckCard
              description={
                <span className="flex justify-center font-medium text-base">
                  {intl.formatMessage({ id: 'purchase.audit.option.approve' })}
                </span>
              }
              value={AuditStatus.APPROVE}
              style={{ width: '208px' }}
            />
            <CheckCard
              description={
                <span className="flex justify-center font-medium text-base">
                  {intl.formatMessage({ id: 'purchase.audit.option.reject' })}
                </span>
              }
              value={AuditStatus.REJECT}
              style={{ width: '208px' }}
            />
          </CheckCard.Group>
        </Form.Item>
        {isCheck && (
          <Form.Item
            name="checkReason"
            rules={[
              {
                required: isCheck,
                message: intl.formatMessage({ id: 'purchase.audit.validation.rejectReason' }),
              },
            ]}
          >
            <TextArea
              placeholder={intl.formatMessage({ id: 'purchase.audit.placeholder.reason' })}
              allowClear
              showCount={true}
              maxLength={100}
            />
          </Form.Item>
        )}
      </ProForm>
    </Modal>
  );
}
