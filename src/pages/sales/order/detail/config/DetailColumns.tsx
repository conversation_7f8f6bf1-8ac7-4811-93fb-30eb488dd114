import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import type {
  OrderGoodsROList,
  OrderPayDetailList,
  OrderTimeNodeROList,
} from '../../list/types/order.list.item.entity';
import _ from 'lodash';

export const getDetailColumns = (intl: any): ProColumns<OrderGoodsROList>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.productCode' }),
    dataIndex: 'itemSn',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.productName' }),
    dataIndex: 'itemName',
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.brandPartNumber' }),
    dataIndex: 'brandPartNo',
    width: 100,
    renderText: (text) => ColumnRender.ArrayColumnRender(text?.split(',') as string[]),
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.brand' }),
    dataIndex: 'brandName',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.category' }),
    dataIndex: 'categoryName',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.unit' }),
    width: 50,
    dataIndex: 'unitName',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.salePrice' }),
    dataIndex: 'unitPriceYuan',
    valueType: 'money',
    width: 80,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.discountPrice' }),
    dataIndex: 'actualSellingUnitPriceYuan',
    valueType: 'money',
    width: 80,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.saleQuantity' }),
    dataIndex: 'saleNum',
    width: 60,
  },
  {
    title: intl.formatMessage({ id: 'purchase.external.columns.preTaxSubtotal' }),
    dataIndex: 'unitPriceYuan',
    valueType: 'money',
    width: 80,
    render: (_text, record) => (
      <span>${_.round(_.multiply(record.unitPriceYuan ?? 0, record.saleNum ?? 0), 2)}</span>
    ),
  },
  {
    title: intl.formatMessage({ id: 'purchase.external.columns.afterTaxSubtotal' }),
    dataIndex: 'unitPriceTaxYuan',
    valueType: 'money',
    width: 80,
    render: (_text, record) => (
      <span>${_.round(_.multiply(record.unitPriceTaxYuan ?? 0, record.saleNum ?? 0), 2)}</span>
    ),
  },
];
/**
 * 支付流水
 */
export const getDetailPaymentColumns = (intl: any): ProColumns<OrderPayDetailList>[] => [
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementTime' }),
    dataIndex: 'payTime',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementAccount' }),
    dataIndex: 'payeeAccountName',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementAmount' }),
    dataIndex: 'payAmountYuan',
    valueType: 'money',
  },
];

/**
 * 操作记录
 */
export const getDetailOperatorColumns = (intl: any): ProColumns<OrderTimeNodeROList>[] => [
  {
    title: intl.formatMessage({ id: 'sales.order.detail.operationTime' }),
    dataIndex: 'nodeTime',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.orderNode' }),
    dataIndex: 'operateTypeName',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.operator' }),
    dataIndex: 'operator',
    search: false,
  },
];
