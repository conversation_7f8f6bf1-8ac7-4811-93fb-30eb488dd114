import FunProTable from '@/components/common/FunProTable';
import { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { accountListQuerySimple, queryStoreByAccount } from '../../user/services';
import { queryqueryRemainingLeaveList } from '../services';
import { RemainLeaveEntity } from '../types/remainLeave.entity';

const LeaveBalance = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });

  const columns: ProColumns<RemainLeaveEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: '门店',
      dataIndex: 'storeName',
      width: 120,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('personnel.leave.employee'),
      dataIndex: 'accountName',
      key: 'memberName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'accountId',
      },
    },
    { title: '年假累计', dataIndex: 'yearTotalHours', search: false },
    { title: '年假已用', dataIndex: 'yearUsedHours', search: false, render: (text) => <span className='text-primary'>{text}</span> },
    { title: '年假剩余', dataIndex: 'yearBalanceHours', search: false },
    { title: '病假累计', dataIndex: 'sickTotalHours', search: false },
    { title: '病假已用', dataIndex: 'sickUsedHours', search: false },
    { title: '病假剩余', dataIndex: 'sickBalanceHours', search: false, render: (text) => <span className='text-primary'>{text}</span> },
    { title: '长期服务假累计', dataIndex: 'serviceTotalHours', search: false },
    { title: '长期服务假已用', dataIndex: 'serviceUsedHours', search: false },
    { title: '长期服务假剩余', dataIndex: 'serviceBalanceHours', search: false },
  ];

  return (
    <FunProTable<RemainLeaveEntity, any>
      columns={columns}
      requestPage={queryqueryRemainingLeaveList}
      scroll={{ x: 'max-content' }}
    />
  );
};

export default LeaveBalance;
