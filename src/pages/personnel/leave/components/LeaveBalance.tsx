import FunProTable from '@/components/common/FunProTable';
import { InfoCircleOutlined, SwapOutlined } from '@ant-design/icons';
import { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useState } from 'react';
import { accountListQuerySimple, queryStoreByAccount } from '../../user/services';
import { queryqueryRemainingLeaveList } from '../services';
import { RemainLeaveEntity } from '../types/remainLeave.entity';

interface BalanceLeaveProps {
  unit: 'hour' | 'day' | 'week';
  value?: number;
  className?: string;
}
const BalanceLeave = (props: BalanceLeaveProps) => {
  const { unit, value = 0, className } = props;
  if (unit === 'hour') {
    return <span className={className}>{value}h</span>;
  }
  if (unit === 'day') {
    return <span className={className}>≈{value}d</span>;
  }
  if (unit === 'week') {
    return <span className={className}>≈{value}wk</span>;
  }
}

const LeaveBalance = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  // 1: 小时 2: 天/周
  const [unitType, setUnitType] = useState<1 | 2>(1);
  const isHour = unitType === 1;

  const columns: ProColumns<RemainLeaveEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: '门店',
      dataIndex: 'storeName',
      width: 120,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('personnel.leave.employee'),
      dataIndex: 'accountName',
      key: 'memberName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'accountId',
      },
    },
    {
      title: '年假累计', dataIndex: isHour ? 'yearTotalHours' : 'yearTotalHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} />
    },
    {
      title: '年假已用', dataIndex: isHour ? 'yearUsedHours' : 'yearUsedHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} />
    },
    {
      title: '年假剩余', dataIndex: isHour ? 'yearBalanceHours' : 'yearBalanceHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} className='text-primary' />
    },
    {
      title: '病假累计', dataIndex: isHour ? 'sickTotalHours' : 'sickTotalHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} />
    },
    {
      title: '病假已用', dataIndex: isHour ? 'sickUsedHours' : 'sickUsedHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} />
    },
    {
      title: '病假剩余', dataIndex: isHour ? 'sickBalanceHours' : 'sickBalanceHoursConvertDay', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'day'} className='text-primary' />
    },
    {
      title: '长期服务假累计', dataIndex: isHour ? 'serviceTotalHours' : 'serviceTotalHoursConvertWeek', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'week'} />
    },
    {
      title: '长期服务假已用', dataIndex: isHour ? 'serviceUsedHours' : 'serviceUsedHoursConvertWeek', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'week'} />
    },
    {
      title: '长期服务假剩余', dataIndex: isHour ? 'serviceBalanceHours' : 'serviceBalanceHoursConvertWeek', search: false,
      render: (text) => <BalanceLeave value={text} unit={isHour ? 'hour' : 'week'} className='text-primary' />
    },
  ];

  return (
    <FunProTable<RemainLeaveEntity, any>
      columns={columns}
      requestPage={queryqueryRemainingLeaveList}
      scroll={{ x: 'max-content' }}
      toolbar={{
        settings: [<div className='text-sm text-gray-500' onClick={() => setUnitType(unitType === 1 ? 2 : 1)}>
          <InfoCircleOutlined />
          <span className='mx-1'>折合天/周的统一说明文案</span>
          <SwapOutlined />
        </div>]
      }}
    />
  );
};

export default LeaveBalance;
