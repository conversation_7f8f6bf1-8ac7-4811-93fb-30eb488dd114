import { TimeRangeFormat } from '@/components/common/TimeFormat';
import Selector from '@/components/Selector';
import {
  ModalForm
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Descriptions, Flex, Form, Image } from 'antd';
import { useState } from 'react';
import { passLeaveApply, rejectLeaveApply } from '../../services';
import { LeaveEntity } from '../../types/leave.entity';
import { LeaveStateEnum, LeaveStateOptions, LeaveTypeOptions } from '../../types/leave.enum';

interface ApproveLeaveModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  leave: LeaveEntity;
}

const ApproveLeaveModal: React.FC<ApproveLeaveModalProps> = ({ visible, onClose, onSuccess, leave }) => {
  const intl = useIntl();
  const t = (key: string, ...args: any[]) => intl.formatMessage({ id: key }, ...args);

  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState<LeaveStateEnum>(LeaveStateEnum.Approved);


  const handleSubmit = async () => {
    if (activeKey === LeaveStateEnum.Rejected) {
      await rejectLeaveApply({ id: leave.id });
    } else {
      await passLeaveApply({ id: leave.id });
    }
    onSuccess();
    onClose();
    return true;
  };

  return (
    <ModalForm
      title={t('personnel.leave.approveLeave')}
      open={visible}
      form={form}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          form.resetFields();
          onClose();
        }
      }}
      width={600}
      initialValues={{
        state: LeaveStateEnum.Approved,
      }}
      submitter={{
        render: (_, dom) => <Flex justify="center" className='w-full' gap={20}>{dom}</Flex>,
      }}
    >
      <div className='module-title font-semibold'>
        {t('personnel.leave.leaveInfo')}
      </div>
      <Descriptions column={1}>
        <Descriptions.Item label={t('personnel.leave.applicant')}>
          {leave.accountName}
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.submitter')}>
          {leave.submitAccountName}
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.leaveType')}>
          {LeaveTypeOptions[leave.type!].text}
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.remainingHours')}>
          {t('personnel.leave.hoursUnit', { hours: leave.hours })}
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.leaveTime')}>
          <TimeRangeFormat startTime={leave.startTime} endTime={leave.endTime} />
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.leaveDuration')}>
          {t('personnel.leave.hoursUnit', { hours: leave.hours })}
        </Descriptions.Item>
        <Descriptions.Item label={t('personnel.leave.reason')}>
          {leave.reason}
        </Descriptions.Item>
        {leave.images && (
          <Descriptions.Item label={t('personnel.leave.doctorCertificate')}>
            <Image.PreviewGroup>
              {leave.images.map((image) => (
                <Image key={image.url} width={100} src={image.url} />
              ))}
            </Image.PreviewGroup>
          </Descriptions.Item>
        )}
      </Descriptions>

      <div className='module-title font-semibold my-2'>
        {t('personnel.leave.approvalResult')}
      </div>
      <Selector
        isFullWidth
        size='middle'
        options={[
          {
            label: LeaveStateOptions[LeaveStateEnum.Approved].text,
            key: LeaveStateEnum.Approved,
          },
          {
            label: LeaveStateOptions[LeaveStateEnum.Rejected].text,
            key: LeaveStateEnum.Rejected,
          },
        ]} activeKey={activeKey}
        onSelect={setActiveKey} className="mb-5" />
    </ModalForm>
  );
};

export default ApproveLeaveModal;