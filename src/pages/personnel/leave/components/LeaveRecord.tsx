import FunProTable from "@/components/common/FunProTable";
import { TimeRangeFormat } from "@/components/common/TimeFormat";
import { ProColumns } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { useBoolean } from "ahooks";
import { Button, message, Modal, Space } from "antd";
import dayjs from "dayjs";
import { useRef, useState } from "react";
import { accountListQuerySimple, queryStoreByAccount } from "../../user/services";
import { cancelLeaveApply, queryLeaveList } from "../services";
import { LeaveApplyEntity, LeaveEntity } from "../types/leave.entity";
import { LeaveExchangeChannel, LeaveStateEnum, LeaveStateOptions, LeaveTypeOptions } from "../types/leave.enum";
import ApplyLeaveModal from "./ApplyLeaveModal";
import ApproveLeaveModal from "./ApproveLeaveModal";
import PayLeaveModal from "./PayLeaveModal";



const LeaveRecord = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  const [modal, contextHolder] = Modal.useModal();

  const [applyLeaveModalVisible, { setTrue: showApplyLeaveModal, setFalse: hideApplyLeaveModal }] = useBoolean(false);
  const [payLeaveModalVisible, { setTrue: showPayLeaveModal, setFalse: hidePayLeaveModal }] = useBoolean(false);
  const [approveLeaveModalVisible, { setTrue: showApproveLeaveModal, setFalse: hideApproveLeaveModal }] = useBoolean(false);

  const [selectedLeave, setSelectedLeave] = useState<any>(null);

  const actionRef = useRef<any>();

  const handleApplyLeave = () => {
    setSelectedLeave(null);
    showApplyLeaveModal();
  };

  const handleEditApplyLeave = (leave: LeaveEntity) => {
    if (leave.exchangeChannel === LeaveExchangeChannel.Leave) {
      showApplyLeaveModal();
    }
    if (leave.exchangeChannel === LeaveExchangeChannel.Pay_Leave) {
      showPayLeaveModal();
    }
    setSelectedLeave(leave);
  };

  const handlePayLeave = () => {
    showPayLeaveModal();
  };

  const handleApproveLeave = (record: any) => {
    setSelectedLeave(record);
    showApproveLeaveModal();
  };

  const reload = () => {
    actionRef.current?.reload();
  }

  const handleCancelLeave = async (record: any, action) => {
    const confirmed = await modal.confirm({
      title: intl.formatMessage({ id: 'common.tip.confirm.action' }, { action: action }),
      okText: action,
    });
    if (confirmed) {
      cancelLeaveApply({ id: record.id }).then((result) => {
        if (result) {
          message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
          actionRef.current?.reload();
        }
      });
    }
  };

  const columns: ProColumns<LeaveApplyEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: '门店',
      dataIndex: 'storeName',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('personnel.leave.leaveType'),
      dataIndex: 'type',
      valueEnum: LeaveTypeOptions,
      formItemProps: {
        name: 'typeList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
      },
    },
    {
      title: t('personnel.leave.employee'),
      dataIndex: 'accountName',
      key: 'memberName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'accountId',
      },
    },
    { title: t('personnel.leave.reason'), dataIndex: 'reason', search: false },
    {
      title: t('personnel.leave.leaveTime'),
      dataIndex: 'startTime',
      render: (_, record) => {
        return <TimeRangeFormat startTime={record.startTime} endTime={record.endTime} />;
      },
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            startTime: value[0] ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00') : undefined,
            endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          };
        },
      },
    },
    {
      title: '记录类型',
      dataIndex: 'exchangeChannelDesc',
      search: false,
    },
    { title: t('personnel.leave.hours'), dataIndex: 'hours', search: false },
    { title: '提交人', dataIndex: 'submitAccountName', search: false },
    {
      title: '审批结果', dataIndex: 'state', valueEnum: LeaveStateOptions,
      formItemProps: {
        name: 'stateList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
      },
    },
    { title: '审批人', dataIndex: 'approveAccountName', search: false },
    { title: '审批时间', dataIndex: 'approveTime', search: false },
    {
      title: t('common.column.operation'),
      key: 'action',
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          {record.state === LeaveStateEnum.Pending_Review && (
            <a onClick={() => handleApproveLeave(record)}>{t('personnel.leave.approve')}</a>
          )}
          {[LeaveStateEnum.Pending_Review, LeaveStateEnum.Rejected].includes(
            record.state as LeaveStateEnum,
          ) && <a onClick={() => handleEditApplyLeave(record)}>{t('common.button.edit')}</a>}
          {record.state === LeaveStateEnum.Pending_Review && <a onClick={() => handleCancelLeave(record, '撤回')}>撤回</a>}
          {record.state === LeaveStateEnum.Approved && <a onClick={() => handleCancelLeave(record, '作废')}>作废</a>}
        </Space >
      ),
    },
  ];
  return <>
    <FunProTable<LeaveEntity, any>
      actionRef={actionRef}
      columns={columns}
      requestPage={queryLeaveList}
      scroll={{ x: 'max-content' }}
      headerTitle={
        <Space>
          <Button type="primary" onClick={handleApplyLeave}>
            {t('personnel.leave.applyLeave')}
          </Button>
          <Button type="primary" ghost onClick={handlePayLeave}>
            {t('personnel.leave.payLeave')}
          </Button>
        </Space>
      }
    />
    {applyLeaveModalVisible && (
      <ApplyLeaveModal
        visible={applyLeaveModalVisible}
        onClose={hideApplyLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {payLeaveModalVisible && (
      <PayLeaveModal
        visible={payLeaveModalVisible}
        onClose={hidePayLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {approveLeaveModalVisible && (
      <ApproveLeaveModal
        visible={approveLeaveModalVisible}
        onClose={hideApproveLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {contextHolder}
  </>
};

export default LeaveRecord;