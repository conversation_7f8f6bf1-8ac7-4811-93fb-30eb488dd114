import { accountListQuerySimple } from '@/pages/personnel/user/services';
import {
  ModalForm,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import { payLeave } from '../../services';
import { LeaveApplyEntity, LeaveEntity, PayLeaveEntity } from '../../types/leave.entity';
import { LeaveType, LeaveTypeOptions } from '../../types/leave.enum';

interface PayLeaveModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  leave?: LeaveEntity;
}

const PayLeaveModal: React.FC<PayLeaveModalProps> = ({ visible, onClose, onSuccess }) => {
  const intl = useIntl();
  const t = (key: string, ...args: any[]) => intl.formatMessage({ id: key }, ...args);

  const [form] = Form.useForm<LeaveApplyEntity>();

  const handleSubmit = async (values: any) => {
    await payLeave({ ...values });
    onSuccess();
    onClose();
    return true;
  };

  return (
    <ModalForm<PayLeaveEntity>
      title={t('personnel.leave.payLeave')}
      open={visible}
      form={form}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          form.resetFields();
          onClose();
        }
      }}
      width={600}
    >
      <ProFormSelect
        name="accountId"
        label={t('personnel.leave.employee')}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={() => accountListQuerySimple({})}
        rules={[{ required: true, message: t('personnel.leave.employee.placeholder') }]}
      />
      <ProFormSelect
        name="type"
        label={t('personnel.leave.leaveType')}
        options={[
          { label: LeaveTypeOptions[LeaveType.Annual_Leave].text, value: LeaveType.Annual_Leave },
          { label: LeaveTypeOptions[LeaveType.Long_Service_Leave].text, value: LeaveType.Long_Service_Leave },
        ]}
        rules={[{ required: true, }]}
      />
      <ProFormDigit
        name="hours"
        label={t('personnel.leave.payLeaveHours')}
        fieldProps={{
          addonAfter: t('personnel.leave.hoursUnit'),
          precision: 1,
          min: 0,
        }}
        rules={[{ required: true, }]}

      />
      <ProFormDigit
        name="paidAmount"
        label={t('personnel.leave.paidAmount')}
        fieldProps={{
          precision: 2,
          min: 0,
        }}
        rules={[{ required: true }]}
      />
      <ProFormTextArea
        name="comments"
        label={t('personnel.leave.comments')}
        fieldProps={{ maxLength: 100, showCount: true }}
      />
    </ModalForm>
  );
};

export default PayLeaveModal;