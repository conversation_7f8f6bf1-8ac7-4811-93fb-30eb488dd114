import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Tabs } from 'antd';
import { useState } from 'react';
import LeaveBalance from './components/LeaveBalance';
import LeaveRecord from './components/LeaveRecord';

const Leave = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  const [activeKey, setActiveKey] = useState<string>('1');


  const items = [
    {
      key: '1',
      label: '请假记录',
    },
    {
      key: '2',
      label: '假期余额',
    },
  ];

  return (
    <PageContainer>
      <div className='bg-white'>
        <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
          <Tabs
            defaultActiveKey={activeKey}
            onChange={setActiveKey}
            items={items}
            tabBarStyle={{ marginBottom: 0 }}
          />
        </ProCard>

        {activeKey === '1' && <LeaveRecord />}

        {activeKey === '2' && <LeaveBalance />}
      </div>
    </PageContainer>
  );
};

export default withKeepAlive(Leave);
