export interface LeaveCalendar {
  /**
   * 员工申请假期
   */
  accountLeaveApplyList?: AccountLeaveApplyList[];
  /**
   * None
   */
  memberId?: string;
  /**
   * 公共假期
   */
  publicHolidayList?: PublicHolidayList[];
}

export interface AccountLeaveApplyList {
  /**
   * 请假人账户id
   */
  accountId?: string;
  /**
   * 请假人账户名称
   */
  accountName?: string;
  /**
   * 审核人账户id
   */
  approveAccountId?: number;
  /**
   * 审核人账户名称
   */
  approveAccountName?: string;
  /**
   * 审核备注
   */
  approveComments?: string;
  /**
   * 提交时间
   */
  approveTime?: string;
  /**
   * 请假备注
   */
  comments?: string;
  /**
   * 请假结束时间
   */
  endTime?: string;
  /**
   * 兑换方式1-请假2-支付假期
   */
  exchangeChannel?: number;
  /**
   * 兑换方式1-请假2-支付假期
   */
  exchangeChannelDesc?: string;
  /**
   * 请假时长
   */
  hours?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片
   */
  images?: Image[];
  /**
   * None
   */
  memberId?: string;
  /**
   * 支付金额
   */
  paidAmount?: string;
  /**
   * 请假原因
   */
  reason?: string;
  /**
   * 请假开始时间
   */
  startTime?: string;
  /**
   * 审核状态
   */
  state?: number;
  /**
   * 审核状态
   */
  stateDesc?: string;
  /**
   * 账户id所归属门店id
   */
  storeId?: string;
  /**
   * 账户id所归属门店id
   */
  storeName?: string;
  /**
   * 提交人账户id
   */
  submitAccountId?: number;
  /**
   * 提交人账户名称
   */
  submitAccountName?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 假期类型
   */
  type?: number;
  /**
   * 假期类型
   */
  typeDesc?: string;
}

export interface Image {
  /**
   * name
   */
  name?: string;
  /**
   * 地址
   */
  url?: string;
}

export interface PublicHolidayList {
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 假期名称
   */
  name?: string;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 状态1-启用0-禁用
   */
  state?: number;
  /**
   * 状态1-启用0-禁用
   */
  stateDesc?: string;
}
