export enum LeaveType {
  /**
   * 年假
   */
  Annual_Leave = 1,
  /**
   * 病假
   */
  Sick_Leave = 2,
  /**
   * 长期服务假
   */
  Long_Service_Leave = 3,
  /**
   * 无薪假
   */
  Unpaid_Leave = 4,
  /**
   * 其他假期
   **/
  Other_Leave = 5,
}

export const LeaveTypeOptions = {
  [LeaveType.Annual_Leave]: { text: '年假' },
  [LeaveType.Sick_Leave]: { text: '病假' },
  [LeaveType.Long_Service_Leave]: { text: '长期服务假' },
  [LeaveType.Unpaid_Leave]: { text: '无薪假' },
  [LeaveType.Other_Leave]: { text: '其他假期' },
};


export enum LeaveExchangeChannel {
  // LEAVE(1, "Leave", "请假"),
  Leave = 1,
  // PAY_LEAVE(2, "Pay leave", "支付假期"),
  Pay_Leave = 2,
  // SYSTEM_RESET(3, "System reset", "系统重置");
  System_Reset = 3,
}

export const LeaveExchangeChannelOptions = {
  [LeaveExchangeChannel.Leave]: { text: '请假' },
  [LeaveExchangeChannel.Pay_Leave]: { text: '支付假期' },
  [LeaveExchangeChannel.System_Reset]: { text: '系统重置' },
};

export enum LeaveStateEnum {
  // Pending_Review (1, "Pending Review", "待审核"),
  Pending_Review = 1,
  // Approved(2, "Approved", "审核通过"),
  Approved = 2,
  // Rejected(3, "Rejected", "审核拒绝"),
  Rejected = 3,
  // Cancelled(4, "Cancelled", "已取消");
  Cancelled = 4,
}

export const LeaveStateOptions = {
  [LeaveStateEnum.Pending_Review]: { text: '待审核', status: 'pending' },
  [LeaveStateEnum.Approved]: { text: '审核通过', status: 'success' },
  [LeaveStateEnum.Rejected]: { text: '审核拒绝', status: 'error' },
  [LeaveStateEnum.Cancelled]: { text: '已取消', status: 'default' },
};