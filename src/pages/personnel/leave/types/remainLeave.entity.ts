export interface RemainLeaveEntity {
  /**
   * 请假人账户id
   */
  accountId?: string;
  /**
   * 明细列表
   */
  accountLeaveAggregationList?: AccountLeaveAggregationList[];
  /**
   * 请假人账户id
   */
  accountName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 服务假剩余时长
   */
  serviceBalanceHours?: number;
  /**
   * 服务假剩余时长(天)
   */
  serviceBalanceHoursConvertDay?: number;
  /**
   * 服务假剩余时长(周)
   */
  serviceBalanceHoursConvertWeek?: number;
  /**
   * 服务假少于7年标签
   */
  serviceLeaveLessThan7Years?: boolean;
  /**
   * 服务假累计时长
   */
  serviceTotalHours?: number;
  /**
   * 服务假累计时长(天)
   */
  serviceTotalHoursConvertDay?: number;
  /**
   * 服务假累计时长(周)
   */
  serviceTotalHoursConvertWeek?: number;
  /**
   * 服务假已用时长
   */
  serviceUsedHours?: number;
  /**
   * 服务假已用时长(天)
   */
  serviceUsedHoursConvertDay?: number;
  /**
   * 服务假已用时长(周)
   */
  serviceUsedHoursConvertWeek?: number;
  /**
   * 病假剩余时长
   */
  sickBalanceHours?: number;
  /**
   * 病假剩余时长(天)
   */
  sickBalanceHoursConvertDay?: number;
  /**
   * 病假剩余时长(周)
   */
  sickBalanceHoursConvertWeek?: number;
  /**
   * 病假累计时长
   */
  sickTotalHours?: number;
  /**
   * 病假累计时长(天)
   */
  sickTotalHoursConvertDay?: number;
  /**
   * 病假累计时长(周)
   */
  sickTotalHoursConvertWeek?: number;
  /**
   * 病假已用时长
   */
  sickUsedHours?: number;
  /**
   * 病假已用时长(天)
   */
  sickUsedHoursConvertDay?: number;
  /**
   * 病假已用时长(周)
   */
  sickUsedHoursConvertWeek?: number;
  /**
   * 年假剩余时长
   */
  yearBalanceHours?: number;
  /**
   * 年假剩余时长(天)
   */
  yearBalanceHoursConvertDay?: number;
  /**
   * 年假剩余时长(周)
   */
  yearBalanceHoursConvertWeek?: number;
  /**
   * 本年年假清零时长
   */
  yearResetHours?: number;
  /**
   * 本年年假清零时长(天)
   */
  yearResetHoursConvertDay?: number;
  /**
   * 本年年假清零时长(周)
   */
  yearResetHoursConvertWeek?: number;
  /**
   * 年假累计时长
   */
  yearTotalHours?: number;
  /**
   * 年假累计时长(天)
   */
  yearTotalHoursConvertDay?: number;
  /**
   * 年假累计时长(周)
   */
  yearTotalHoursConvertWeek?: number;
  /**
   * 年假已用时长
   */
  yearUsedHours?: number;
  /**
   * 年假已用时长(天)
   */
  yearUsedHoursConvertDay?: number;
  /**
   * 年假已用时长(周)
   */
  yearUsedHoursConvertWeek?: number;
}

export interface AccountLeaveAggregationList {
  /**
   * 账户id
   */
  accountId?: number;
  /**
   * 账户名称
   */
  accountName?: string;
  /**
   * 当前总时长
   */
  currentTotalHours?: number;
  /**
   * 已用总时长
   */
  currentUsedHours?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * 锁定用时长(申请中)
   */
  lockedHours?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 超期清零时间，为空不清零
   */
  resetTime?: string;
  /**
   * 总累计时长(只叠加不减少)
   */
  totalHours?: number;
  /**
   * 假期类型
   */
  type?: number;
  /**
   * version
   */
  version?: number;
  /**
   * 年(针对需要清零的类型，按年生成一条记录)
   */
  year?: number;
}
