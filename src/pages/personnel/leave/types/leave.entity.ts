import { LeaveExchangeChannel, LeaveStateEnum, LeaveType } from "./leave.enum";

export interface LeaveApplyEntity {
  /**
   * 请假人账户id
   */
  accountId?: string;
  /**
   * 审核人账户id
   */
  approveAccountId?: string;
  /**
   * 请假备注
   */
  comments?: string;
  /**
   * 请假结束时间
   */
  endTime?: string;
  /**
   * 兑换方式1-请假2-支付假期3-系统清零
   */
  exchangeChannel?: LeaveExchangeChannel;
  /**
   * None
   */
  extRemark?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * 请假时长
   */
  hours?: number;
  /**
   * 图片
   */
  images?: Image[];
  /**
   * None
   */
  lastName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 支付金额
   */
  paidAmount?: string;
  /**
   * 请假原因
   */
  reason?: string;
  /**
   * 请假开始时间
   */
  startTime?: string;
  /**
   * 审核状态
   */
  state?: LeaveStateEnum;
  /**
   * 账户id所归属门店id
   */
  storeId?: string;
  /**
   * 提交人账户id
   */
  submitAccountId?: string;
  /**
   * 假期类型
   */
  type?: LeaveType;
}

export interface PayLeaveEntity {
  /**
   * 请假人账户id
   */
  accountId?: string;
  /**
   * 请假时长
   */
  hours?: number;
  /**
   * 假期类型
   */
  type?: LeaveType;
  /**
   * 支付金额
   */
  paidAmount?: string;
  /**
  * 请假备注
  */
  comments?: string;
}

export interface LeaveEntity {
  /**
   * 请假人账户id
   */
  accountId?: string;
  /**
   * 请假人账户名称
   */
  accountName?: string;
  /**
   * 审核人账户id
   */
  approveAccountId?: number;
  /**
   * 审核人账户名称
   */
  approveAccountName?: string;
  /**
   * 提交时间
   */
  approveTime?: string;
  /**
   * 请假备注
   */
  comments?: string;
  /**
   * 请假结束时间
   */
  endTime?: string;
  /**
   * 兑换方式1-请假2-支付假期
   */
  exchangeChannel?: LeaveExchangeChannel;
  /**
   * 兑换方式1-请假2-支付假期
   */
  exchangeChannelDesc?: string;
  /**
   * 请假时长
   */
  hours?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片
   */
  images?: Image[];
  /**
   * none
   */
  memberId?: string;
  /**
   * 支付金额
   */
  paidAmount?: string;
  /**
   * 请假原因
   */
  reason?: string;
  /**
   * 请假开始时间
   */
  startTime?: string;
  /**
   * 审核状态
   */
  state?: LeaveStateEnum;
  /**
   * 审核状态
   */
  stateDesc?: string;
  /**
   * 账户id所归属门店id
   */
  storeId?: string;
  /**
   * 账户id所归属门店id
   */
  storeName?: string;
  /**
   * 提交人账户id
   */
  submitAccountId?: number;
  /**
   * 提交人账户名称
   */
  submitAccountName?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 假期类型
   */
  type?: LeaveType;
  /**
   * 假期类型
   */
  typeDesc?: string;
}


export interface ApproveLeaveApplyRequest {
  id?: string;
  /**
    * 审核备注
    */
  approveComments?: string;
}


export interface Image {
  /**
   * name
   */
  name?: string;
  /**
   * 地址
   */
  url?: string;
}
