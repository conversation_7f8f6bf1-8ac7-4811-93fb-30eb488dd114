import FunProTable from '@/components/common/FunProTable';
import React, { useRef, useState } from 'react';
import { HolidayEntity } from '../../types/holiday.entity';
import { holidayInsert, holidayUpdate, queryHolidayList } from '../../services';
import type { ActionType } from '@ant-design/pro-table/lib';
import { message, Space } from 'antd';
import AuthButton from '@/components/common/AuthButton';
import { useActivate } from 'react-activation';
import { positionEnum, PositionState } from '../../types/position.state';
import { useIntl } from 'umi';
import {
  ModalForm,
  ProFormDateRangePicker,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';

export default function Holiday() {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [showAdd, setShowAdd] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  useActivate(() => {
    actionRef.current?.reload();
  });

  // 更新
  const onEditSave = async (_key: React.Key | React.Key[], record: HolidayEntity) => {
    const result = await holidayUpdate(record);
    if (!result) {
      return Promise.reject();
    }
    message.success('修改成功');
    return true;
  };

  // 新增
  const handleCreate = async (values: HolidayEntity) => {
    setLoading(true);
    holidayInsert(values)
      .then((res) => {
        if (res) {
          message.success('创建成功');
          actionRef.current?.reload();
          setShowAdd(false);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <FunProTable<HolidayEntity, any>
        rowKey="id"
        toolbar={{ settings: [] }}
        scroll={{ x: 800 }}
        ghost={true}
        form={{ className: '!p-0' }}
        requestPage={queryHolidayList}
        actionRef={actionRef}
        editable={{
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        columns={[
          {
            title: '序号',
            valueType: 'index',
            dataIndex: 'index',
            width: 60,
          },
          {
            title: '假期名称',
            dataIndex: 'name',
            width: 300,
            formItemProps: {
              rules: [{ required: true }],
            },
          },
          {
            title: '假期时间',
            valueType: 'dateRange',
            dataIndex: 'dateRange',
            search: false,
            formItemProps: {
              rules: [{ required: true }],
            },
            fieldProps: {
              // @ts-ignore
              transform: (value) => {
                return {
                  startTime: value[0] ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00') : undefined,
                  endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : undefined,
                };
              },
            },
          },
          {
            title: '是否启用',
            dataIndex: 'state',
            width: 120,
            search: false,
            valueEnum: positionEnum,
          },
          {
            title: intl.formatMessage({ id: 'common.column.operation' }),
            valueType: 'option',
            width: 120,
            fixed: 'right',
            align: 'center',
            render: (_text, record, _, action) => {
              return (
                <AuthButton
                  isHref
                  authority=""
                  key="create"
                  onClick={() => {
                    // @ts-ignore
                    action?.startEditable?.(record.id);
                  }}
                >
                  {intl.formatMessage({ id: 'common.button.edit' })}
                </AuthButton>
              );
            },
          },
        ]}
        headerTitle={
          <Space>
            <AuthButton authority="" type="primary" onClick={() => setShowAdd(true)}>
              新增假期
            </AuthButton>
          </Space>
        }
      />
      <ModalForm
        title="新增公共假期"
        open={showAdd}
        onOpenChange={() => setShowAdd(false)}
        width={500}
        onFinish={handleCreate}
        modalProps={{
          destroyOnClose: true,
          okButtonProps: {
            loading,
          },
        }}
      >
        <ProFormText name="name" label="假期名称" rules={[{ required: true }]} />
        <ProFormDateRangePicker
          name="dateRange"
          label="假期时间"
          rules={[{ required: true }]}
          transform={(value) => {
            return {
              startTime: value[0] ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00') : undefined,
              endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : undefined,
            };
          }}
        />
        <ProFormRadio.Group
          valueEnum={positionEnum}
          label="状态"
          name="state"
          initialValue={PositionState.Enabled}
        />
      </ModalForm>
    </>
  );
}
