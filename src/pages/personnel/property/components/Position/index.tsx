import FunProTable from '@/components/common/FunProTable';
import React, { useRef, useState } from 'react';
import { PositionEntity } from '../../types/position.entity';
import { positionInsert, positionUpdate, queryPositionList } from '../../services';
import type { ActionType } from '@ant-design/pro-table/lib';
import { message, Space } from 'antd';
import AuthButton from '@/components/common/AuthButton';
import { useActivate } from 'react-activation';
import { positionEnum, PositionState } from '../../types/position.state';
import { useIntl } from 'umi';
import { ModalForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';

export default function Position() {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [showAdd, setShowAdd] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  useActivate(() => {
    actionRef.current?.reload();
  });

  // 更新
  const onEditSave = async (_key: React.Key | React.Key[], record: PositionEntity) => {
    const result = await positionUpdate(record);
    if (!result) {
      return Promise.reject();
    }
    message.success('修改成功');
    return true;
  };

  // 新增
  const handleCreate = async (values: PositionEntity) => {
    setLoading(true);
    positionInsert(values)
      .then((res) => {
        if (res) {
          message.success('创建成功');
          actionRef.current?.reload();
          setShowAdd(false);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <FunProTable<PositionEntity, any>
        rowKey="id"
        toolbar={{ settings: [] }}
        scroll={{ x: 800 }}
        ghost={true}
        form={{ className: '!p-0' }}
        requestPage={queryPositionList}
        actionRef={actionRef}
        editable={{
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        columns={[
          {
            title: '序号',
            valueType: 'index',
            dataIndex: 'index',
            width: 60,
          },
          {
            title: '职位名称',
            dataIndex: 'name',
            formItemProps: {
              rules: [{ required: true }],
            },
          },
          {
            title: '是否启用',
            dataIndex: 'state',
            search: false,
            valueEnum: positionEnum,
          },
          {
            title: intl.formatMessage({ id: 'common.column.operation' }),
            valueType: 'option',
            width: 120,
            fixed: 'right',
            align: 'center',
            render: (_text, record, _, action) => {
              return (
                <AuthButton
                  isHref
                  authority=""
                  key="create"
                  onClick={() => {
                    // @ts-ignore
                    action?.startEditable?.(record.id);
                  }}
                >
                  {intl.formatMessage({ id: 'common.button.edit' })}
                </AuthButton>
              );
            },
          },
        ]}
        headerTitle={
          <Space>
            <AuthButton authority="" type="primary" onClick={() => setShowAdd(true)}>
              新增职位
            </AuthButton>
          </Space>
        }
      />
      <ModalForm
        title="新增职位"
        open={showAdd}
        onOpenChange={() => setShowAdd(false)}
        width={500}
        onFinish={handleCreate}
        modalProps={{
          destroyOnClose: true,
          okButtonProps: {
            loading,
          },
        }}
      >
        <ProFormText name="name" label="职位名称" rules={[{ required: true }]} />
        <ProFormRadio.Group
          valueEnum={positionEnum}
          label="状态"
          name="state"
          initialValue={PositionState.Enabled}
        />
      </ModalForm>
    </>
  );
}
