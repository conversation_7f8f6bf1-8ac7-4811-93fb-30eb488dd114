import { PageContainer, ProCard } from '@ant-design/pro-components';
import Position from './components/Position';
import Holiday from './components/Holiday';

export default function PersonnelProperty() {
  const items = [
    {
      key: 'position',
      label: '职位管理',
      children: <Position />,
    },
    {
      key: 'holiday',
      label: '公共假期',
      children: <Holiday />,
    },
  ];
  return (
    <PageContainer>
      <ProCard tabs={{ items }}></ProCard>
    </PageContainer>
  );
}
