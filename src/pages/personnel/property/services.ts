import { request } from '@/utils/request';
import { QueryPositionListRequest } from './types/query.position.list.request';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { PositionEntity } from './types/position.entity';
import { QueryHolidayListRequest } from './types/query.holiday.list.request';
import { HolidayEntity } from './types/holiday.entity';

/**
 * 分页查询职位列表
 * @param params
 */
export const queryPositionList = async (params: QueryPositionListRequest) => {
  return request<PageResponseDataType<PositionEntity>>(`/ipmspassport/PositionFacade/pageQuery`, {
    data: params,
  }).then((res) => {
    // @ts-ignore
    return { ...res, data: res.data?.map((item) => ({ ...item, state: item.state?.toString() })) };
  });
};

/**
 * 新增职位
 * @param params
 */
export const positionInsert = async (params: PositionEntity) => {
  return request<boolean>(`/ipmspassport/PositionFacade/insert`, {
    data: params,
  });
};

/**
 * 更新职位
 * @param params
 */
export const positionUpdate = async (params: PositionEntity) => {
  return request<boolean>(`/ipmspassport/PositionFacade/update`, {
    data: params,
  });
};

/**
 * 分页查询公共假期列表
 */
export const queryHolidayList = async (params: QueryHolidayListRequest) => {
  return request<PageResponseDataType<HolidayEntity>>(
    `/ipmspassport/PublicHolidayFacade/pageQuery`,
    {
      data: params,
    },
  ).then((res) => {
    return {
      ...res,
      // @ts-ignore
      data: res.data?.map((item) => ({
        ...item,
        state: item.state?.toString(),
        dateRange: [item.startTime, item.endTime],
      })),
    };
  });
};

/**
 * 新增假期
 * @param params
 */
export const holidayInsert = async (params: HolidayEntity) => {
  return request<boolean>(`/ipmspassport/PublicHolidayFacade/insert`, {
    data: params,
  });
};

/**
 * 更新假期
 * @param params
 */
export const holidayUpdate = async (params: HolidayEntity) => {
  return request<boolean>(`/ipmspassport/PublicHolidayFacade/update`, {
    data: params,
  });
};
