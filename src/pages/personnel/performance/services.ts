import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { PerformanceEntity } from './types/performance.entity';


export const queryPerformancePage = (params: Partial<PerformanceEntity> & PageRequestParamsType): Promise<PageResponseDataType<PerformanceEntity>> => {
  return request('/ipmspassport/PerformanceFacade/pageQuery', {
    data: params,
  });
};

/**
 * 作废
 */
export const cancelPerformance = (params: { id: string }): Promise<boolean> => {
  return request('/ipmspassport/PerformanceFacade/cancel', {
    data: params,
  });
};