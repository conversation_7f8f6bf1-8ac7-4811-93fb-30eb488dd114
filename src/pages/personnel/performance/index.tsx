import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import ProFormPerformanceDate from '@/components/ProFormItem/ProFormPerformanceDate';
import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useIntl } from '@@/exports';
import { PageContainer } from '@ant-design/pro-components';
import { message, Popconfirm, Space } from 'antd';
import React from 'react';
import { cancelPerformance, queryPerformancePage } from './services';
import { PerformanceEntity } from './types/performance.entity';

const PerformancePage = () => {
  const intl = useIntl();
  const actionRef = React.createRef<any>();
  const formRef = React.createRef<any>();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

  const handleCancel = async (id: string) => {
    const res = await cancelPerformance({ id });
    if (res) {
      message.success(t('common.message.operationSuccess'));
      actionRef.current?.reload();
    }
  };

  const columns: any[] = [
    {
      title: t('common.column.index'),
      dataIndex: 'index',
      width: 120,
      valueType: 'index',
    },
    {
      title: t('personnel.performance.performanceTime'),
      dataIndex: 'dateMonth',
      width: 120,
      renderFormItem: (_, record) => {
        return <ProFormPerformanceDate form={formRef.current} />;
      },
    },
    {
      title: t('personnel.performance.employee'),
      dataIndex: 'name',
      width: 120,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: t('personnel.performance.performanceItem'),
      dataIndex: 'indicator',
      width: 120,
    },
    {
      title: t('personnel.performance.performanceValue'),
      dataIndex: 'value',
      width: 120,
      search: false,
    },
    {
      title: t('personnel.performance.performanceDesc'),
      dataIndex: 'instruction',
      width: 120,
      search: false,
    },
    {
      title: t('personnel.performance.updatedBy'),
      dataIndex: 'updatePerson',
      width: 120,
      search: false,
    },
    {
      title: t('personnel.performance.updatedAt'),
      dataIndex: 'updateTime',
      width: 120,
      valueType: 'date',
      search: false,
    },
    {
      title: t('personnel.performance.status'),
      dataIndex: 'state',
      width: 120,
      valueType: 'select',
      valueEnum: {
        1: { text: t('common.option.active'), status: 'Success' },
        0: { text: t('common.option.inactive'), status: 'Error' },
      },
      search: false,
    },
    {
      title: t('common.column.operation'),
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      render: (text, record, _, action) => [
        <Popconfirm
          key="cancel"
          title={t('common.tip.confirm.action', { action: t('personnel.performance.button.cancel') })}
          onConfirm={() => handleCancel(record.id)}
        >
          <AuthButton isHref>
            {intl.formatMessage({ id: 'personnel.performance.button.cancel' })}
          </AuthButton>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <FunProTable<PerformanceEntity, any>
        rowKey="id"
        requestPage={queryPerformancePage}
        scroll={{ x: 'max-content' }}
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        headerTitle={
          <Space>
            <AuthButton
              key="import_supplier"
              type="primary"
              ghost
              onClick={() => {
                importData({
                  moduleId: 'PERFORMANCE_IMPORT',
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: '导入绩效数据',
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E4%BE%9B%E5%BA%94%E5%95%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                  onSuccess: () => {

                    actionRef.current?.reload();
                  }
                });
              }}
            >
              {intl.formatMessage({ id: 'personnel.performance.button.import' })}
            </AuthButton>
            <AuthButton
              type="primary"
              ghost
              onClick={() => {
                exportData({
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: '导出绩效数据',
                  moduleId: 'PERFORMANCE_EXPORT',
                  params: formRef.current?.getFieldsFormatValue?.(),
                });
              }}
            >
              {intl.formatMessage({ id: 'personnel.performance.button.export' })}
            </AuthButton>
          </Space>
        }
      />
    </PageContainer>
  );
};

export default withKeepAlive(PerformancePage);
