export interface PerformanceEntity {
  /**
   * 员工账号
   */
  accountId?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 绩效时间
   */
  dateMonth?: string;
  /**
   * firstName
   */
  firstName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 绩效项
   */
  indicator?: string;
  /**
   * 绩效说明
   */
  instruction?: string;
  /**
   * lastName
   */
  lastName?: string;
  /**
   * none
   */
  memberId?: string;
  /**
   * firstName和lastName拼接
   */
  name?: string;
  /**
   * 状态1-生效0-失效
   */
  state?: number;
  /**
   * 状态1-生效0-失效
   */
  stateDesc?: string;
  /**
   * 类型1-年度2-月度
   */
  type?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 绩效值
   */
  value?: string;
}
