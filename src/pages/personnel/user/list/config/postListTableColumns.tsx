import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space, Tag } from 'antd';
import { postStatusOptions, setStatusValue, statusAttribute } from '../types/PostStatus';
import type { PostEntity } from '../types/post.entity';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { hireTypeOptions } from '@/pages/personnel/user/list/config/hire.type';
import { hireStatusOptions } from '@/pages/personnel/user/list/config/hire.status';
import { queryPositionList } from '@/pages/personnel/property/services';
import { PositionState } from '@/pages/personnel/property/types/position.state';

export interface PostListTableColumnsProps {
  handleDeleteItem: (id: string, status: string) => void;
  handleUpdateItem: (id: string) => void;
  intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.employeeId' }),
      dataIndex: 'id',
      key: 'id',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.employeeName' }),
      dataIndex: 'name',
      key: 'name',
      search: true,
      width: 80,
      ellipsis: true,
      render: (text, record) => {
        return (
          <Space>
            <span>{text}</span>
            {record.type == '0' && (
              <Tag color="blue">
                {props.intl.formatMessage({ id: 'system.user.list.mainAccount' })}
              </Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.gender' }),
      dataIndex: 'gender',
      key: 'gender',
      search: false,
      width: 60,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.phone' }),
      dataIndex: 'phone',
      key: 'phone',
      search: true,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.email' }),
      dataIndex: 'email',
      key: 'email',
      search: true,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.store' }),
      dataIndex: 'storeNames',
      formItemProps: {
        name: 'storeIdList',
      },
      key: 'storeNames',
      search: true,
      width: 120,
      ellipsis: true,
      fieldProps: {
        mode: 'multiple',
        showSearch: true,
        maxTagCount: 3,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => queryStoreByAccount({ status: 1 }),
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.category' }),
      formItemProps: {
        name: 'categoryList',
      },
      dataIndex: 'category',
      key: 'category',
      search: true,
      width: 80,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        options: hireTypeOptions,
      },
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.position' }),
      dataIndex: 'positionNames',
      key: 'position',
      search: true,
      width: 80,
      formItemProps: {
        name: 'positionIdList',
      },
      fieldProps: {
        mode: 'multiple',
        showSearch: true,
        maxTagCount: 3,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () =>
        queryPositionList({ state: PositionState.Enabled, pageSize: 9999, pageNo: 1 }).then(
          (res) => res?.data ?? [],
        ),
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.role' }),
      dataIndex: 'roleNames',
      key: 'roleNames',
      width: 80,
      search: false,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.hireState' }),
      dataIndex: 'hireState',
      key: 'hireState',
      search: false,
      width: 80,
      valueType: 'select',
      fieldProps: {
        options: hireStatusOptions,
      },
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.remark' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 160,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'status',
      key: 'status',
      search: false,
      width: 60,
      valueEnum: postStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (_, record: PostEntity) => (
        <Space>
          <AuthButton
            authority="editUser"
            isHref
            onClick={() => props.handleUpdateItem(record.id ?? '')}
          >
            {props.intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          {record.type != '0' && (
            <Popconfirm
              title={props.intl.formatMessage(
                { id: 'system.user.list.confirm.enableDisable' },
                { action: props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] }) },
              )}
              onConfirm={() =>
                props.handleDeleteItem(record.id ?? '', setStatusValue[record.status ?? ''])
              }
            >
              <AuthButton authority="enableOrDisableUser" isHref>
                {props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] })}
              </AuthButton>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
