import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { message, Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { insertUser, modifyStatusPost, queryPostList, updateUser } from '../services';
import FormModal from './components/modal';
import { PostListTableColumns } from './config/postListTableColumns';
import { type PostEntity } from './types/post.entity';
import { UserEntity } from '@/pages/personnel/user/list/types/user.entity';
import dayjs from 'dayjs';

const UserList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [userModalProps, setUserModalProps] = useState<CommonModelForm<string, PostEntity>>({
    visible: false,
    readOnly: false,
    title: '',
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用事件
   * @param id
   * @param status
   */
  const handleDeleteItem = async (id: string, status: string) => {
    await modifyStatusPost({ id, status });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (id: string) => {
    setUserModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'system.user.list.editEmployee' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setUserModalProps({
      visible: false,
      readOnly: false,
      title: '',
    });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: UserEntity) => {
    // 格式化工作时间，组件的format在深层嵌套下有bug，导致编辑时间后格式为年月日时分秒
    // https://github.com/ant-design/pro-components/issues/5873
    values.accountEmployment?.accountWorkingScheduleList?.forEach((m) => {
      m.scheduledHours?.forEach((n) => {
        if (dayjs(n.startTime).isValid()) {
          n.startTime = dayjs(n.startTime).format('HH:mm');
        }
        if (dayjs(n.endTime).isValid()) {
          n.endTime = dayjs(n.endTime).format('HH:mm');
        }
      });
    });
    // 格式化地址
    // @ts-ignore
    const area = values.account?.addressList?.[0]?.areaCode || [];
    if (area.length === 2) {
      values.account!.addressList![0].provinceCode = area[0];
      values.account!.addressList![0].prefectureCode = area[1];
      // @ts-ignore
      delete values.account?.addressList?.[0]?.areaCode;
    }
    setLoading(true);
    if (values.account?.id) {
      updateUser(values)
        .then((res) => {
          if (res) {
            hideModal();
            actionRef.current?.reload(true);
            message.success('更新成功');
          }
        })
        .finally(() => setLoading(false));
    } else {
      insertUser(values)
        .then((res) => {
          if (res) {
            hideModal();
            actionRef.current?.reload(true);
            message.success('创建成功');
          }
        })
        .finally(() => setLoading(false));
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        // @ts-ignore
        requestPage={queryPostList}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
          intl,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addUser"
              onClick={() =>
                setUserModalProps({
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'system.user.list.addEmployee' }),
                })
              }
            >
              {intl.formatMessage({ id: 'common.button.add' })}
            </AuthButton>
          </Space>
        }
      />
      <FormModal
        {...userModalProps}
        loading={loading}
        onOk={handleSaveOrUpdate}
        onCancel={hideModal}
      />
    </PageContainer>
  );
};
export default withKeepAlive(UserList);
