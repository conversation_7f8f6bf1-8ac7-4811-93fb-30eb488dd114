import { ProForm } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import { EditableProTable } from '@ant-design/pro-table';
import { Input, Upload } from 'antd';
import { FileTextOutlined, UploadOutlined } from '@ant-design/icons';

export interface FileUploadListProps {
  title?: React.ReactNode;
  form?: any;
  name?: string;
}

export default function FileUploadList(props: FileUploadListProps) {
  const { title, form, name } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [refreshKey, setRefreshKey] = useState(1);

  const getFileName = (url: string) => {
    if (!url) return '';
    return url.split('/').pop() || url;
  };

  const columns = [
    {
      title: '序号',
      width: 60,
      dataIndex: 'index',
      valueType: 'index',
    },
    {
      title: '文件类型',
      dataIndex: 'name',
      width: 300,
      formItemProps: {
        rules: [{ required: true, message: '请输入文件类型' }],
      },
      // @ts-ignore
      renderFormItem: (item, { record, ...rest }) => {
        const images = form.getFieldValue([name, 'images']) || [];
        const index = images.findIndex((item: any) => item.id === record.id);
        return (
          <Input
            defaultValue={record?.name}
            onChange={(e) => {
              form.setFieldValue([name, 'images', index, 'name'], e.target.value);
            }}
          />
        );
      },
    },
    {
      title: '文件',
      dataIndex: 'url',
      valueType: 'formItem',
      formItemProps: {
        rules: [{ required: true, message: '请上传文件' }],
      },
      // @ts-ignore
      renderFormItem: (_, { record }) => {
        const images = form.getFieldValue([name, 'images']) || [];
        const index = images.findIndex((item: any) => item.id === record.id);
        const newUrl = record.url;
        const fileName = getFileName(newUrl);
        return (
          <div className="flex gap-5 items-center">
            <Upload
              showUploadList={false}
              maxCount={1}
              action="/apigateway/public/upload/object/batch"
              onChange={(info) => {
                const url = info.file?.response?.data?.[0];
                if (url) {
                  form.setFieldValue([name, 'images', index, 'url'], url);
                  setRefreshKey((pre) => pre + 1);
                }
              }}
            >
              <a className="flex gap-1">
                <UploadOutlined />
                {fileName ? '重新上传' : '点击上传'}
              </a>
            </Upload>
            {fileName && (
              <div className="flex items-center gap-1">
                <FileTextOutlined />
                {fileName}
              </div>
            )}
          </div>
        );
      },
      // @ts-ignore
      render: (_, record) =>
        record?.url ? (
          <div className="flex items-center gap-1">
            <FileTextOutlined />
            <a href={record.url} target="_blank" rel="noopener noreferrer">
              {getFileName(record.url)}
            </a>
          </div>
        ) : (
          '未上传'
        ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      // @ts-ignore
      render: (text, record: any, _: any, action: any) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            const current = form.getFieldValue([name, 'images']) || [];
            const newList = current.filter((item: any) => item.id !== record.id);
            form.setFieldValue([name, 'images'], newList);
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <div>
      <ProForm.Item label={<div className="font-medium">{title}</div>} name={[name, 'images']}>
        <EditableProTable
          key={refreshKey}
          rowKey="id"
          ghost={true}
          // @ts-ignore
          columns={columns}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'bottom',
            record: () => ({
              id: Date.now(),
            }),
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.delete],
          }}
        />
      </ProForm.Item>
    </div>
  );
}
