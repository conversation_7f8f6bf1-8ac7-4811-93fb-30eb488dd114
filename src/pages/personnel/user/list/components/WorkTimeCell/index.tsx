import { FormListActionType, ProFormList, ProFormTimePicker } from '@ant-design/pro-components';
import { CloseCircleOutlined } from '@ant-design/icons';
import { useRef } from 'react';
import { Button } from 'antd';

const timePickerFieldProps: any = {
  minuteStep: 30,
  format: 'HH:mm',
  showNow: false,
  variant: 'outlined',
  needConfirm: false,
};

export default function WorkTimeCell() {
  const actionRef = useRef<
    FormListActionType<{
      startTime: string;
      endTime: string;
    }>
  >();

  return (
    <div className="mx-2">
      <div className="text-center mt-2">
        <Button
          type={'link'}
          onClick={() => {
            actionRef.current?.add();
          }}
        >
          添加时间段
        </Button>
      </div>
      <ProFormList
        name="scheduledHours"
        creatorButtonProps={false}
        actionRef={actionRef}
        itemRender={(_doms, listMeta) => (
          <div className="border border-solid border-[#dddddd] px-2 py-4 relative mb-3">
            <CloseCircleOutlined
              className="cursor-pointer absolute right-1 top-1"
              onClick={() => {
                actionRef.current?.remove(listMeta.index);
              }}
            />
            <div>
              <div className="mb-1">上班</div>
              <ProFormTimePicker
                name="startTime"
                formItemProps={{ className: 'mb-0' }}
                fieldProps={{ ...timePickerFieldProps }}
                rules={[{ required: true }]}
              />
            </div>
            <div className="mt-3">
              <div className="mb-1">下班</div>
              <ProFormTimePicker
                name="endTime"
                formItemProps={{ className: 'mb-0' }}
                fieldProps={{
                  ...timePickerFieldProps,
                }}
                rules={[{ required: true }]}
              />
            </div>
          </div>
        )}
      />
    </div>
  );
}
