export enum WorkdayEnum {
  SUNDAY = 1,
  MONDAY,
  TUESDAY,
  WEDNESDAY,
  THURSDAY,
  FRIDAY,
  SATURDAY,
}

export enum WorkdayEnumName {
  SUNDAY = 'SUNDAY',
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
}

export const workdayEnumOptions = [
  {
    label: WorkdayEnumName.MONDAY,
    value: WorkdayEnum.MONDAY,
    isDefault: true,
  },
  {
    label: WorkdayEnumName.TUESDAY,
    value: WorkdayEnum.TUESDAY,
    isDefault: true,
  },
  {
    label: WorkdayEnumName.WEDNESDAY,
    value: WorkdayEnum.WEDNESDAY,
    isDefault: true,
  },
  {
    label: WorkdayEnumName.THURSDAY,
    value: WorkdayEnum.THURSDAY,
    isDefault: true,
  },
  {
    label: WorkdayEnumName.FRIDAY,
    value: WorkdayEnum.FRIDAY,
    isDefault: true,
  },
  {
    label: WorkdayEnumName.SATURDAY,
    value: WorkdayEnum.SATURDAY,
  },
  {
    label: WorkdayEnumName.SUNDAY,
    value: WorkdayEnum.SUNDAY,
  },
];
