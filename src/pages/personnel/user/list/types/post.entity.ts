export interface PostEntity {
  /**
   * 雇佣类别
   */
  category?: number;
  /**
   * 雇佣类别
   */
  categoryDesc?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * firstName
   */
  firstName?: string;
  /**
   * 性别
   */
  gender?: string;
  /**
   * 雇佣状态
   */
  hireState?: number;
  /**
   * 雇佣状态
   */
  hireStateDesc?: string;
  /**
   * 账户ID
   */
  id?: string;
  /**
   * lastName
   */
  lastName?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 账号
   */
  name?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 关联职位名列表
   */
  positionNames?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 角色名列表
   */
  roleNames?: string;
  /**
   * 状态：0启用，1禁用，-1注销
   */
  status?: string;
  /**
   * 状态：0禁用，1启用，-1注销
   */
  statusDesc?: string;
  /**
   * 门店名列表
   */
  storeNames?: string;
  /**
   * 类型：0主账户，1子账户
   */
  type?: string;
}
