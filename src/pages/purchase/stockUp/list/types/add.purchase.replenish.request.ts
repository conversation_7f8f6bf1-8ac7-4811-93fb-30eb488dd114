export interface AddPurchaseReplenishRequest {
  /**
   * 门店系统账户id
   */
  accountId?: string;
  /**
   * 门店系统账户名称
   */
  accountName?: string;
  /**
   * 门店系统手机号
   */
  accountPhone?: string;
  /**
   * 门店系统零售商id
   */
  memberId?: string;
  /**
   * 补货类型：按库存补货BY_STOCK按销售补货BY_SALE
   */
  ruleType?: string;
  /**
   * None
   */
  saleCmd?: SaleCmd;
  /**
   * sessionId
   */
  sessionId?: string;
  /**
   * None
   */
  stockCmd?: StockCmd;
  /**
   * 建议类型：1-采购建议2-调拨建议
   */
  suggestionType?: string;
}

/**
 * None
 */
export interface SaleCmd {
  /**
   * 下单结束时间
   */
  endTime?: string;
  /**
   * 下单开始时间
   */
  startTime?: string;
  /**
   * 销售状态ids
   */
  statusIds?: number[];
  /**
   * 销售门店id
   */
  storeIds?: string[];
}

/**
 * None
 */
export interface StockCmd {
  /**
   * 补货子类型:低于下限补到上限LOW_UP低于上限补到上限UPPER_UP
   */
  ruleSubType?: string;
  /**
   * 补货仓库id
   */
  warehouseIds?: string[];
}
