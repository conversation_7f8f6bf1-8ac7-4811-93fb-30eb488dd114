import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { PostEntity } from './types/post.entity';
import { AddPurchaseReplenishRequest } from '@/pages/purchase/stockUp/list/types/add.purchase.replenish.request';

export const queryPostList = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(
    `/ipmspurchase/purchase/PurchaseSuggestionFacade/queryPurchaseSuggestionPage`,
    {
      data: params,
    },
  );
};

export const addPurchaseReplenish = async (params: AddPurchaseReplenishRequest) => {
  return request<boolean>(`/ipmspurchase/purchase/PurchaseReplenishFacade/addPurchaseReplenish`, {
    data: params,
  });
};
