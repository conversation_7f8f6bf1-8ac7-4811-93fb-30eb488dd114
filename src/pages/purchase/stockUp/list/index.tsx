import FunProTable from '@/components/common/FunProTable';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { PostListTableColumns } from './config/postListTableColumns';
import { addPurchaseReplenish, queryPostList } from './services';

import AuthButton from '@/components/common/AuthButton';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { Space } from 'antd';
import { useActivate } from 'react-activation';
import type { PostEntity } from './types/post.entity';

const StockUpList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleAgainItem = async (suggestionNo: string) => {
    history.push(`/purchase/stockUp/detail?suggestionNo=${suggestionNo}`);
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="suggestionNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        params={{
          suggestionType: '1',
        }}
        // @ts-ignore
        requestPage={queryPostList}
        columns={PostListTableColumns({
          intl,
          handleAgainItem,
        })}
        headerTitle={
          <AuthButton
            type="primary"
            authority=""
            loading={loading}
            onClick={() => {
              setLoading(true);
              addPurchaseReplenish({ suggestionType: '1' })
                .then((res) => {
                  if (res) {
                    actionRef.current?.reload();
                  }
                })
                .finally(() => {
                  setLoading(false);
                });
            }}
          >
            <Space>{intl.formatMessage({ id: 'purchase.stockUp.list.button.addStockUp' })}</Space>
          </AuthButton>
        }
      />
    </PageContainer>
  );
};
export default withKeepAlive(StockUpList);
