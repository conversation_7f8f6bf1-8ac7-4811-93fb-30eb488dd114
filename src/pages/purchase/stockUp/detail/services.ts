import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { DetailResponseEntity, ReplenishListItemEntity } from './types/detail.response.entity';

/**
 * 补货建议详情查询
 * @returns
 * @param suggestionNo
 */
export const queryPurchaseSuggestionDetail = async (suggestionNo: string) => {
  return request<DetailResponseEntity>(
    `/ipmspurchase/purchase/PurchaseSuggestionFacade/queryPurchaseSuggestionDetail`,
    {
      data: { suggestionNo },
    },
  );
};

/**
 * 查询补货单列表
 */
export const queryPurchaseReplenishListPage = async (params: PageRequestParamsType) => {
  return request<PageResponseDataType<ReplenishListItemEntity>>(
    `/ipmspurchase/purchase/PurchaseReplenishFacade/queryPurchaseReplenishListPage`,
    {
      data: params,
    },
  );
};
