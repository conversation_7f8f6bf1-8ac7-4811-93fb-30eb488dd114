import ConfirmModal from '@/components/ConfirmModal';
import GoodsPrintDrawer, { PrintItems } from '@/components/GoodsPrintDrawer';
import PaymentExternalFormModal from '@/components/PaymentExternalFormModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import SubTitle from '@/components/common/SubTitle';
import { RightOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { Flex, GetProps, Space, Tag } from 'antd';
import { add, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { history, useIntl, useSearchParams } from 'umi';
import CapitalFlow from '../capitalFlow';
import { confirmPayExternalPurchasePost, inStockExternalPurchasePost } from '../external/services';
import { balanceStatusOptions } from '../list/types/BalanceStatus';
import { OrderSourceStatus } from '../list/types/OrderSourceStatus';
import { OrderStatus, orderStatusOptions } from '../list/types/OrderStatus';
import { PayStatus, payStatusOptions } from '../list/types/PayStatus';
import { payTypeStatusOptions } from '../list/types/PayTypeStatus';
import OperationDetail from '../operationDetail';
import SupplierDetailDrawer from '../supplier/components/SupplierDetailDrawer';
import AuditModal, { AuditModalProps } from './components/AuditModal';
import RoList, { RoListProps } from './components/RoListModal';
import WareHouseModal, { WareHouseModalProps } from './components/WareHouseModal';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  approvePurchasePost,
  changePurchaseWarehousePost,
  closePurchasePost,
  queryEtcSubmitAfterPagePost,
  queryLogisticsInfoPost,
  queryPurchaseLinePagePost,
  queryPurchaseOrderDetailPost,
  rejectPurchasePost,
  resetPurchasePost,
  submitAfterSalesPost,
} from './services';
import { AuditStatus } from './types/AuditStatus';
import { AuditPostRequest } from './types/audit.post.request';
import { LinePostEntity } from './types/line.post.entity';
import { LogisticsInfoEntity } from './types/logistics.info.entity';
import { PurchasePostEntity } from './types/purchase.post.entity';

export default () => {
  const intl = useIntl();
  const actionRef = useRef<ProDescriptionsActionType>();

  let [searchParams, setSearchParams] = useSearchParams();
  let [recordData, setRecordData] = useState<PurchasePostEntity>({});
  let [logisticsList, setLogisticsList] = useState<LogisticsInfoEntity[]>([]);

  const [expanded, setExpanded] = useState(false);

  const [showPrint, setShowPrint] = useState(false);
  const [printItems, setPrintItems] = useState<PrintItems[]>([]);

  const [supplierDetailDrawer, setSupplierDetailDrawer] = useState<any>({
    open: false,
    supplierId: undefined,
  });

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const hideModal = () => {
    setRoListModalProps({ visible: false, recordRolist: [], onClose: () => { } });
  };

  const hideWareModal = () => {
    setWareHouseModalProps({ visible: false, storeId: '', onClose: () => { } });
  };
  const hideAuditModal = () => {
    setAuditModalProps({ visible: false, onClose: () => { } });
  };

  const [roListModalProps, setRoListModalProps] = useState<RoListProps>({
    visible: false,
    recordRolist: [],
    onClose: hideModal,
  });
  const [wareHouseModalProps, setWareHouseModalProps] = useState<WareHouseModalProps>({
    visible: false,
    storeId: '',
    onClose: hideWareModal,
  });

  const [auditModalProps, setAuditModalProps] = useState<AuditModalProps>({
    visible: false,
    onClose: hideAuditModal,
  });

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  /**
   * 发起售后
   * @param params
   */
  const hanldSubmitAfterSalesPost = async (params: {
    etcSkuId?: string;
    subOrderNo?: string;
    etcNo?: string;
  }) => {
    const data = await submitAfterSalesPost({
      etcSkuId: params.etcSkuId,
      subOrderNo: params.subOrderNo,
    });
    if (data) {
      //发起售后成功跳到一体系售后页面
      const data = await queryEtcSubmitAfterPagePost({
        etcNo: params.etcNo,
        etcSkuId: params.etcSkuId,
      });
      if (data) {
        window.open(data);
      }
    }
  };
  /**
   * 作废
   * @param orderNo
   */
  const hanldCancelReturnOrder = async (orderNo: string) => {
    const data = await closePurchasePost({ orderNo });
    if (data) {
      hideConfirmModal();
      actionRef.current?.reload(true);
    }
  };
  /**
   * 撤回
   * @param orderNo
   */
  const hanldResetPurchase = async (orderNo: string) => {
    const data = await resetPurchasePost({ orderNo });
    if (data) {
      hideConfirmModal();
      actionRef.current?.reload(true);
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const hideConfirmModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };
  // 编辑
  const hanldUpdatePurchase = () => {
    if (recordData?.orderSource == OrderSourceStatus.ETC_PURCHASE) {
      //平台采购
      history.push('/purchase/platform?orderId=' + recordData.id);
    } else if ([OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource)) {
      //外部采购
      history.push('/purchase/external?purchaseOrderId=' + recordData.id);
    }
  };
  /**
   * 一键入库
   */
  const hanldInStockPurchase = async () => {
    const data = await inStockExternalPurchasePost({ orderNo: recordData.orderNo });
    if (data) {
      hideConfirmModal();
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
  };

  const [paymentVisible, setPaymentVisible] = useState(false);
  /**
   * 确认结算
   */
  const hanldConfirmPay = async (values: any) => {
    const data = await confirmPayExternalPurchasePost({ orderNo: recordData.orderNo, ...values });
    if (data) {
      setPaymentVisible(false);
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
  };

  const openWarehouse = async () => {
    setWareHouseModalProps({
      visible: true,
      storeId: recordData.storeId,
      wareHouseId: recordData.receiveWarehouseId,
      onClose: () => hideWareModal,
    });
  };

  const handlUpdateWareHouse = async (values: any) => {
    const data = await changePurchaseWarehousePost({
      orderNo: recordData.orderNo,
      receiveWarehouseId: values.warehouseId,
    });
    if (data) {
      hideWareModal();
      actionRef.current?.reload(true);
    }
    return true;
  };

  const handleAudit = async (values: AuditPostRequest) => {
    if (AuditStatus.APPROVE == values.result) {
      //审核通过
      const data = await approvePurchasePost({ ...values, orderNo: recordData.orderNo });
      if (data) {
        hideAuditModal();
        actionRef.current?.reload(true);
      }
    } else if (AuditStatus.REJECT == values.result) {
      //拒绝
      const data = await rejectPurchasePost({ ...values, orderNo: recordData.orderNo });
      if (data) {
        hideAuditModal();
        actionRef.current?.reload(true);
      }
    }
  };

  /**
   * 关闭对话框
   */

  const columns = [
    ...PostListTableColumns({ recordData, intl }),
    {
      title: intl.formatMessage({ id: 'purchase.detail.columns.operation' }),
      width: 140,
      fixed: 'right',
      hideInTable: !(recordData?.orderSource == OrderSourceStatus.ETC_PURCHASE),
      render: (text, record, _, action) => [
        <Space>
          {(recordData?.orderStatus == OrderStatus.TO_ARRIVAL ||
            recordData?.orderStatus == OrderStatus.RECEIVED ||
            recordData?.orderStatus == OrderStatus.COMPLETE) && (
              <a
                key="start"
                onClick={() => {
                  hanldSubmitAfterSalesPost({
                    etcSkuId: record?.etcSkuId,
                    subOrderNo: recordData?.sourceNo,
                    etcNo: record?.etcNo,
                  });
                }}
              >
                {intl.formatMessage({ id: 'purchase.detail.button.afterSales' })}
              </a>
            )}
          {(record.afterRecordNum ?? 0) > 0 && (
            <a
              key="detail"
              onClick={() => {
                setRoListModalProps({
                  visible: true,
                  recordRolist: record.recordRoList,
                  onClose: hideModal,
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.detail.button.afterSalesRecord' })}
            </a>
          )}
        </Space>,
      ],
    },
  ] as ProColumns<LinePostEntity>[];

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Flex vertical>
              <Space>
                <span>{recordData?.orderNo}</span>
                <Tag color="orange">
                  {orderStatusOptions[recordData?.orderStatus!]?.text}
                </Tag>
                {![OrderStatus.DRAFT, OrderStatus.CLOSE].includes(recordData?.orderStatus!) && (
                  <>
                    <Tag color="green">
                      {payStatusOptions[recordData?.payStatus!]?.text}
                    </Tag>
                    <Tag color="green">
                      {balanceStatusOptions[recordData?.balanceStatus!]?.text}
                    </Tag>
                  </>
                )}
              </Space>
            </Flex>
          }
          extra={
            <Space>
              {[OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource) &&
                (OrderStatus.DRAFT == recordData?.orderStatus ||
                  OrderStatus.REJECT == recordData?.orderStatus) && (
                  <AuthButton authority="editPurchase" type="primary" ghost onClick={hanldUpdatePurchase}>
                    {intl.formatMessage({ id: 'purchase.detail.button.edit' })}
                  </AuthButton>
                )}

              {[OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource) &&
                [
                  OrderStatus.DRAFT,
                  OrderStatus.AUDITING,
                  OrderStatus.CONFIRM,
                  OrderStatus.TO_ARRIVAL,
                  OrderStatus.REJECT,
                ].includes(recordData.orderStatus!) && (
                  <AuthButton
                    authority="deletePurchase"
                    type="primary" ghost
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: intl.formatMessage({ id: 'purchase.detail.confirm.void' }),
                        onOk: () => hanldCancelReturnOrder(recordData.orderNo!),
                      })
                    }
                  >
                    {intl.formatMessage({ id: 'purchase.detail.button.void' })}
                  </AuthButton>
                )}
              {[OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource) &&
                OrderStatus.TO_ARRIVAL == recordData?.orderStatus && (
                  <AuthButton
                    authority="withdrawPurchase"
                    type="primary" ghost
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: intl.formatMessage({ id: 'purchase.detail.confirm.withdraw' }),
                        onOk: () => hanldResetPurchase(recordData.orderNo!),
                      })
                    }
                  >
                    {intl.formatMessage({ id: 'purchase.detail.button.withdraw' })}
                  </AuthButton>
                )}

              <AuthButton
                authority="purchasePrintGoodTag"
                type="primary" ghost
                onClick={() => {
                  setShowPrint(true);
                }}
              >
                {intl.formatMessage({ id: 'purchase.detail.button.printLabel' })}
              </AuthButton>

              {[OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource) &&
                PayStatus.UN_BALANCE == recordData?.payStatus &&
                (OrderStatus.RECEIVED == recordData?.orderStatus ||
                  OrderStatus.TO_ARRIVAL == recordData?.orderStatus) && (
                  <AuthButton
                    authority="purchaseSettlement"
                    type="primary"
                    onClick={() => setPaymentVisible(true)}
                  >
                    {intl.formatMessage({ id: 'purchase.detail.button.confirmSettlement' })}
                  </AuthButton>
                )}
              {((OrderStatus.TO_ARRIVAL == recordData?.orderStatus &&
                [OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(recordData?.orderSource)) ||
                (OrderStatus.TO_ARRIVAL == recordData?.orderStatus &&
                  OrderSourceStatus.ETC_PURCHASE == recordData?.orderSource &&
                  PayStatus.PART_BALANCE == recordData?.payStatus)) && (
                  <AuthButton
                    authority="purchaseInWareHouse"
                    type="primary"
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: intl.formatMessage({ id: 'purchase.detail.confirm.oneClickInStock' }),
                        onOk: hanldInStockPurchase,
                      })
                    }
                  >
                    {intl.formatMessage({ id: 'purchase.detail.button.oneClickInStock' })}
                  </AuthButton>
                )}
              {OrderStatus.AUDITING == recordData?.orderStatus && (
                <AuthButton
                  authority="purchaseAudit"
                  type="primary"
                  onClick={() => {
                    setAuditModalProps({
                      visible: true,
                    });
                  }}
                >
                  {intl.formatMessage({ id: 'purchase.detail.button.audit' })}
                </AuthButton>
              )}
            </Space>
          }
          params={{ id: searchParams.get('purchaseId') ?? '' }}
          request={async (param) => {
            if (!isEmpty(param?.id)) {
              const data = await queryPurchaseOrderDetailPost({ ...param });
              setRecordData(data);
              if (
                data?.orderSource == OrderSourceStatus.ETC_PURCHASE &&
                data?.sourceNo &&
                data?.deliveryNo
              ) {
                const logisticsData = await queryLogisticsInfoPost({
                  deliveryNo: data?.deliveryNo,
                  subOrderNo: data?.sourceNo,
                });
                if (logisticsData) {
                  setLogisticsList(logisticsData);
                } else {
                  setLogisticsList([]);
                }
              } else {
                setLogisticsList([]);
              }
              return { data, success: true };
            }
            return [];
          }}
          column={4}
        >
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'purchase.detail.label.supplier' })}
            dataIndex="supplierName"
            render={(text, record) => {
              return (
                <div className='flex justify-between cursor-pointer' onClick={() => setSupplierDetailDrawer({ open: true, supplierId: record?.supplierId })}>
                  <span className='mr-5'>{record?.supplierName}</span>
                  <RightOutlined style={{
                    color: '#0D0D0D',
                  }} />
                </div>
              );
            }}
          />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'purchase.detail.label.orderStatus' })}
            dataIndex={'orderStatus'}
            valueEnum={orderStatusOptions}
          />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.purchaseAmount' })} dataIndex={'sumAmount'} />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'purchase.detail.label.payType' })}
            dataIndex={'payType'}
            valueEnum={payTypeStatusOptions}
          />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.purchaseStore' })} dataIndex={'storeName'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.receiveWarehouse' })}>
            <Space>
              {recordData?.receiveWarehouseName}
              {OrderStatus.TO_ARRIVAL == recordData?.orderStatus && (
                <a onClick={openWarehouse}>{intl.formatMessage({ id: 'purchase.detail.button.modify' })}</a>
              )}
            </Space>
          </ProDescriptions.Item>
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.orderTime' })} dataIndex={'orderTime'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.external.label.deliveryTime' })} dataIndex={'deliveryTime'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.creator' })} dataIndex={'purchaseUser'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'common.field.currency' })} dataIndex={'currency'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'common.field.rate' })} dataIndex={'exchangeRate'} />
          <ProDescriptions.Item label="GST Excluded" dataIndex={'gstExcluded'} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.detail.label.purchaseRemark' })} dataIndex={'remark'} span={4} />
          {recordData?.auditInfo && (
            <ProDescriptions.Item span={4} className="bg-[#FDF5E8] pt-4 pl-4 text-[14px]">
              <Space>
                <span>{intl.formatMessage({ id: 'purchase.detail.label.auditor' })}：{recordData.auditInfo?.operator}</span>
                <span>{intl.formatMessage({ id: 'purchase.detail.label.auditTime' })}：{recordData.auditInfo?.operationTime}</span>
                <span>{intl.formatMessage({ id: 'purchase.detail.label.remark' })}：{recordData.auditInfo?.remark}</span>
              </Space>
            </ProDescriptions.Item>
          )}
        </ProDescriptions>
      </ProCard>
      <ProCard
        className="mt-4"
        bodyStyle={{ padding: 0 }}
        actions={
          <div className="flex p-6 justify-between ">
            <div className="flex text-[16px] text-black/[0.85] font-semibold items-center ">
              <div>
                {intl.formatMessage({ id: 'purchase.detail.summary.totalQuantity' })}:<span>{recordData?.sumQuantity}</span>
              </div>
              <div className="flex items-center pl-[40px]">
                {intl.formatMessage({ id: 'purchase.detail.summary.totalAmount' })}: <MoneyText text={recordData?.sumAmount} />
              </div>
              <div className="flex items-center  pl-[40px]">
                {intl.formatMessage({ id: 'purchase.detail.summary.freight' })}:
                <MoneyText text={recordData?.freightAmount} />
              </div>
              <div className="flex items-center pl-[40px]">
                GST:
                <MoneyText text={recordData?.totalTaxationAmount} />
              </div>
            </div>
            <div className="text-[16px] text-black/[0.85] font-semibold ">
              {intl.formatMessage({ id: 'purchase.detail.summary.totalPurchaseAmount' })}:
              <span className="text-[24px] font-medium text-primary">
                <MoneyText
                  text={add(recordData?.sumAmount ?? 0, recordData?.freightAmount ?? 0)}
                />
              </span>
            </div>
          </div>
        }
      >
        <FunProTable<LinePostEntity, any>
          headerTitle={<SubTitle text={intl.formatMessage({ id: 'purchase.detail.section.goodsDetail' })} />}
          columns={columns}
          rowKey="id"
          params={{ orderNo: searchParams.get('purchaseOrderNo') ?? '' }}
          requestPage={async (params) => {
            if (!isEmpty(params?.orderNo)) {
              const data = await queryPurchaseLinePagePost(params);
              setPrintItems(
                data?.data?.map((item) => ({ itemSn: item.itemSn!, number: item.num! })) ?? [],
              );
              return data;
            }
            return { data: [], total: 0 };
          }}
          search={false}
        />
      </ProCard>
      <CapitalFlow capitalData={recordData?.paySubTypeList} />
      <OperationDetail logList={recordData.logList} />
      <RoList {...roListModalProps} />
      <ConfirmModal
        {...confirmModalProps}
        onCancel={() => {
          setConfirmModalProps((preProps) => ({
            ...preProps,
            open: false,
          }));
        }}
      />
      <WareHouseModal
        {...wareHouseModalProps}
        onOk={handlUpdateWareHouse}
        onClose={hideWareModal}
      />
      <AuditModal {...auditModalProps} onOk={handleAudit} onClose={hideAuditModal} />
      <PaymentExternalFormModal
        totalAmount={add(recordData?.sumAmount ?? 0, recordData?.freightAmount ?? 0)}
        visible={paymentVisible}
        storeId={recordData.storeId}
        onClose={() => setPaymentVisible(false)}
        onSubmit={hanldConfirmPay}
        dataSource={{
          payType: recordData?.payType,
          paySubTypeList: recordData?.paySubTypeList,
        }}
      />
      <GoodsPrintDrawer
        visible={showPrint}
        onClose={() => setShowPrint(false)}
        items={printItems}
        bizType={'purchase'}
      />
      <SupplierDetailDrawer {...supplierDetailDrawer} onClose={() => setSupplierDetailDrawer({ open: false })} />

    </PageContainer>
  );
};
