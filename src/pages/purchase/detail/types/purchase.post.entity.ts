
export interface PurchasePostEntity {
  /**
   * 审核信息
   */
  auditInfo?: AuditInfo;
  /**
   * 付款状态0-未结款，1-部分结款，2-全部结款
   */
  balanceStatus?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 发货单号
   */
  deliveryNo?: string;
  /**
   * 预计到货时间
   */
  deliveryTime?: string;
  /**
   * 汇率
   */
  exchangeRate?: number;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 客户不收税
   */
  gstExcluded?: number;
  /**
   * 订单id
   */
  id?: string;
  /**
   * 操作日志
   */
  logList?: LogList[];
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商name
   */
  memberName?: string;
  /**
   * 是否需要审核
   */
  needApprove?: boolean;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 数据来源
   */
  orderSource?: string;
  /**
   * 采购状态
   */
  orderStatus?: string;
  /**
   * 采购时间
   */
  orderTime?: string;
  /**
   * 付款状态
   */
  payStatus?: string;
  /**
   * 子支付方式
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 付款方式
   */
  payType?: string;
  /**
   * 采购人
   */
  purchaseUser?: string;
  /**
   * 采购人id
   */
  purchaseUserId?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseId?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 实付总金额（sum_amount+freight_amount+total_taxation_amount）
   */
  shouldTotalAmount?: number;
  /**
   * 订单来源-子订单号
   */
  sourceNo?: string;
  /**
   * 发起门店
   */
  storeId?: string;
  /**
   * 发起门店
   */
  storeName?: string;
  /**
   * 采购总金额
   */
  sumAmount?: number;
  /**
   * 采购数量
   */
  sumQuantity?: number;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 税费总额
   */
  totalTaxationAmount?: number;
}

/**
 * 审核信息
 */
export interface AuditInfo {
  /**
   * 操作时间
   */
  operationTime?: string;
  /**
   * 操作类型
   */
  operationType?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 备注
   */
  remark?: string;
}

export interface LogList {
  /**
   * 模块
   */
  module?: string;
  /**
   * 操作人名称
   */
  operatePerson?: string;
  /**
   * 操作业务单号
   */
  operationNo?: string;
  /**
   * 操作时间
   */
  operationTime?: string;
  /**
   * 操作类型
   */
  operationType?: string;
  /**
   * 操作类型描述
   */
  operationTypeDesc?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 备注
   */
  remark?: string;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: number;
  /**
   * 确认结算时间
   */
  confirmTime?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}
