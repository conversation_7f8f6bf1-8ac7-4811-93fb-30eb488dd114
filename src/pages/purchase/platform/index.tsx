import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import AuthButton from '@/components/common/AuthButton';
import MoneyText from '@/components/common/MoneyText';
import SubTitle from '@/components/common/SubTitle';
import {
  addPurchaseCartBatch,
  deletePurchaseCart,
  queryEtcCartPage,
  queryPurchaseCartList,
  submitPurchaseCart,
  updatePurchaseCart,
} from '@/pages/purchase/platform/services';
import type { PurchaseCartEntity } from '@/pages/purchase/platform/types/purchase.cart.entity';
import type { SkuInfoList } from '@/pages/purchase/platform/types/submit.purchase.cart.request';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { importData } from '@/utils/importData';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import {
  EditableProTable,
  PageContainer,
  ProCard,
  ProForm,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useDebounceFn } from 'ahooks';
import { App, ConfigProvider, Space, message, Result } from 'antd';
import _ from 'lodash';
import { useRef, useState } from 'react';
import { YesNoStatus } from '../supplier/operation/types/YesNo';
import { purchaseCartTableColumns } from './config/purchaseCartTableColumns';
import { useModel } from '@@/exports';

const PurchaseList = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { modal } = App.useApp();
  const [dataSourceCache, setDataSourceCache] = useState<PurchaseCartEntity[]>([]);

  const updateFn = useDebounceFn(
    (id, num) => {
      handleUpdate(id, num);
    },
    { wait: 500 },
  );

  const [storeId, setStoreId] = useState<string>();
  const [warehouseId, setWarehouseId] = useState<string>();

  /**
   * 选择key
   */
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  if (!initialState?.currentUser?.etcMemberId || !initialState?.currentUser?.etcAccountId) {
    return <Result title="请先绑定一体系账号" status={403} className="mt-[100px]" />;
  }

  /**
   * 新增购物车
   */
  const handleAdd = (itemList: any[]) => {
    // const item = itemList[0];
    if (dataSourceCache.length >= 200) {
      message.warning('加购商品数量不能超过200');
      return;
    }
    const lineList = itemList.map((item) => {
      return {
        etcNo: item.etcNo,
        num: item.number,
        skuName: item.skuName,
      };
    });

    addPurchaseCartBatch({ lineList }).then((result) => {
      if (result) {
        message.success('添加成功');
        actionRef.current?.reload(true);
      }
    });
  };

  /**
   * 更新购物车
   */
  const handleUpdate = (id: string, num: number) => {
    updatePurchaseCart({ id, num }).then((result) => {
      if (result) {
        message.success('编辑成功');
        actionRef.current?.reload(true);
      }
    });
  };

  /**
   * 删除购物车
   */
  const handleDelete = (ids: string[]) => {
    deletePurchaseCart({ ids }).then((result) => {
      if (result) {
        message.success('删除成功');
        actionRef.current?.reload(true);
        setSelectedRowKeys((items) => items.filter((item) => !ids.includes(item)));
      }
    });
  };

  /**
   * 提交购物车
   */
  const handleSubmit = () => {
    const skuInfoList: SkuInfoList[] = [];
    dataSourceCache.forEach((m) => {
      if (selectedRowKeys.includes(m.id)) {
        skuInfoList.push({
          etcNo: m.etcNo,
          num: m.num,
        });
      }
    });
    submitPurchaseCart({ skuInfoList }).then((result) => {
      switch (result.code) {
        case 0:
          message.success('提交成功');
          actionRef.current?.reload(true);
          setSelectedRowKeys([]);
          break;
      }
    });
  };

  /**
   * 计算总价
   */
  const getTotalPrice = () => {
    let price = 0;
    dataSourceCache.forEach((m) => {
      if (selectedRowKeys.includes(m.id)) {
        price = _.round(_.add(price, _.multiply(m.num!, m.price!)), 2);
      }
    });
    return price;
  };

  console.log('warehouseId', warehouseId);

  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingTop: 0 }}>
        <ProForm submitter={false} layout="inline" className="px-6 pt-6" formRef={formRef}>
          <ProFormSelect
            labelAlign="left"
            width={300}
            label="采购门店"
            name="storeId"
            onChange={(v) => {
              formRef.current?.setFieldsValue({ warehouseId: undefined });
              setWarehouseId(undefined);
              setStoreId(v as string);
            }}
            request={async () => {
              const data = await queryStoreByAccount({ status: 1 });
              if (data[0] && data[0].type === '0') {
                formRef.current?.setFieldValue('storeId', data[0].id);
                setStoreId(data[0].id);
              }
              return data?.map(({ id, name }) => ({
                key: id,
                value: id,
                label: name,
              }));
            }}
          />
          <ProFormSelect
            width={300}
            label="收货仓库"
            name="warehouseId"
            dependencies={['storeId']}
            fieldProps={{
              fieldNames: { label: 'warehouseName', value: 'id' },
            }}
            onChange={(v) => {
              setWarehouseId(v as string);
            }}
            request={(query) => {
              if (query.storeId) {
                return warehouseList({ storeIdList: [query.storeId], state: YesNoStatus.YES }).then(
                  (result) => {
                    if (result?.warehouseSimpleRoList?.length == 0) {
                      //无仓库信息
                      formRef.current?.setFieldValue('warehouseId', undefined);
                      setWarehouseId(undefined);
                    } else {
                      formRef.current?.setFieldValue(
                        'warehouseId',
                        result.warehouseSimpleRoList?.[0].id,
                      );
                      setWarehouseId(result.warehouseSimpleRoList?.[0].id);
                    }
                    return result.warehouseSimpleRoList ?? [];
                  },
                );
              } else {
                return new Promise((resolve, reject) => {
                  return reject([]);
                });
              }
            }}
          />
        </ProForm>
      </ProCard>
      <ProCard bodyStyle={{ paddingTop: 8 }} className="mt-[16px]">
        <GoodsSearch
          bizType={GoodsSearchBizType.PlatformPurchase}
          onAdd={handleAdd}
          addedItemSns={dataSourceCache.map((item) => item.etcNo)}
          storeId={storeId}
          warehouseId={warehouseId}
        />
      </ProCard>
      <ProCard
        title={<SubTitle text="采购单明细" />}
        className="mt-4"
        bodyStyle={{ padding: 0 }}
        actions={
          <div className="flex p-6 justify-between ">
            <div className="flex text-[16px] text-black/[0.85] font-semibold items-end">
              <div>
                总购买数：<span>{selectedRowKeys.length}</span>
              </div>
              <div className="flex items-end pl-[58px]">
                总采购金额：
                <span className="text-[24px] text-[#F83431] leading-[1.3]">
                  <MoneyText text={getTotalPrice()} />
                </span>
              </div>
              <span className="text-[14px] ml-[20px] text-gray-400">
                *实际金额以一体系商城结算为准
              </span>
            </div>
            <div>
              <Space>
                <AuthButton
                  disabled={dataSourceCache.length === 0}
                  danger
                  key="submit"
                  type="primary"
                  authority="submitPlatformPurchase"
                  onClick={handleSubmit}
                >
                  立即采购
                </AuthButton>
              </Space>
            </div>
          </div>
        }
      >
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 80,
              },
            },
          }}
        >
          <EditableProTable<PurchaseCartEntity, any>
            rowKey="id"
            search={false}
            actionRef={actionRef}
            scroll={{ x: 1300 }}
            tableAlertOptionRender={false}
            tableAlertRender={false}
            recordCreatorProps={false}
            params={{ storeId, warehouseId }}
            request={async (query) => {
              const result = await queryPurchaseCartList(query);
              if (result) {
                setDataSourceCache(result);
                return {
                  data: result,
                  success: true,
                };
              } else {
                return {
                  data: [],
                  success: false,
                };
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              preserveSelectedRowKeys: true,
              onChange: (selectedKeys) => {
                setSelectedRowKeys(selectedKeys as string[]);
              },
            }}
            columns={purchaseCartTableColumns({ handleDelete, handleUpdate: updateFn.run })}
            editable={{
              type: 'multiple',
              editableKeys: dataSourceCache.map((item) => item.id),
              actionRender: () => [],
            }}
            headerTitle={
              <Space>
                <AuthButton
                  danger
                  key="delete"
                  authority="purchasePlatformDelete"
                  disabled={selectedRowKeys.length <= 0}
                  onClick={() => handleDelete(selectedRowKeys)}
                >
                  删除
                </AuthButton>
                <AuthButton
                  danger
                  key="import"
                  authority="purchasePlatformImport"
                  onClick={() => {
                    importData({
                      moduleId: 'BATCH_IMPORT_PURCHASE_CART_LINE',
                      systemId: 'ETC_SAAS_SYS',
                      taskDesc: '批量导入一体系采购购物车',
                      onSuccess: () => {
                        actionRef.current?.reload(true);
                      },
                      downloadFileName:
                        'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E8%B4%AD%E7%89%A9%E8%BD%A6%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
                    });
                  }}
                >
                  批量导入
                </AuthButton>
              </Space>
            }
          />
        </ConfigProvider>
      </ProCard>
    </PageContainer>
  );
};

export default PurchaseList;
