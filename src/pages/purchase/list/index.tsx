import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProFormInstance } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { Access, history, useAccess, useIntl } from '@umijs/max';
import { App, Button, Space, message } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { closePurchasePost, resetPurchasePost } from '../detail/services';
import PurchseImportModal from './components/PurchseImportModal';
import { PostListTableColumns } from './config/postListTableColumns';
import { copyOrderPost, queryPostList } from './services';
import { PurchasePostEntity } from './types/purchase.post.entity';

const PurchaseList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const { modal } = App.useApp();
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();

  useActivate(() => {
    actionRef.current?.reload();
  });

  const [importModalProps, setImportModalProps] = useState({
    visible: false,
  });

  const handleAgainItem = async (orderNo: string) => {
    const result = await copyOrderPost({ orderNo });
    if (result) {
      switch (result.code) {
        case 0:
          message.success(intl.formatMessage({ id: 'purchase.list.message.submitSuccess' }));
          actionRef.current?.reload(true);
          break;
      }
    }
    return true;
  };
  const handleUpdateItem = async (orderId: string) => {
    history.push(`/purchase/external?purchaseOrderId=${orderId}`);
  };
  /**
   * 作废
   * @param orderNo
   */
  const handleClosePurchase = async (orderNo: string) => {
    const data = await closePurchasePost({ orderNo });
    actionRef.current?.reload(true);
    return true;
  };
  /**
   * 撤回
   * @param orderNo
   */
  const handleWithdraw = async (orderNo: string) => {
    const data = await resetPurchasePost({ orderNo });
    actionRef.current?.reload(true);
    return true;
  };

  return (
    <PageContainer>
      <FunProTable<PurchasePostEntity, any>
        rowKey="id"
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        formRef={formRef}
        headerTitle={
          <Space>
            <Access accessible={access.hasButtonPerms('addPurchase')}>
              <Button type="primary" key="primary" onClick={() => history.push('/purchase/external')}>
                {intl.formatMessage({ id: 'purchase.list.button.addPurchase' })}
              </Button>
            </Access>
            <AuthButton
              key="export"
              type="primary"
              ghost
              authority="exportPurchase"
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.list.export.taskDesc' }),
                  moduleId: 'PURCHASE_ORDER_EXPORT',
                  params: formRef.current?.getFieldsValue(),
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.list.button.export' })}
            </AuthButton>
            <AuthButton
              key="import"
              type="primary"
              ghost authority="importPurchase"
              onClick={() => {
                setImportModalProps({ visible: true });
              }}
            >
              {intl.formatMessage({ id: 'purchase.list.button.import' })}
            </AuthButton>
          </Space>
        }
        requestPage={queryPostList}
        columns={PostListTableColumns({
          handleAgainItem,
          handleUpdateItem,
          handleClosePurchase,
          handleWithdraw,
          formRef,
          intl,
        })}
      />
      <PurchseImportModal {...importModalProps} onCancel={() => setImportModalProps({ visible: false })} />
    </PageContainer>
  );
};
export default withKeepAlive(PurchaseList);
