import { OrderSourceStatus } from "./OrderSourceStatus";

export interface PurchasePostEntity {
  /**
   * 付款状态0-未结款，1-部分结款，2-全部结款
   */
  balanceStatus?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 发货单号
   */
  deliveryNo?: string;
  /**
   * 汇率
   */
  exchangeRate?: number;
  /**
   * 客户不收税
   */
  gstExcluded?: number;
  /**
   * 订单id
   */
  id?: string;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商name
   */
  memberName?: string;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 数据来源
   */
  orderSource?: string;
  /**
   * 采购状态
   */
  orderStatus?: OrderSourceStatus;
  /**
   * 下单时间
   */
  orderTime?: string;
  /**
   * 付款状态
   */
  payStatus?: string;
  /**
   * 付款方式
   */
  payType?: string;
  /**
   * 采购人
   */
  purchaseUser?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseId?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 实付总金额（sum_amount+freight_amount+total_taxation_amount）
   */
  shouldTotalAmount?: number;
  /**
   * 来源单号
   */
  sourceNo?: string;
  /**
   * 发起门店
   */
  storeId?: string;
  /**
   * 发起门店
   */
  storeName?: string;
  /**
   * 采购总金额
   */
  sumAmount?: number;
  /**
   * 采购数量
   */
  sumQuantity?: number;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 税费总额
   */
  totalTaxationAmount?: number;
}
