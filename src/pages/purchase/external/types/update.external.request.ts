export interface UpdateExternalRequest {
  /**
   * 预计到货时间
   */
  deliveryTime?: string;
  /**
   * 一体系账户id
   */
  etcAccountId?: string;
  /**
   * 一体系零售商id
   */
  etcMemberId?: string;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 采购单id
   */
  id?: string;
  /**
   * 门店系统零售商id
   */
  memberId?: string;
  /**
   * 具体结算方式，现款时不允许为空
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 结算方式0挂账1现款
   */
  payType?: string;
  /**
   * 收货仓库id
   */
  receiveWarehouseId?: string;
  /**
   * 收货仓库Name
   */
  receiveWarehouseName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 下单门店id
   */
  storeId?: string;
  /**
   * 下单门店
   */
  storeName?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}
