/**
 * 用于区分是澳洲本土供应商还是海外供应商
 * 目前主要是AU/CN
 */
export enum Country {
  /**
   * 澳洲本土供应商
   */
  AU = 'AU',
  /**
   * 海外供应商
   */
  CN = 'CN',
}

export interface AddPostEntity {
  /**
   * 门店系统账户id
   */
  accountId?: string;
  /**
   * 门店系统账户名称
   */
  accountName?: string;
  /**
   * 门店系统手机号
   */
  accountPhone?: string;
  /**
   * 供应商地址信息
   */
  supplierAddressList?: SupplierAddressList[];
  /**
   * 供应商联系人信息
   */
  supplierConcatList?: SupplierConcatList[];
  /**
   * 供应商基础信息
   */
  supplierInfo?: SupplierInfo;
  /**
   * 供应商结算信息
   */
  supplierSettleAccountList?: SupplierSettleAccountList[];
  supplierSettleInfo?: SupplierSettleInfo;
}

export interface SupplierAddressList {
  /**
   * 区
   */
  area?: string;
  /**
   * 省市区code列表，兼容前端传值方式
   */
  areaCodeList?: string[];
  /**
   * 市
   */
  city?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * 联系人
   */
  firstName?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认;0-默认地址1-非默认地址
   */
  isDefault?: string;
  /**
   * 联系人
   */
  lastName?: string;
  /**
   * 省
   */
  province?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
}

export interface SupplierConcatList {
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 联系人
   */
  firstName?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认联系人
   */
  isDefault?: string;
  /**
   * 联系人
   */
  lastName?: string;
  /**
   * 职务
   */
  post?: string;
  /**
   * QQ
   */
  qq?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别;0-男1-女
   */
  sex?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 微信
   */
  wx?: string;
}

/**
 * 供应商基础信息
 */
export interface SupplierInfo {
  /**
   * ABN
   */
  abn?: string;
  /**
   * GRIPX在供应商处的代码
   */
  clientCode?: string;
  /**
   * 澳洲本土供应商、海外供应商
   */
  country?: Country;
  /**
   * 财务邮箱
   */
  financeEmail?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 座机电话
   */
  phone?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 供应商简称
   */
  shortName?: string;
  /**
   * 供应商code
   */
  supplierCode?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 通用邮箱
   */
  universalEmail?: string;
}

export interface SupplierSettleAccountList {
  /**
   * AccountName
   */
  accountName?: string;
  /**
   * AccountNumber
   */
  accountNumber?: string;
  /**
   * BSB
   */
  bsb?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * SwiftCode
   */
  swiftCode?: string;
}

export interface SupplierSettleInfo {
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: number;
  /**
   * id
   */
  id?: string;
  /**
   * 0=非多币种1=多币种
   */
  isMultiCurrency?: number;
  /**
   * 结算类型
   */
  settleType?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商应付
   */
  currencyPayableAmount?: string;
}
