import type { ProColumns } from '@ant-design/pro-components';
import type { SupplierSettleAccountList } from '../types/add.post.entity';

export const SettleAccountListTableColumns = (intl: any) =>
  [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      dataIndex: 'index',
      readonly: true,
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'purchase.supplier.operation.settlement.columns.bsb' }),
      dataIndex: 'bsb',
      key: 'bsb',
    },
    {
      title: intl.formatMessage({ id: 'purchase.supplier.operation.settlement.columns.swiftCode' }),
      dataIndex: 'swiftCode',
      key: 'swiftCode',
    },
    {
      title: intl.formatMessage({ id: 'purchase.supplier.operation.settlement.columns.accountNumber' }),
      dataIndex: 'accountNumber',
      key: 'accountNumber',
    },
    {
      title: intl.formatMessage({ id: 'purchase.supplier.operation.settlement.columns.accountName' }),
      dataIndex: 'accountName',
      key: 'accountName',
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id ?? '');
          }}
        >
          {intl.formatMessage({ id: 'purchase.supplier.operation.contact.button.edit' })}
        </a>,
        <a
          key="delete"
          onClick={() => {
            // Implement delete logic here
          }}
        >
          {intl.formatMessage({ id: 'purchase.supplier.operation.contact.button.delete' })}
        </a>,
      ],
    },
  ] as ProColumns<SupplierSettleAccountList>[];
