import { AddressCode } from '@/pages/customer/list/components/CustomerCreateDrawerForm/AddressInfo';
import { requiredProps } from '@/types/validateRules';
import type { ProColumns } from '@ant-design/pro-components';
import { ProFormRadio } from '@ant-design/pro-components';
import { Flex, Popconfirm, Radio } from 'antd';
import { YesNoStatus } from '../types/YesNo';
import type { SupplierAddressList } from '../types/add.post.entity';

export interface AddressListTableColumnsProps {
  intl: any;
  setIsChecked: (id: string) => void;
  handleDeleteItemAddress: (id: string, isDefault: string) => void;
}

export const AddressListTableColumns = (props: AddressListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      dataIndex: 'index',
      readonly: true,
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.defaultAddress' }),
      dataIndex: 'isDefault',
      key: 'isDefault',
      search: false,
      width: 80,
      render: (_, row, index) => (
        <ProFormRadio
          fieldProps={{
            width: '80',
            checked: row.isDefault == YesNoStatus.NO,
          }}
        />
      ),
      renderFormItem: ({ index, originProps }, config) => (
        <Radio
          checked={config.record?.isDefault == YesNoStatus.NO}
          onChange={() => props.setIsChecked(config?.record?.id ?? '')}
        />
      ),
    },
    {
      dataIndex: 'postCode',
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.postCode' })}</span>
        </Flex>
      ),
      width: 200,
      fieldProps: (form, { rowKey }) => ({
        onChange: () => {
          form.setFieldValue([rowKey, 'areaCodeList'], undefined);
        },
      }),
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.provinceCityDistrict' })}</span>
        </Flex>
      ),
      width: 200,
      dataIndex: 'areaCodeList',
      renderFormItem: (_, { record }) => {
        const { postCode = '' } = record;
        return (
          <AddressCode postCode={postCode} />
        )
      },
      renderText: (text, record) => {
        return <>{record?.province} / {record?.area}</>;
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.detailAddress' }),
      dataIndex: 'detailAddress',
      key: 'detailAddress',
      width: 200,
      search: false,
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.contactName' })}</span>
        </Flex>
      ),
      dataIndex: 'firstName',
      width: 100,
      colSpan: 2
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.contactName' })}</span>
        </Flex>
      ),
      dataIndex: 'lastName',
      width: 100,
      colSpan: 0
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.contactPhone' }),
      dataIndex: 'concatPhone',
      key: 'concatPhone',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            console.log(action);
            action?.startEditable?.(record.id ?? '');
          }}
        >
          {props.intl.formatMessage({ id: 'purchase.supplier.operation.address.button.edit' })}
        </a>,
        <Popconfirm
          key="delete"
          title={props.intl.formatMessage({ id: 'purchase.supplier.operation.address.confirm.delete' })}
          onConfirm={() => props.handleDeleteItemAddress(record.id ?? '', record.isDefault ?? '')}
        >
          <a>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.button.delete' })}</a>
        </Popconfirm>,
      ],
    },
  ] as ProColumns<SupplierAddressList>[];
