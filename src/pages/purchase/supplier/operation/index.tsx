import SubTitle from '@/components/common/SubTitle';
import { SettleType } from '@/pages/customer/list/types/CustomerSaveEntity';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { REG_EMAIL_RULE, REG_ONLY_ALPHA_AND_DIGIT_RULE } from '@/utils/RuleUtils';
import type {
  ProDescriptionsActionType,
  ProFormInstance
} from '@ant-design/pro-components';
import {
  DrawerForm,
  EditableProTable,
  PageContainer,
  ProCard,
  ProForm,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText
} from '@ant-design/pro-components';
import { useAsyncEffect } from 'ahooks';
import { ConfigProvider, Space } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useRef, useState } from 'react';
import { useIntl } from 'umi';
import { queryFullById } from '../services';
import { AddressListTableColumns } from './config/addressListTableColumns';
import { ContactsListTableColumns } from './config/contactsListTableColumns';
import { SettleAccountListTableColumns } from './config/settleAccountListTableColumns';
import { YesNoStatus } from './types/YesNo';
import { Country, type AddPostEntity, type SupplierAddressList, type SupplierConcatList, type SupplierSettleAccountList } from './types/add.post.entity';
export default (props: CommonModelForm<string, any>) => {
  const intl = useIntl();
  const [form] = useForm<any>();
  const formRef = useRef<ProFormInstance<any>>();
  const actionRef = useRef<ProDescriptionsActionType>();
  const [concatEditableKeys, setConcatEditableKeys] = useState<React.Key[]>(() => []);
  const [addressEditableKeys, setAddressEditableKeys] = useState<React.Key[]>(() => []);
  const [settleAccountEditableKeys, setSettleAccountEditableKeys] = useState<React.Key[]>(() => []);

  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
    } else {
      setConcatEditableKeys([]);
      setAddressEditableKeys([]);
      const data = await queryFullById({ id: props.recordId ?? '' });
      let { supplierConcatList = [] } = data;

      supplierConcatList = supplierConcatList?.map((t) => ({
        ...t,
        positions: t?.postList?.map((item) => item.positionCode)
      }));
      data.supplierConcatList = supplierConcatList
      form.setFieldsValue(data);
    }
  }, [props.visible]);
  //删除联系人
  const handleDeleteItemContacts = async (id: string, isDefault: string) => {
    const supplierConcatListDataSource = form.getFieldValue(
      'supplierConcatList',
    ) as SupplierConcatList[];
    let newDataSource;
    newDataSource = supplierConcatListDataSource.filter((item) => item.id !== id);
    if (isDefault == YesNoStatus.NO) {
      if (newDataSource.length > 0) {
        console.log(newDataSource);
        newDataSource = newDataSource.map((s, index) => ({
          ...s,
          isDefault: index == 0 ? YesNoStatus.NO : YesNoStatus.YES,
        }));
      }
    }
    form.setFieldsValue({
      supplierConcatList: newDataSource,
    });
  };
  //删除联系人
  const handleDeleteItemAddress = async (id: string, isDefault: string) => {
    const supplierAddressListDataSource = form.getFieldValue(
      'supplierAddressList',
    ) as SupplierConcatList[];
    let newDataSource;
    newDataSource = supplierAddressListDataSource.filter((item) => item.id !== id);
    if (isDefault == YesNoStatus.NO) {
      //如果删除的是默认地址
      if (newDataSource.length > 0) {
        newDataSource = newDataSource.map((s, index) => ({
          ...s,
          isDefault: index == 0 ? YesNoStatus.NO : YesNoStatus.YES,
        }));
      }
    }
    form.setFieldsValue({
      supplierAddressList: newDataSource,
    });
  };
  //设置地址默认值
  const setIsCheckedAddress = async (id: string) => {
    const supplierAddressListDataSource = form.getFieldValue(
      'supplierAddressList',
    ) as SupplierAddressList[];
    const newDataSource = supplierAddressListDataSource.map((s) => ({
      ...s,
      isDefault: s.id == id ? YesNoStatus.NO : YesNoStatus.YES,
    }));
    form.setFieldsValue({
      supplierAddressList: newDataSource,
    });
  };
  //设置联系人默认
  const setIsChecked = async (id: string) => {
    const supplierConcatListDataSource = form.getFieldValue(
      'supplierConcatList',
    ) as SupplierConcatList[];
    const newDataSource = supplierConcatListDataSource.map((s) => ({
      ...s,
      isDefault: s.id == id ? YesNoStatus.NO : YesNoStatus.YES,
    }));
    form.setFieldsValue({
      supplierConcatList: newDataSource,
    });
  };


  const rules = [{ required: !props.readOnly }];

  return (
    <PageContainer>
      <DrawerForm<AddPostEntity>
        form={form}
        onFinish={props.onOk}
        drawerProps={{
          maskClosable: false,
          onClose: props.onCancel,
          bodyStyle: { padding: 0 },
        }}
        title={props.title}
        open={props.visible}
        width={1200}
        layout='vertical'
        grid
        initialValues={{
          supplierSettleInfo: {
            settleType: SettleType.COD,
          }
        }}
      >
        <Space direction="vertical" className='p-4 bg-gray-100 gap-4'>
          <ProCard className="rounded-lg" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.basic.title' })} />}>
            <ProForm.Group>
              <ProFormText colProps={{ span: 8 }} name={['supplierInfo', 'id']} disabled={props.readOnly} hidden={true} />
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'supplierName']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierName' })}
                rules={rules}
                fieldProps={{ maxLength: 50 }}
              />
              <ProFormText
                colProps={{ span: 8 }}
                rules={[REG_ONLY_ALPHA_AND_DIGIT_RULE]}
                name={['supplierInfo', 'supplierCode']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierCode' })}
                fieldProps={{ maxLength: 50 }}
              />
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'shortName']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.shortName' })}
                fieldProps={{ maxLength: 20 }}
              />
              <ProFormSelect
                colProps={{ span: 8 }}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.country' })}
                name={['supplierInfo', 'country']}
                valueEnum={{
                  [Country.AU]: 'AU',
                  [Country.CN]: 'CN',
                }}
              />
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'abn']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.abn' })}
                rules={rules}
              />
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'clientCode']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.clientCode' })}
              />
              {/* 通用邮箱 */}
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'universalEmail']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.universalEmail' })}
                rules={[REG_EMAIL_RULE]}
              />
              {/* 财务邮箱 */}
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'financeEmail']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.financeEmail' })}
                rules={[REG_EMAIL_RULE]}
              />
              {/* 座机电话 */}
              <ProFormText
                colProps={{ span: 8 }}
                name={['supplierInfo', 'phone']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.telephone' })}
              />

              <ProFormText
                colProps={{ span: 24 }}
                name={['supplierInfo', 'remark']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.remark' })}
                placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.basic.remark.placeholder' })}
                fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
                rules={[{ max: 100 }]}
              />
            </ProForm.Group>
          </ProCard>
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.contact.title' })} />}>
            <ConfigProvider
              theme={{
                components: {
                  InputNumber: {
                    controlWidth: 130,
                  },
                },
              }}
            >
              <EditableProTable<SupplierConcatList>
                rowKey="id"
                search={false}
                columns={ContactsListTableColumns({ intl, setIsChecked, handleDeleteItemContacts })}

                recordCreatorProps={{
                  record: (index, dataSource) => ({
                    id: 'new_' + Math.random() * 1000000 + '',
                    isDefault: dataSource?.length == 0 ? YesNoStatus.NO : YesNoStatus.YES,
                  }),
                  creatorButtonText: intl.formatMessage({ id: 'purchase.supplier.operation.contact.add' }),
                }}
                name="supplierConcatList"
                editable={{
                  type: 'multiple',
                  editableKeys: concatEditableKeys,
                  onChange: setConcatEditableKeys,
                  actionRender: (row, config, defaultDom) => [defaultDom.delete],
                }}
              />
            </ConfigProvider>
          </ProCard>

          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.address.title' })} />}>
            <EditableProTable<SupplierAddressList>
              rowKey="id"
              search={false}
              columns={AddressListTableColumns({
                intl,
                setIsChecked: setIsCheckedAddress,
                handleDeleteItemAddress,
              })}
              name="supplierAddressList"
              recordCreatorProps={{
                record: (index, dataSource) => ({
                  id: 'new_' + Math.random() * 1000000,
                  isDefault: dataSource?.length == 0 ? YesNoStatus.NO : YesNoStatus.YES,
                }),
                creatorButtonText: intl.formatMessage({ id: 'purchase.supplier.operation.address.add' }),
              }}
              editable={{
                type: 'multiple',
                editableKeys: addressEditableKeys,
                onChange: setAddressEditableKeys,
                actionRender: (row, config, defaultDom) => [defaultDom.delete],
              }}
            />
          </ProCard>
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.title' })} />}>
            <ProForm.Group>
              <ProFormText hidden name={['supplierSettleInfo', 'id']} />
              <ProFormRadio.Group
                colProps={{ span: 8 }}
                name={['supplierSettleInfo', 'settleType']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.settleType' })}
                options={[
                  { label: SettleType.COD, value: SettleType.COD },
                  { label: SettleType.MONTHLY, value: SettleType.MONTHLY },
                ]}
              />
              <ProFormSwitch
                colProps={{ span: 8 }}
                name={['supplierSettleInfo', 'isMultiCurrency']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.isMultiCurrency' })}
              />
              <ProFormSwitch
                colProps={{ span: 8 }}
                name={['supplierSettleInfo', 'gstExcluded']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.gstExcluded' })}
              />
            </ProForm.Group>

            <div className='mb-2'>
              {
                intl.formatMessage({ id: 'purchase.supplier.operation.settlement.columns.accountName' })
              }
            </div>
            <EditableProTable<SupplierSettleAccountList>
              rowKey="id"
              search={false}
              columns={SettleAccountListTableColumns(intl)}
              name="supplierSettleAccountList"
              recordCreatorProps={{
                record: () => ({ id: 'new_' + Math.random() * 1000000 }),
                creatorButtonText: intl.formatMessage({ id: 'purchase.supplier.operation.settlement.addAccount' }),
              }}
              editable={{
                type: 'multiple',
                editableKeys: settleAccountEditableKeys,
                onChange: setSettleAccountEditableKeys,
                actionRender: (row, config, defaultDom) => [defaultDom.delete],
              }}
            />
          </ProCard>
        </Space>
      </DrawerForm >
    </PageContainer >
  );
};
