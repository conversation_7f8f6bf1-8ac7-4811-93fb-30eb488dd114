export interface SupplierPostEntity {
  /**
   * 供应商地址信息
   */
  supplierAddressList?: SupplierAddressList[];
  /**
   * 供应商联系人信息
   */
  supplierConcatList?: SupplierConcatList[];
  /**
   * 供应商基础信息
   */
  supplierInfo?: SupplierInfo;
  /**
   * 结算账户信息
   */
  supplierSettleAccountList?: SupplierSettleAccountList[];
  /**
   * 结算信息
   */
  supplierSettleInfo?: SupplierSettleInfo;
}

export interface SupplierAddressList {
  /**
   * 区
   */
  area?: string;
  /**
   * 区code
   */
  areaCode?: string;
  /**
   * 省市区code列表，兼容前端展示方式
   */
  areaCodeList?: string[];
  /**
   * 是否可编辑0-否1-是
   */
  canEdit?: string;
  /**
   * 市
   */
  city?: string;
  /**
   * 市code
   */
  cityCode?: string;
  /**
   * 联系人
   */
  concatPerson?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认;0-默认地址1-非默认地址
   */
  isDefault?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 邮编
   */
  postCode?: string;
  /**
   * 省
   */
  province?: string;
  /**
   * 省code
   */
  provinceCode?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface SupplierConcatList {
  /**
   * 是否可编辑0-否1-是
   */
  canEdit?: string;
  /**
   * 联系人
   */
  concatPerson?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认联系人
   */
  isDefault?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 职务
   */
  post?: string;
  /**
   * QQ
   */
  qq?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别;0-男1-女
   */
  sex?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 微信
   */
  wx?: string;
}

/**
 * 供应商基础信息
 */
export interface SupplierInfo {
  /**
   * 是否可编辑0-否1-是
   */
  canEdit?: string;
  /**
   * 澳洲本土供应商、海外供应商AUCN
   */
  country?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 供应商简称
   */
  shortName?: string;
  /**
   * 供应商code
   */
  supplierCode?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 供应商状态;0-禁用1-启用
   */
  supplierStatus?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface SupplierSettleAccountList {
  /**
   * AccountName
   */
  accountName?: string;
  /**
   * AccountNumber
   */
  accountNumber?: string;
  /**
   * BSB
   */
  bsb?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * SwiftCode
   */
  swiftCode?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

/**
 * 结算信息
 */
export interface SupplierSettleInfo {
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: number;
  /**
   * id
   */
  id?: number;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 0=非多币种1=多币种
   */
  isMultiCurrency?: number;
  /**
   * 结算类型
   */
  settleType?: string;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface SupplierList {
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 是否多币种
   */
  isMultiCurrency?: number;
}
