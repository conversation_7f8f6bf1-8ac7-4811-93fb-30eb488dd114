import { LogList } from '@/pages/purchase/detail/types/purchase.post.entity';
import { ReturnLineList } from './line.post.entity';

export interface ReutrnPostEntity {
  /**
   * 结算流水
   */
  finCapitalFlowList?: FinCapitalFlowList[];
  /**
   * 操作日志
   */
  logList?: LogList[];
  /**
   * 采购退货单明细信息
   */
  returnLineList?: ReturnLineList[];
  /**
   * 采购退货单信息
   */
  returnOrder?: ReturnOrder;
}

/**
 * 采购退货单信息
 */
export interface ReturnOrder {
  /**
   * 完成时间
   */
  completeTime?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 汇率
   */
  exchangeRate?: number;
  /**
   * 客户不收税
   */
  gstExcluded?: number;
  /**
   * 退货订单id
   */
  id?: string;
  /**
   * 一体系零售商id
   */
  memberId?: string;
  /**
   * 一体系零售商name
   */
  memberName?: string;
  /**
   * 退货总数量
   */
  num?: number;
  /**
   * 退货单号
   */
  orderNo?: string;
  /**
   * 退货订单状态;draft草稿、to_outbound待出库、outbound已出库、complete已完成、close已关闭
   */
  orderStatus?: string;
  /**
   * 退货订单状态;draft草稿、to_outbound待出库、outbound已出库、complete已完成、close已关闭
   */
  orderStatusDesc?: string;
  /**
   * 出库时间
   */
  outStockTime?: string;
  /**
   * 出库仓id
   */
  outWarehouseId?: string;
  /**
   * 出库仓name
   */
  outWarehouseName?: string;
  /**
   * 退款状态0：待退款1：已退款
   */
  payStatus?: string;
  /**
   * 退款状态0：待退款1：已退款
   */
  payStatusDesc?: string;
  /**
   * 具体退款方式，现款时不允许为空
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 退款方式0挂账1现款
   */
  payType?: string;
  /**
   * 退款方式0挂账1现款
   */
  payTypeDesc?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 退货原因
   */
  returnReason?: string;
  /**
   * 退货时间
   */
  returnTime?: string;
  /**
   * 结算时间
   */
  settlementTime?: string;
  /**
   * 实际退货总金额（sum_amount+freight_amount+total_taxation_amount）
   */
  shouldReturnTotalAmount?: number;
  /**
   * 退货来源
   */
  source?: string;
  /**
   * 来源单号;售后单号
   */
  sourceNo?: string;
  /**
   * 退货门店id
   */
  storeId?: string;
  /**
   * 退货门店名称
   */
  storeName?: string;
  /**
   * 一体系子订单号
   */
  subOrderNo?: string;
  /**
   * 退货总金额
   */
  sumAmount?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 税费总额
   */
  totalTaxationAmount?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: number;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}

export interface FinCapitalFlowList {
  /**
   * 账户id
   */
  accountId?: number;
  /**
   * 账户名称
   */
  accountName?: string;
  /**
   * 金额，单位：分
   */
  amount?: number;
  /**
   * 金额，单位：元
   */
  amountYuan?: number;
  /**
   * 业务单号
   */
  bizNo?: string;
  /**
   * 业务时间
   */
  bizTime?: string;
  /**
   * 业务类型
   */
  bizType?: number;
  /**
   * 买家id
   */
  buyerId?: string;
  /**
   * 买家名称
   */
  buyerName?: string;
  /**
   * 台账类型：1：收入，2：支出
   */
  ledgerType?: number;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 卖家id
   */
  sellerId?: string;
  /**
   * 卖家名称
   */
  sellerName?: string;
  /**
   * 资金流水号
   */
  serialNumber?: string;
  /**
   * 0：无效，1：有效
   */
  status?: number;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
}
