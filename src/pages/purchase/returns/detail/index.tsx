import ConfirmModal from '@/components/ConfirmModal';
import PaymentExternalFormModal from '@/components/PaymentExternalFormModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { KeepAliveTabContext } from '@/layouts/context';
import { PayStatus, payStatusOptions } from '@/pages/purchase/list/types/PayStatus';
import { RightOutlined } from '@ant-design/icons';
import type { ProDescriptionsActionType } from '@ant-design/pro-components';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import type { GetProps } from 'antd';
import { Flex, Space, Spin, Tag } from 'antd';
import { useContext, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CapitalFlow from '../../capitalFlow';
import { payTypeStatusOptions } from '../../list/types/PayTypeStatus';
import OperationDetail from '../../operationDetail';
import SupplierDetailDrawer from '../../supplier/components/SupplierDetailDrawer';
import { cancelReturnOrderPost, resetReturnOrderPost } from '../list/services';
import {
  RetrunOrderStatus,
  retrunOrderStatusOptions
} from '../list/types/RetrunOrderStatus';
import { confirmPayReturnPost, confirmStockOutReturnPost } from '../operation/services';
import { queryReturnOrderFacadeById } from '../services';
import { PostListTableColumns } from './config/postListTableColumns';
import type { ReturnLineList } from './types/line.post.entity';
import type { ReutrnPostEntity } from './types/return.post.entity';


export default () => {
  const actionRef = useRef<ProDescriptionsActionType>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [recordData, setRecordData] = useState<ReutrnPostEntity>({});
  const [loading, setLoading] = useState<boolean>(false);
  const { closeTab } = useContext(KeepAliveTabContext);

  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

  const [supplierDetailDrawer, setSupplierDetailDrawer] = useState<any>({
    open: false,
    supplierId: undefined,
  });

  useActivate(() => {
    actionRef.current?.reload(true);
  });

  /**
   * 一键出库
   * @param params
   */
  const hanldConfirmStockOut = async () => {
    setLoading(true);
    const data = await confirmStockOutReturnPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (data) {
      hideModal();
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
    setLoading(false);
  };

  const [paymentVisible, setPaymentVisible] = useState(false);
  /**
   * 编辑
   */
  const hanldUpdateReturns = async (orderId: string | undefined) => {
    if (orderId) {
      //关闭当前页面
      closeTab();
      history.push(`/purchase/returns/operation?returnOrderId=${orderId}`);
    }
  };
  /**
   * 作废
   */
  const hanldCancelReturnOrder = async () => {
    const data = await cancelReturnOrderPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (data) {
      hideModal();
      actionRef?.current?.reload(true);
    }
  };
  /**
   * 撤回
   */
  const handleDetailRestItem = async () => {
    const result = await resetReturnOrderPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (result) {
      hideModal();
      actionRef?.current?.reload(true);
    }
  };

  const hideModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 确认结算
   */
  const hanldConfirmPay = async (values: any) => {
    const data = await confirmPayReturnPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
      ...values,
    });
    if (data) {
      setPaymentVisible(false);
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  return (
    <PageContainer>
      <Spin spinning={loading}>
        <ProCard>
          <ProDescriptions
            actionRef={actionRef}
            title={
              <Flex vertical>
                <Space>
                  <span>{recordData?.returnOrder?.orderNo}</span>
                  <Tag
                    color="orange"
                  >
                    {retrunOrderStatusOptions[recordData?.returnOrder?.orderStatus!]?.text}
                  </Tag>
                  {![RetrunOrderStatus.DRAFT, RetrunOrderStatus.CLOSE].includes(
                    recordData?.returnOrder?.orderStatus!,
                  ) && (
                      <Tag color="green">
                        {payStatusOptions[recordData?.returnOrder?.payStatus!]?.text}
                      </Tag>
                    )}
                </Space>
              </Flex>
            }
            extra={
              <Space>
                {(RetrunOrderStatus.DRAFT == recordData?.returnOrder?.orderStatus ||
                  RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus) && (
                    <Space>
                      {RetrunOrderStatus.DRAFT == recordData?.returnOrder?.orderStatus && (
                        <AuthButton
                          authority="editPurchaseReturn"
                          type="primary"
                          ghost
                          onClick={() => hanldUpdateReturns(recordData?.returnOrder?.id)}
                        >
                          {t('purchase.returns.detail.button.edit')}
                        </AuthButton>
                      )}
                      <AuthButton
                        authority="deletePurchaseReturn"
                        type="primary"
                        ghost
                        onClick={() =>
                          setConfirmModalProps({
                            open: true,
                            tips: t('purchase.returns.detail.confirm.void'),
                            onOk: hanldCancelReturnOrder,
                          })
                        }
                      >
                        {t('purchase.returns.detail.button.void')}
                      </AuthButton>
                    </Space>
                  )}
                {RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus && (
                  <AuthButton
                    authority="withdrawPurchaseReturn"
                    type="primary"
                    ghost
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: t('purchase.returns.detail.confirm.withdraw'),
                        onOk: handleDetailRestItem,
                      })
                    }
                  >
                    {t('purchase.returns.detail.button.withdraw')}
                  </AuthButton>
                )}

                {/* <AuthButton
                  authority="purchaseReturnPrint"
                  type="primary"
                  ghost
                  onClick={() => {
                    window.open(
                      `/print?returnId=${searchParams.get('returnId')}&printType=${PrintType.purchaseReturnOrder
                      }`,
                    );
                  }}
                >
                  {t('purchase.returns.detail.button.print')}
                </AuthButton> */}
                {(RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus ||
                  RetrunOrderStatus.OUTBOUND == recordData?.returnOrder?.orderStatus) &&
                  PayStatus.UN_BALANCE == recordData?.returnOrder?.payStatus && (
                    <AuthButton
                      type="primary"
                      authority="purchaseReturnSettlement"
                      onClick={() => setPaymentVisible(true)}
                    >
                      {t('purchase.returns.detail.button.confirmSettlement')}
                    </AuthButton>
                  )}
                {RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus && (
                  <AuthButton
                    authority="purchaseReturnOutWareHouse"
                    type="primary"
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: t('purchase.returns.detail.confirm.oneClickOutbound'),
                        onOk: hanldConfirmStockOut,
                      })
                    }
                  >
                    {t('purchase.returns.detail.button.oneClickOutbound')}
                  </AuthButton>
                )}
              </Space>
            }
            params={{ id: searchParams.get('returnId') ?? '' }}
            request={async (param) => {
              if (param?.id) {
                const data = await queryReturnOrderFacadeById({ ...param });
                setRecordData(data);

                return { data, success: true };
              }
              return [];
            }}
            column={4}
          >
            <ProDescriptions.Item
              label={t('purchase.detail.label.supplier')}
              dataIndex={['returnOrder', 'supplierName']}
              render={(text, record) => {
                return (
                  <div className='flex justify-between cursor-pointer' onClick={() => setSupplierDetailDrawer({ open: true, supplierId: record?.returnOrder?.supplierId })}>
                    <span className='mr-5'>{record?.returnOrder?.supplierName}</span>
                    <RightOutlined style={{
                      color: '#0D0D0D',
                    }} />
                  </div>
                );
              }}
            />
            <ProDescriptions.Item
              label={t('purchase.returns.detail.label.orderStatus')}
              dataIndex={['returnOrder', 'orderStatus']}
              valueEnum={retrunOrderStatusOptions}
            />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.returnAmount')} dataIndex={['returnOrder', 'sumAmount']} />
            <ProDescriptions.Item
              label={t('purchase.returns.detail.label.paymentMethod')}
              dataIndex={['returnOrder', 'payType']}
              valueEnum={payTypeStatusOptions}
            />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.returnStore')} dataIndex={['returnOrder', 'storeName']} />
            <ProDescriptions.Item
              label={t('purchase.returns.detail.label.shippingWarehouse')}
              dataIndex={['returnOrder', 'outWarehouseName']}
            />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.orderTime')} dataIndex={['returnOrder', 'createTime']} />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.creator')} dataIndex={['returnOrder', 'creator']} />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.currency')} dataIndex={['returnOrder', 'currency']} />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.exchangeRate')} dataIndex={['returnOrder', 'exchangeRate']} />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.gstExcluded')} dataIndex={['returnOrder', 'gstExcluded']} render={(text) => Boolean(text) ? t('common.option.yes') : t('common.option.no')} />
            <ProDescriptions.Item label={t('purchase.returns.detail.label.remark')} dataIndex={['returnOrder', 'remark']} />
          </ProDescriptions>
        </ProCard>
        <ProCard
          className="mt-4"
          bodyStyle={{ padding: 0 }}
        >
          <FunProTable<ReturnLineList, any>
            headerTitle={<SubTitle text={t('purchase.returns.detail.section.productDetail')} />}
            columns={PostListTableColumns()}
            pagination={false}
            rowKey="id"
            dataSource={recordData?.returnLineList}
            search={false}
          />
          <div className="flex w-full p-6 justify-between text-[16px] text-black/[0.85] font-semibold ">
            <Space>
              <span>
                {t('purchase.returns.detail.summary.totalQuantity')}：<span>{recordData?.returnOrder?.num}</span>
              </span>
              <span>
                {t('purchase.returns.detail.summary.totalAmount')}：<span>{recordData?.returnOrder?.shouldReturnTotalAmount?.toFixed(2)}</span>
              </span>
              <span>
                {t('purchase.returns.detail.summary.gst')}：<span>{recordData?.returnOrder?.totalTaxationAmount?.toFixed(2)}</span>
              </span>
            </Space>
            <div>
              {t('purchase.returns.detail.summary.totalReturnAmount')}：
              <span className="text-[24px] font-medium text-primary">
                {recordData?.returnOrder?.sumAmount}
              </span>
            </div>
          </div>
        </ProCard>
        <CapitalFlow
          capitalData={recordData?.finCapitalFlowList?.map((s) => ({
            amount: s.amountYuan,
            confirmTime: s.bizTime,
            desc: s.accountName,
          }))}
        />
        <OperationDetail logList={recordData.logList} />
        <ConfirmModal
          {...confirmModalProps}
          onCancel={() => {
            setConfirmModalProps((preProps) => ({
              ...preProps,
              open: false,
            }));
          }}
        />
        <PaymentExternalFormModal
          totalAmount={recordData?.returnOrder?.sumAmount}
          visible={paymentVisible}
          storeId={recordData?.returnOrder?.storeId}
          onClose={() => setPaymentVisible(false)}
          onSubmit={hanldConfirmPay}
          dataSource={{
            payType: recordData?.returnOrder?.payType,
            paySubTypeList: recordData?.returnOrder?.paySubTypeList,
          }}
        />
      </Spin>
      <SupplierDetailDrawer {...supplierDetailDrawer} onClose={() => setSupplierDetailDrawer({ open: false })} />
    </PageContainer>
  );
};
