import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { ReturnLineList } from '../types/line.post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = (): ProColumns<ReturnLineList>[] => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });

  return [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: t('goods.list.table.itemName'),
      dataIndex: 'skuName',
      key: 'skuName',
      search: false,
      width: 120,
    },
    {
      title: t('goods.list.table.itemSn'),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      width: 100,
    },
    {
      title: t('goods.list.table.brandPartNos'),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: t('goods.list.table.brandName'),
      dataIndex: 'brandName',
      key: 'brandName',
      search: false,
      width: 100,
    },
    {
      title: t('goods.list.table.categoryName'),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: t('goods.list.table.unit'),
      dataIndex: 'unit',
      key: 'unit',
      search: false,
      width: 50,
    },
    {
      title: t('purchase.returns.operation.orderColumns.purchaseOrderNo'),
      dataIndex: 'orderNo',
      key: 'orderNo',
      search: false,
      width: 160,
    },
    {
      title: t('purchase.returns.operation.detailColumns.returnAmount'),
      dataIndex: 'price',
      key: 'price',
      search: false,
      width: 80,
    },
    {
      title: t('purchase.returns.operation.detailColumns.returnQuantity'),
      dataIndex: 'num',
      key: 'num',
      search: false,
      width: 80,
    },
    {
      title: t('purchase.external.columns.preTaxSubtotal'),
      dataIndex: 'sumPrice',
      width: 80,
    },
    {
      title: t('purchase.external.columns.afterTaxSubtotal'),
      dataIndex: 'taxationAmount',
      width: 80,
    },
    {
      title: t('purchase.returns.operation.detailColumns.returnReason'),
      dataIndex: 'reason',
      key: 'reason',
      search: false,
      width: 100,
      ellipsis: true,
    },
  ];
};
