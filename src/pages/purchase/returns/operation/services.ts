import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { AddItemResult } from './types/add.item.result';
import { CreateReturnOrderRequest } from './types/create.return.order.request';
import { DeleteReturnOrderRequest } from './types/delete.return.request';
import { OrderLineReturnRequest } from './types/order.line.return.request';
import { PostOrderLineEntity } from './types/post.order.line.entity';
import { ReturnItemRequest } from './types/return.item.request';
import { UpdateReturnRequest } from './types/update.return.request';
import { UpdateReturnTypeRequest } from './types/update.return.type.request';
import { UpdateReturnWarehouse } from './types/update.return.warehouse';

export const createReturnOrderPost = async (params: Partial<CreateReturnOrderRequest>) => {
  return request<AddItemResult>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/createReturnOrder',
    {
      data: params,
    },
  );
};

/**
 * 添加商品明细
 * @param params
 * @returns
 */
export const addItemReturnsPost = async (params: Partial<ReturnItemRequest>) => {
  return request<AddItemResult>('/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/addItem', {
    data: params,
  });
};
/**
 * 删除品明细
 * @param params
 * @returns
 */
export const deleteItemsReturnsPost = async (params: Partial<DeleteReturnOrderRequest>) => {
  return request<AddItemResult>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/deleteItems',
    {
      data: params,
    },
  );
};
/**
 * 更新商品明细(数量价格)
 * @param params
 * @returns
 */
export const updateItemPost = async (params: Partial<UpdateReturnRequest>) => {
  return request<AddItemResult>('/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/updateItem', {
    data: params,
  });
};

/**
 * 更新备注、汇率、关联单号
 */
export const updateRemarkAndBasePost = async (params: Partial<UpdateReturnTypeRequest>): Promise<boolean> => {
  return request(
    '/ipmspurchase/purchaseReturnOrder/updateRemarkAndBase',
    {
      data: params,
    },
  );
};

/**
 * 原因
 * @param params
 * @returns
 */
export const updateReturnReasonPost = async (params: Partial<UpdateReturnRequest>) => {
  return request<AddItemResult>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/updateReturnReason',
    {
      data: params,
    },
  );
};

/**
 * 更新退款方式
 * @param params
 * @returns
 */
export const updateReturnTypePost = async (params: Partial<UpdateReturnTypeRequest>) => {
  return request<AddItemResult>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/updateReturnType',
    {
      data: params,
    },
  );
};

/**
 * 提交采购退货单
 */

export const submitReturnOrderPost = async (params: Partial<ReturnItemRequest>) => {
  return request<AddItemResult>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/submitReturnOrder',
    {
      data: params,
    },
  );
};
/**
 * 确认结算
 * @param params
 * @returns
 */
export const confirmPayReturnPost = async (params: Partial<ReturnItemRequest>) => {
  return request<boolean>('/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/confirmPay', {
    data: params,
  });
};
/**
 * 一键出库 直接出库
 * @param params
 * @returns
 */
export const confirmStockOutReturnPost = async (params: Partial<ReturnItemRequest>) => {
  return request<boolean>('/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/confirmStockOut', {
    data: params,
  });
};

/**
 * 列表查询 查询可退货明细列表(采购单退货)
 * @param params
 * @returns
 */
export const queryOrderLineFacadeListPost = async (
  params: Partial<OrderLineReturnRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<PostOrderLineEntity>>(
    `/ipmspurchase/purchase/PurchaseOrderLineFacade/queryAvailablePage`,
    {
      data: params,
    },
  );
};

/**
 * 修改 发货仓库
 */

export const returnUpdateOutWarehousePost = async (params: Partial<UpdateReturnWarehouse>) => {
  return request<boolean>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/updateOutWarehouse',
    {
      data: params,
    },
  );
};
