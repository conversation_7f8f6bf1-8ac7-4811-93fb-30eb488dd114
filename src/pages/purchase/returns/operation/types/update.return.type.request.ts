export interface UpdateReturnTypeRequest {
  /**
   * 退货单id
   */
  orderId?: string;
  /**
   * 退货单号
   */
  orderNo?: string;
  /**
   * 具体退款方式，现款时不允许为空
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 退款方式0挂账1现款
   */
  payType?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}
