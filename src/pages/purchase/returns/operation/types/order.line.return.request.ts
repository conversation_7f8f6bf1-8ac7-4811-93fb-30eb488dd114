export interface OrderLineReturnRequest {
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 商品编码,模糊匹配名称编码OE供应商编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 采购单号
   */
  orderNo?: string;
  /**
   * 采购状态
   */
  orderStatus?: string;
  /**
   * None
   */
  pageNo?: number;
  /**
   * None
   */
  pageSize?: number;
  /**
   * 收货仓id
   */
  receiveWarehouseId?: string;
  /**
   * None
   */
  startRow?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 发起门店id列表
   */
  storeIdList?: string[];
  /**
   * 供应商id
   */
  supplierId?: string;
}
