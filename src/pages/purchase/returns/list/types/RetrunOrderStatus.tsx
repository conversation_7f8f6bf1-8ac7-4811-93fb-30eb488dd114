import { FormattedMessage } from "@umijs/max";

export enum RetrunOrderStatus {
  DRAFT = 'draft',
  TO_OUTBOUND = 'to_outbound',
  OUTBOUND = 'outbound',
  COMPLETE = 'complete',
  CLOSE = 'close',
}

export enum RetrunOrderStatusName {
  DRAFT = 'purchase.returnOrderStatus.draft',
  TO_OUTBOUND = 'purchase.returnOrderStatus.toOutbound',
  OUTBOUND = 'purchase.returnOrderStatus.outbound',
  COMPLETE = 'purchase.returnOrderStatus.complete',
  CLOSE = 'purchase.returnOrderStatus.close',
}
export const retrunOrderStatusOptions = {
  [RetrunOrderStatus.DRAFT]: { text: <FormattedMessage id={RetrunOrderStatusName.DRAFT} /> },
  [RetrunOrderStatus.TO_OUTBOUND]: { text: <FormattedMessage id={RetrunOrderStatusName.TO_OUTBOUND} /> },
  [RetrunOrderStatus.OUTBOUND]: { text: <FormattedMessage id={RetrunOrderStatusName.OUTBOUND} /> },
  [RetrunOrderStatus.COMPLETE]: { text: <FormattedMessage id={RetrunOrderStatusName.COMPLETE} /> },
  [RetrunOrderStatus.CLOSE]: { text: <FormattedMessage id={RetrunOrderStatusName.CLOSE} /> },
};
export const retrunOrderColorOptions = {
  [RetrunOrderStatus.DRAFT]: { status: 'error' },
  [RetrunOrderStatus.TO_OUTBOUND]: { status: 'error' },
  [RetrunOrderStatus.OUTBOUND]: { status: 'error' },
  [RetrunOrderStatus.COMPLETE]: { status: 'success' },
  [RetrunOrderStatus.CLOSE]: { status: 'default' },
};
