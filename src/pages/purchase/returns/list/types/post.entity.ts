import { PayStatus } from '@/pages/purchase/list/types/PayStatus';
import { RetrunOrderStatus } from './RetrunOrderStatus';
export interface PostEntity {
  /**
   * 创建人
   */
  createPerson?: string;
  creator?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 退货订单id
   */
  id: string;
  /**
   * 一体系零售商id
   */
  memberId?: string;
  /**
   * 一体系零售商name
   */
  memberName?: string;
  /**
   * 退货总数量
   */
  num?: number;
  /**
   * 退货单号
   */
  orderNo?: string;
  /**
   * 退货订单状态;draft草稿、to_outbound待出库、outbound已出库、complete已完成、close已关闭
   */
  orderStatus?: RetrunOrderStatus;
  /**
   * 退货订单状态;draft草稿、to_outbound待出库、outbound已出库、complete已完成、close已关闭
   */
  orderStatusDesc?: string;
  /**
   * 出库仓id
   */
  outWarehouseId?: string;
  /**
   * 出库仓name
   */
  outWarehouseName?: string;
  /**
   * 退款状态0：待退款1：已退款
   */
  payStatus?: PayStatus;
  /**
   * 退款状态0：待退款1：已退款
   */
  payStatusDesc?: string;
  /**
   * 具体退款方式，现款时不允许为空
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 退款方式0挂账1现款
   */
  payType?: string;
  /**
   * 退款方式0挂账1现款
   */
  payTypeDesc?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 退货原因
   */
  returnReason?: string;
  /**
   * 退货时间
   */
  returnTime?: string;

  /**
   * 退货完成时间
   */
  completeTime?: string;
  /**
   * 来源单号;售后单号
   */
  sourceNo?: string;
  /**
   * 一体系子订单号
   */
  subOrderNo?: string;
  /**
   * 退货总金额
   */
  sumAmount?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 退货门店id
   */
  storeId?: string;
  /**
   * 退货门店名称
   */
  storeName?: string;
}
export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}
