import type { FinReceivableEntity } from '@/pages/finance/collection/types/FinReceivableEntity.entity';
import type { ProColumns } from '@ant-design/pro-components';
import { ProFormDigit } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { Tag } from 'antd';
import { MoneyFormat } from '..';

export interface CreatePaymentOrderDetailColumnsProps {
  handleUpdate: (record: FinReceivableEntity) => void;
}

export const getCreateColumns = (intl: IntlShape, props: CreatePaymentOrderDetailColumnsProps) =>
  [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.storeName' }),
      dataIndex: 'storeName',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.businessOrderNo' }),
      dataIndex: 'orderNo',
      width: 100,
      editable: false,
      render: (text, record) => (
        <>
          {text}
          {
            record.tag && <Tag color='red'>{record.tag}</Tag>
          }
        </>
      ),
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.transactionCompleteTime' }),
      dataIndex: 'billDate',
      width: 140,
      editable: false,
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
      editable: false,
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.orderAmount' }),
      dataIndex: 'orderAmountYuan',
      search: false,
      width: 100,
      editable: false,
      renderText: (text) => <MoneyFormat money={text} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.receivedAmountPaid' }),
      dataIndex: 'receivedAmountYuan',
      search: false,
      width: 100,
      editable: false,
      renderText: (text) => <MoneyFormat money={text} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.unreceived' }),
      dataIndex: 'remainReceivableAmountYuan',
      search: false,
      width: 100,
      editable: false,
      renderText: (text) => <MoneyFormat money={text} />,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.currentWriteOff' }),
      dataIndex: 'currReceivedAmount',
      search: false,
      width: 120,
      editable: false,
      render: (text, record) => (
        <ProFormDigit
          placeholder={intl.formatMessage({ id: 'finance.receive.placeholders.enterAmount' })}
          max={999999999.99}
          min={-999999999.99}
          fieldProps={{
            controls: false,
            precision: 2,
            value: record?.currReceivedAmount,
            onChange: (value) => props.handleUpdate({ ...record, currReceivedAmount: value }),
          }}
          formItemProps={{

          }}
        />
      ),
    },
  ] as ProColumns<FinReceivableEntity>[];
