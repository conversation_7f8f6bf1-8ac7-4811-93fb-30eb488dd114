import FunProTable from "@/components/common/FunProTable";
import LeftTitle from "@/components/LeftTitle";
import { queryReceivedFlowList, updateReceivedStatus } from "@/pages/finance/receive/services";
import type { ReceivedDetailModalType } from "@/pages/finance/receive/types/ReceivedDetailModalType";
import type { ReceivedEntity } from "@/pages/finance/receive/types/ReceivedEntity";
import { ReceivedStatus } from "@/pages/finance/receive/types/ReceivedEntity";
import type { ReceivedFlowEntity } from "@/pages/finance/receive/types/ReceivedFlowEntity";
import { ExclamationCircleFilled } from '@ant-design/icons';
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { App, Button, Tag } from 'antd';
import { useState } from "react";
import { MoneyFormat } from "../..";
import { getDetailColumns } from '../../config/DetailColumns';
import { ReceivedStatusEnum } from '../../types/ReceivedEntityEnum';
import AuditModal from '../AuditModal';

export default (props: ReceivedDetailModalType) => {
  const { modal } = App.useApp();
  const intl = useIntl();
  const [finReceivedRo, setFinReceivedRo] = useState<ReceivedEntity | null>(null);
  const [finReceivedFlowRoList, setFinReceivedFlowRoList] = useState<ReceivedFlowEntity[]>([]);
  const [auditModalVisible, setAuditModalVisible] = useState(false);

  const onRefresh = async () => {
    const receivedFlow = await queryReceivedFlowList(props.serialNumber);
    setFinReceivedRo({ ...receivedFlow?.finReceivedRo, receivedAccountName: receivedFlow?.finReceivedRo?.receivedAccountName || '' });
    setFinReceivedFlowRoList(
      receivedFlow?.finReceivedFlowRoList?.map(flow => ({
        ...flow,
        storeName: receivedFlow?.finReceivedRo?.storeName,
        receivedAmount: flow.receivedAmount || '0',
      }))
    );
  };

  useAsyncEffect(async () => {
    if (props.visible && props.serialNumber) {
      onRefresh();
    } else if (!props.visible) {
      setFinReceivedRo(null);
      setFinReceivedFlowRoList([]);
    }
  }, [props.visible, props.serialNumber]);

  const handleAuditSuccess = () => {
    onRefresh();
    setAuditModalVisible(false)
  }

  const handleCancelReceived = () => {
    modal.confirm({
      title: intl.formatMessage({ id: 'finance.receive.cancel.title' }),
      icon: <ExclamationCircleFilled />,
      content: intl.formatMessage({ id: 'finance.receive.cancel.content' }),
      okText: intl.formatMessage({ id: 'finance.receive.cancel.confirm' }),
      cancelText: intl.formatMessage({ id: 'finance.receive.cancel.giveup' }),
      onOk: async () => {
        if (props.serialNumber) {
          const result = await updateReceivedStatus({
            serialNumber: props.serialNumber,
            status: ReceivedStatus.CANCELLED,
          });
          if (result) {
            onRefresh();
          }
        }
      },
    });
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        destroyOnClose: true,
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={{
        render: () => (
          <div style={{ textAlign: 'right' }}>
            {[ReceivedStatus.PENDING, ReceivedStatus.UNPAYMENT].includes(finReceivedRo?.status as ReceivedStatus) && <Button type="primary" ghost className='mr-2' onClick={handleCancelReceived}>
              {intl.formatMessage({ id: 'finance.receive.cancel.title' })}
            </Button>}
            {finReceivedRo?.status === ReceivedStatus.PENDING && <Button type="primary" onClick={() => setAuditModalVisible(true)}>
              {intl.formatMessage({ id: 'finance.receive.audit.title' })}
            </Button>}
          </div>
        ),
      }}
    >
      <ProCard className="mb-4">
        <ProDescriptions<ReceivedEntity, any>
          title={<>
            {
              props.serialNumber}
            <Tag className='ml-2' color={ReceivedStatusEnum[finReceivedRo?.status as ReceivedStatus]?.color}>{ReceivedStatusEnum[finReceivedRo?.status as ReceivedStatus]?.text}</Tag>
          </>
          }
          column={3}
          dataSource={finReceivedRo}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.customerName' }),
              dataIndex: 'buyerName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.store' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
              dataIndex: 'businessTime',
              valueType: 'date',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.currency' }),
              dataIndex: 'currency',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.rate' }),
              dataIndex: 'rate',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' }),
              dataIndex: 'receivedAccountName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' }),
              dataIndex: 'totalReceivedAmountYuan',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.adjustAmount' }),
              dataIndex: 'adjustAmount',
              renderText: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.writeOffTotal' }),
              dataIndex: 'writeOffAmount',
              renderText: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.advanceAmount' }),
              dataIndex: 'advanceAmount',
              renderText: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.lossAmount' }),
              dataIndex: 'lossAmount',
              renderText: (text) => <MoneyFormat money={text} />,
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
              dataIndex: 'createPerson',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.image' }),
              dataIndex: 'receivePic',
              valueType: 'image',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
        {[ReceivedStatus.PAYMENT, ReceivedStatus.ABORTED].includes(finReceivedRo?.status as ReceivedStatus) && <div className="p-4 mt-4 bg-[#FDF5E8] rounded">
          <span>
            审核人：{finReceivedRo?.auditor}
          </span>
          <span className="ml-2">
            审核时间：{finReceivedRo?.auditTime}
          </span>
        </div>}
      </ProCard>
      <FunProTable<ReceivedFlowEntity, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'finance.receive.writeOffOrder' })} />}
        scroll={{ x: 'max-content' }}
        search={false}
        pagination={false}
        dataSource={finReceivedFlowRoList}
        options={false}
        columns={getDetailColumns(intl)}
      />
      {props.serialNumber && <AuditModal
        visible={auditModalVisible}
        onCancel={() => setAuditModalVisible(false)}
        onSuccess={handleAuditSuccess}
        serialNumber={props.serialNumber}
      />}
    </DrawerForm>
  );
};
