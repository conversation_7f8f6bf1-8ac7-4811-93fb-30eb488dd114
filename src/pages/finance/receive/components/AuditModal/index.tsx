
import Selector from '@/components/Selector';
import { confirmReceived, updateReceivedStatus } from '@/pages/finance/receive/services';
import { ReceivedStatus } from '@/pages/finance/receive/types/ReceivedEntity';
import { ModalForm, ProForm, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { message } from 'antd';
import { useState } from 'react';

interface AuditModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  serialNumber: string;
}

export default (props: AuditModalProps) => {
  const intl = useIntl();
  const [form] = ProForm.useForm();
  const [auditStatus, setAuditStatus] = useState<ReceivedStatus>(ReceivedStatus.UNPAYMENT);

  const handleSubmit = async (values: any) => {
    const { remark, } = values;
    let success = false;
    // 审核不通过
    if (auditStatus === ReceivedStatus.ABORTED) {
      success = await updateReceivedStatus({
        serialNumber: props.serialNumber,
        status: auditStatus,
        remark,
      });
    }
    // 审核通过
    if (auditStatus === ReceivedStatus.PAYMENT) {
      success = await confirmReceived({
        serialNumber: props.serialNumber,
        status: auditStatus,
      });
    }

    if (success) {
      message.success(intl.formatMessage({ id: 'common.message.operation.success' }));
      props.onSuccess();
      return true;
    }
    return false;
  };

  return (
    <ModalForm
      title={intl.formatMessage({ id: 'finance.receive.audit.title' })}
      open={props.visible}
      form={form}
      onFinish={handleSubmit}
      modalProps={{
        onCancel: props.onCancel,
        destroyOnClose: true,
      }}
      width={520}
    >
      <Selector
        className='mb-4'
        size='large'
        isFullWidth
        activeKey={auditStatus}
        onSelect={setAuditStatus}
        options={[
          {
            label: intl.formatMessage({ id: 'finance.receive.audit.approve' }),
            key: ReceivedStatus.PAYMENT,
          },
          {
            label: intl.formatMessage({ id: 'finance.receive.audit.reject' }),
            key: ReceivedStatus.ABORTED,
          },
        ]}
      />
      <ProFormTextArea
        fieldProps={{
          maxLength: 100,
          showCount: true,
        }}
        name="remark"
        placeholder={intl.formatMessage({ id: auditStatus === ReceivedStatus.ABORTED ? 'finance.receive.audit.reject.reason.placeholder' : 'common.placeholder.input' })}
        rules={[
          {
            required: auditStatus === ReceivedStatus.ABORTED,
          },
        ]}
      />
    </ModalForm>
  );
};
