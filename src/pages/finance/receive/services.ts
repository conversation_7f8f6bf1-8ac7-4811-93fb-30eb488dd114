import { ReceivedFlowDetailEntity } from '@/pages/finance/receive/types/ReceivedFlowDetailEntity';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { ReceivedConfirmation } from './types/ReceivedConfirmation';
import { ReceivedStatus, type ReceivedEntity } from './types/ReceivedEntity';

/**
 * 收款分页查询
 *
 * @param params
 * @returns
 */
export const queryReceivedPage = async (
  params: Partial<ReceivedEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<ReceivedEntity>>(`/ipmsaccount/queryRecevedPage`, {
    data: params,
  });
};
/**
 * 查询实收流水
 *
 * @param params
 * @returns
 */
export const queryReceivedFlowList = async (params: string): Promise<ReceivedFlowDetailEntity> => {
  return request(
    `/ipmsaccount/queryReceivedFlowList`,
    {
      data: { receivedSerialNumber: params },
    },
  );
};
/**
 * 实收
 *
 * @param params
 * @returns
 */
export const receivedConfirmation = async (params: ReceivedConfirmation) => {
  return request<PageResponseDataType<string>>(`/ipmsaccount/receivedConfirmation`, {
    data: params,
  });
};


/**
 * 审核通过
 */
interface AuditReceived { serialNumber: string; status: ReceivedStatus; remark?: string }
export const confirmReceived = async (params: AuditReceived): Promise<boolean> => {
  return request(`/ipmsaccount/confirmReceived`, {
    data: params,
  });
};

/**
 * 取消收款单或审核不通过
 */
export const updateReceivedStatus = async (params: AuditReceived): Promise<boolean> => {
  return request(`/ipmsaccount/auditReceived`, {
    data: params,
  });
};

// 保存收款单
export const saveReceived = async (params: ReceivedConfirmation): Promise<string> => {
  return request(`/ipmsaccount/saveReceived`, {
    data: params,
  });
};

// 查询客户剩余应收(多币种)
export interface TotalReceivableEntity {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}
export const queryTotalReceivable = async (params: Partial<{
  buyerId?: string;
}>): Promise<TotalReceivableEntity[]> => {
  return request(`/ipmsaccount/queryTotalReceivable`, {
    data: params,
  });
};