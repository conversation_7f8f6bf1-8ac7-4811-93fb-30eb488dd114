import { FormattedMessage } from '@umijs/max';
import { ReceivedStatus } from "./ReceivedEntity";

export const ReceivedStatusEnum = {
    [ReceivedStatus.CANCELLED]: { text: <FormattedMessage id="finance.receive.status.cancelled" />, status: 'Error', color: 'red' },
    [ReceivedStatus.ABORTED]: { text: <FormattedMessage id="finance.receive.status.aborted" />, status: 'Error', color: 'red' },
    [ReceivedStatus.DRAFT]: { text: <FormattedMessage id="finance.receive.status.draft" />, status: 'Warning', color: 'orange' },
    [ReceivedStatus.PENDING]: { text: <FormattedMessage id="finance.receive.status.pending" />, status: 'Warning', color: 'orange' },
    [ReceivedStatus.UNPAYMENT]: { text: <FormattedMessage id="finance.receive.status.unpayment" />, status: 'Warning', color: 'orange' },
    [ReceivedStatus.PAYMENT]: { text: <FormattedMessage id="finance.receive.status.payment" />, status: 'Success', color: 'green' },
};

// export const ReceivedTypeEnum = {
//   [ReceivedType.INCOME]: { text: '收入', status: 'Success' },
//   [ReceivedType.EXPENSE]: { text: '支出', status: 'Error' },
// };