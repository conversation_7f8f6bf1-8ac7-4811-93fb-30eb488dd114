

import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type AccountFlowEntity } from './types/AccountFlow.entity';

/**
 * 账户流水分页查询
 * @param params
 */
export const queryAccountFlowPage = async (
    params: Partial<AccountFlowEntity> & PageRequestParamsType,
): Promise<PageResponseDataType<AccountFlowEntity>> => {
    return request(`/ipmsaccount/queryAccountFlowPage`, {
        data: params,
    });
};