export enum AccountFlowBizType {
    /**
     * 预收充值
     */
    RECEIVED_INCOME = 15,
    /**
     * 收款使用预收
     */
    ADVANCE_RECEIVED_IN = 16,
    /**
     * 销售使用预收
     */
    ADVANCE_SALE_OUT = 17,
    /**     
     * 退货转预收
     *  */
    ADVANCE_REFUND_IN = 18,
}



export interface AccountFlowEntity {
    /**
     * 预收余额（多币种）
     */
    advanceAccountRoList?: AdvanceAccountRoList[];
    createPerson?: string;
    createTime?: string;
    /**
     * 账户流水明细
     */
    finadvanceFlowRoList?: FinadvanceFlowRoList[];
    isDelete?: number;
    memberId?: string;
    updatePerson?: string;
    updateTime?: string;
}

export interface AdvanceAccountRoList {
    /**
     * 可用额度
     */
    availableAmount?: number;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
}

export interface FinadvanceFlowRoList {
    /**
     * 账户余额，单位：分
     */
    accountBalance?: number;
    /**
     * 账户表ID
     */
    accountId?: number;
    /**
     * 台账金额，单位：分
     */
    amount?: number;
    /**
     * 业务描述
     */
    bizDesc?: string;
    /**
     * 业务单号（即订单号）
     */
    bizNo?: string;
    /**
     * 业务时间
     */
    bizTime?: string;
    /**
     * 业务类型(如冻结、支付、退款)
     */
    bizType?: number;
    /**
     * 业务类型名称（销售、退货、收款）
     */
    bizTypeName?: string;
    createPerson?: string;
    createTime?: string;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 客户ID
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 自增ID
     */
    id?: number;
    isDelete?: number;
    /**
     * 台账类型1收入2支出
     */
    ledgerType?: number;
    memberId?: string;
    /**
     * 汇率
     */
    rate?: number;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 流水号
     */
    serialNum?: string;
    /**
     * 解冻、支付时记录原始冻结单号，此时解冻记录额biz_no=1001_biz_no，支付=1002_biz_no，退款时，找对应的支付记录根据biz_no=1002_biz_no查询
     */
    sourceBizNo?: string;
    /**
     * 临时流水状态：0：无效1：有效
     */
    status?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 交易金额
     */
    tradeAmount?: number;
    /**
     * 操作人
     */
    updatePerson?: string;
    /**
     * 更新时间
     */
    updateTime?: string;
}
