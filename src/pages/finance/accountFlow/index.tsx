import FunProTable from '@/components/common/FunProTable';
import { getCstList } from '@/pages/customer/list/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { queryAccountFlowPage } from './service';
import {
  AccountFlowBizType,
  type AdvanceAccountRoList,
  type FinadvanceFlowRoList,
} from './types/AccountFlow.entity';

const AccountFlowPage = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [totalBalance, setTotalBalance] = useState<AdvanceAccountRoList[]>([]);

  const t = (id: string) => intl.formatMessage({ id });

  const columns: ProColumns<FinadvanceFlowRoList>[] = [
    {
      title: t('common.column.index'),
      dataIndex: 'index',
      valueType: 'index',
      width: 48,
    },
    {
      title: t('finance.accountFlow.columns.bizNo'),
      dataIndex: 'bizNo',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.bizNo'),
      dataIndex: 'bizNo',
      hideInTable: true,
      order: 3,
    },
    {
      title: t('finance.accountFlow.columns.bizTime'),
      dataIndex: 'bizTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.customerName'),
      dataIndex: 'customerName',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.customerName'),
      dataIndex: 'customerId',
      hideInTable: true,
      valueType: 'select',
      order: 5,
      fieldProps: {
        showSearch: true,
      },
      request: async (query) => {
        const data = await getCstList({ keyword: query.keyWords });
        return data?.map(({ cstId, cstName }) => ({
          value: cstId,
          label: cstName,
        }));
      },
    },
    {
      title: t('finance.accountFlow.columns.bizTypeName'),
      dataIndex: 'bizTypeName',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.bizTypeName'),
      dataIndex: 'bizTypeList',
      valueType: 'select',
      hideInTable: true,
      order: 2,
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: {
        [AccountFlowBizType.RECEIVED_INCOME]: t('finance.accountFlow.bizType.RECEIVED_INCOME'),
        [AccountFlowBizType.ADVANCE_RECEIVED_IN]: t(
          'finance.accountFlow.bizType.ADVANCE_RECEIVED_IN',
        ),
        [AccountFlowBizType.ADVANCE_SALE_OUT]: t('finance.accountFlow.bizType.ADVANCE_SALE_OUT'),
        [AccountFlowBizType.ADVANCE_REFUND_IN]: t('finance.accountFlow.bizType.ADVANCE_REFUND_IN'),
      },
    },
    {
      title: t('finance.accountFlow.columns.storeName'),
      dataIndex: 'storeName',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.storeName'),
      dataIndex: 'storeIdList',
      valueType: 'select',
      hideInTable: true,
      order: 4,
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('finance.accountFlow.columns.amount'),
      dataIndex: 'amount',
      align: 'right',
      hideInSearch: true,
      renderText: (text, record) => {
        const amount = (text as number) / 100;
        const sign = record.ledgerType === 1 ? '+' : '-';
        return (
          <div>
            {sign}
            {amount.toFixed(2)}
          </div>
        );
      },
    },
    {
      title: t('finance.accountFlow.columns.accountBalance'),
      dataIndex: 'accountBalance',
      align: 'right',
      hideInSearch: true,
      renderText: (text) => {
        const amount = (text as number) / 100;
        return amount.toFixed(2);
      },
    },
    {
      title: t('finance.accountFlow.columns.createPerson'),
      dataIndex: 'createPerson',
      hideInSearch: true,
    },
    {
      title: t('finance.accountFlow.columns.bizTime'),
      dataIndex: 'bizTimeRange',
      valueType: 'dateRange',
      hideInTable: true,
      order: 1,
      search: {
        transform: (value: any) => ({
          startBizTime: `${value[0]} 00:00:00`,
          endBizTime: `${value[1]} 23:59:59`,
        }),
      },
    },
  ];

  return (
    <PageContainer>
      <FunProTable<FinadvanceFlowRoList, any>
        rowKey="id"
        headerTitle={
          <div className="w-full">
            <div className="flex align-bottom">
              <span className="text-gray-500 text-base mr-4">
                {t('finance.accountFlow.totalAdvanceBalance')}
              </span>
              <span className="text-[24px] font-medium text-primary">
                {totalBalance.map((item, index) => (
                  <>
                    {item.currencySymbol}
                    {(item.availableAmount ? item.availableAmount / 100 : 0).toFixed(2)}
                    {index < totalBalance.length - 1 ? '; ' : ''}
                  </>
                ))}
              </span>
            </div>
          </div>
        }
        requestPage={async (params) => {
          // @ts-ignore
          const result = await queryAccountFlowPage(params);
          const { data = [], total = 0 } = result || {};
          setTotalBalance(data?.[0]?.advanceAccountRoList || []);
          const pageData = data?.[0]?.finadvanceFlowRoList || [];
          return {
            data: pageData || [],
            success: true,
            total,
          };
        }}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={columns}
      />
    </PageContainer>
  );
};

export default withKeepAlive(AccountFlowPage);
