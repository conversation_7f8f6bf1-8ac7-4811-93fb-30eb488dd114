import { CommonStatusValueEnum } from '@/types/CommonStatus';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { type ProColumns } from '@ant-design/pro-components';
import { Input, Tag, Tooltip } from 'antd';
import type { CurrencyEntity } from '../types/CurrencyEntity';

export const getCurrencyTableColumns = ({
  intl,
}: {
  intl: any;
}): ProColumns<CurrencyEntity>[] => {
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.currency.name' }), // 币种名称
      dataIndex: 'targetCurrency',
      render: (text, record) => {
        return <>
          {record?.targetCurrency}
          {
            Boolean(record?.isBaseCurrency) && <Tag color="orange" className="ml-2" >
              {intl.formatMessage({ id: 'finance.currency.isBase' })}
            </Tag>
          }
        </>;
      },
      renderFormItem: (item, config) => {
        return <Input maxLength={3} />
      },
      formItemProps: {
        rules: [REQUIRED_RULES]
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.currency.symbol' }), // 币种符号
      dataIndex: 'currencySymbol',
      formItemProps: {
        rules: [REQUIRED_RULES]
      },
    },
    {
      title: <>
        {intl.formatMessage({ id: 'finance.currency.exchangeRate' })}
        <Tooltip title={intl.formatMessage({ id: 'finance.currency.exchangeRate.tooltip' })}>
          <QuestionCircleOutlined className='ml-1' />
        </Tooltip>
      </>,
      dataIndex: 'rate',
      valueType: 'digit',
      formItemProps: {
        rules: [
          REQUIRED_RULES
        ],
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.tag.columns.isEnabled' }),
      dataIndex: 'isActive',
      valueEnum: CommonStatusValueEnum,
      fieldProps: {
        allowClear: false,
      },
    },
  ];
};