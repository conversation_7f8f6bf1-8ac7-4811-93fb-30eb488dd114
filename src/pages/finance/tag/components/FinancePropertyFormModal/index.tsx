import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { REG_LENGTH_RULE, REQUIRED_RULES, REQUIRED_RULES_NOT_ALLOW_EMPTY } from '@/utils/RuleUtils';
import { ProFormRadio } from "@ant-design/pro-components";
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useForm } from 'antd/lib/form/Form';
import { useEffect } from 'react';

export default ({
  title,
  recordId,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const intl = useIntl();
  const [form] = useForm();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible])

  return (
    <ModalForm
      form={form}
      title={title}
      open={visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      <ProFormText
        rules={[REQUIRED_RULES, REQUIRED_RULES_NOT_ALLOW_EMPTY, REG_LENGTH_RULE]}
        name="tagName"
        label={intl.formatMessage({ id: 'finance.tag.form.incomeExpenseType' })}
        placeholder={intl.formatMessage({ id: 'finance.tag.form.pleaseInput' })}
      />
      <ProFormText name="id" hidden={true} />
      <ProFormRadio.Group
        name="ledgerType"
        label={intl.formatMessage({ id: 'finance.tag.form.incomeExpenseDirection' })}
        initialValue={1}
        options={[
          { label: intl.formatMessage({ id: 'finance.tag.form.income' }), value: 1 },
          { label: intl.formatMessage({ id: 'finance.tag.form.expense' }), value: 2 },
        ]}
      />
    </ModalForm>
  );
};
