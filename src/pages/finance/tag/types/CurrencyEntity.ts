/**
 * 币种
 */
export interface CurrencyEntity {
    /**
     * 基准货币代码
     */
    baseCurrency?: string;
    createPerson?: string;
    createTime?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
    /**
     * 主键
     */
    id?: string;
    /**
     * 是否有效（0无效，1有效）
     */
    isActive?: number;
    /**
     * 是否本位币（0否，1是）
     */
    isBaseCurrency?: number;
    isDelete?: number;
    /**
     * 零售商id
     */
    memberId?: string;
    /**
     * 汇率值
     */
    rate?: number;
    /**
     * 目标货币代码
     */
    targetCurrency?: string;
    updatePerson?: string;
    updateTime?: string;
}
