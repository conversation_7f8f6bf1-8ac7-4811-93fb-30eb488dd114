

import { FormattedMessage } from "@umijs/max";

// 状态枚举
export enum FinanceCostStateEnum {
  /**
   * 已作废
   */
  CANCELLED = -2,
  /**
   * 审核不通过
   */
  ABORTED = -1,
  /**
   * 待审核
   */
  PENDING = 0,
  /**
   * 已确认
   */
  CONFIRMED = 1,
}

export const FinanceCostStateEnumMap = {
  [FinanceCostStateEnum.CANCELLED]: {
    text: <FormattedMessage id="finance.cost.status.cancelled" />,
    color: 'red',
  },
  [FinanceCostStateEnum.ABORTED]: {
    text: <FormattedMessage id="finance.cost.status.aborted" />,
    color: 'red',
  },
  [FinanceCostStateEnum.PENDING]: {
    text: <FormattedMessage id="finance.cost.status.pending" />,
    color: 'orange',
  },
  [FinanceCostStateEnum.CONFIRMED]: {
    text: <FormattedMessage id="finance.cost.status.confirmed" />,
    color: 'green',
  }
}

// 收支性质枚举
export enum FinanceCostTypeEnum {
  /**
   * 收入
   */
  INCOME = 1,
  /**
   * 支出
   */
  EXPEND = 2,
  /**
   * 应收
   */
  RECEIVABLE = 3,
  /**
   * 应付
   */
  PAYMENT = 4,
}

export const FinanceCostTypeEnumMap = {
  [FinanceCostTypeEnum.INCOME]: {
    text: <FormattedMessage id="finance.cost.type.income" />,
  },
  [FinanceCostTypeEnum.EXPEND]: {
    text: <FormattedMessage id="finance.cost.type.expend" />,
  },
  [FinanceCostTypeEnum.RECEIVABLE]: {
    text: <FormattedMessage id="finance.cost.type.receivable" />,
  },
  [FinanceCostTypeEnum.PAYMENT]: {
    text: <FormattedMessage id="finance.cost.type.payable" />,
  },
}