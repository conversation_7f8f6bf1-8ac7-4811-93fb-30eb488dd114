import { FinanceCostStateEnum, FinanceCostTypeEnum } from "./FinanceCost.enum";

export interface FinanceCostEntity {
  /**
   * 收款账户名称
   */
  accountName?: string;
  /**
   * 金额（分）
   */
  amount?: number;
  /**
   * 金额（元）
   */
  amountYuan?: number;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 台账类型1：收入，2：支出
   */
  ledgerType?: FinanceCostTypeEnum;
  /**
   * None
   */
  memberId?: string;
  /**
   * 收付款对象编码
   */
  objCode?: string;
  /**
   * 收付款对象名称
   */
  objName?: string;
  /**
   * 收付款对象类型
   */
  objType?: number;
  /**
   * 发生时间
   */
  occurTime?: string;
  /**
   * 制单人
   */
  operatorName?: string;
  /**
   * 图片
   */
  pic?: string;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 收支单号
   */
  serialNumber?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 财务属性标签类型
   */
  tagName?: string;
  /**
   * 收支类型
   */
  tagType?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 状态
   */
  status?: FinanceCostStateEnum;
}
