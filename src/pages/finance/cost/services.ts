import { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { FinanceCostStateEnum } from './types/FinanceCost.enum';

export const queryCostFlowPage = (params: Partial<FinanceCostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<FinanceCostEntity>>(`/ipmsaccount/queryCostFlowPage`, {
    data: params,
  });
};

export const createCostFlow = async (params: FinanceCostEntity) => {
  return request<boolean>(`/ipmsaccount/createCostFlow`, {
    data: params,
  });
};

// 审核费用流水：作废、审核不通过时调用
export const auditCostFlow = async (params: {
  id?: string;
  status: FinanceCostStateEnum
}): Promise<boolean> => {
  return request(`/ipmsaccount/auditCostFlow`, {
    data: params,
  });
};

// 收支详情
export const queryCostFlowDetail = async (params: {
  id?: string;
}): Promise<FinanceCostEntity> => {
  return request(`/ipmsaccount/queryCostFlowDetail`, {
    data: params,
  });
};

// 确认收支：审核通过时调用
export const confirmCostFlow = async (params: {
  id?: string;
}): Promise<boolean> => {
  return request(`/ipmsaccount/confirmCostFlow`, {
    data: { ...params, status: FinanceCostStateEnum.CONFIRMED },
  });
};