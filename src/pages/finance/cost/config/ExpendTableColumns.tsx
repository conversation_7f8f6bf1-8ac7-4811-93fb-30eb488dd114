import type { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';
import { queryTagList } from '@/pages/finance/tag/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import {
  FinanceCostStateEnumMap,
  FinanceCostTypeEnum,
  FinanceCostTypeEnumMap,
} from '../types/FinanceCost.enum';

export default (props): ProColumns<FinanceCostEntity>[] => {
  const { intl, handleOpenDrawer } = props;
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      editable: false,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 160,
      search: false,
      render: (text, record) => <a onClick={() => handleOpenDrawer(record)}>{text}</a>,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.occurTime' }),
      dataIndex: 'occurTime',
      width: 160,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendStore' }),
      dataIndex: 'storeName',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.store' }),
      dataIndex: 'storeIdList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendNature' }),
      dataIndex: 'ledgerType',
      width: 140,
      valueEnum: {
        [FinanceCostTypeEnum.EXPEND]: FinanceCostTypeEnumMap[FinanceCostTypeEnum.EXPEND],
        [FinanceCostTypeEnum.PAYMENT]: FinanceCostTypeEnumMap[FinanceCostTypeEnum.PAYMENT],
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendType' }),
      dataIndex: 'tagId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'tagName', value: 'id' },
      },
      // 这里params里面的变量如果有变动，下面会重新发起请求获取属性列表
      params: { tabActiveKey: props?.tabActiveKey },
      request: async (query) => await queryTagList({ ledgerType: 2, ...query }),
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendType' }),
      dataIndex: 'tagName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendObject' }),
      dataIndex: 'objName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.expendAmount' }),
      dataIndex: 'amountYuan',
      valueType: 'money',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.currency' }),
      dataIndex: 'currency',
      width: 80,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.collection.columns.rate' }),
      dataIndex: 'rate',
      width: 80,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.settlementAccount' }),
      dataIndex: 'accountName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.createTime' }),
      dataIndex: 'createTime',
      width: 140,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startBizTime: value[0],
            endBizTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.createTime' }),
      dataIndex: 'createTime',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.creator' }),
      dataIndex: 'createPerson',
      width: 60,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'common.column.remark' }),
      dataIndex: 'remark',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'status',
      width: 80,
      valueEnum: FinanceCostStateEnumMap,
    },
  ] as ProColumns<FinanceCostEntity>[];
};
