import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { queryTagList } from '@/pages/finance/tag/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { compressImage } from '@/utils/fileUtils';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormDigit, ProFormTextArea } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormUploadButton,
} from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useForm } from 'antd/lib/form/Form';
import { useEffect } from 'react';
import { FinanceCostTypeEnum } from '../../types/FinanceCost.enum';

export default ({
  title,
  recordId,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const intl = useIntl();
  const [form] = useForm();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  return (
    <ModalForm
      form={form}
      layout="vertical"
      title={title}
      open={visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
      initialValues={{
        ledgerType: FinanceCostTypeEnum.EXPEND,
      }}
    >
      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="storeId"
        label={intl.formatMessage({ id: 'finance.cost.form.expendStore' })}
        mode="single"
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={async () => {
          const result = await queryStoreByAccount({ status: 1 });
          if (result.length > 0) {
            form.setFieldValue('storeId', result[0].id);
          }
          return result;
        }}
      />

      <ProFormRadio.Group
        name="ledgerType"
        options={[
          {
            label: intl.formatMessage(
              { id: 'finance.cost.recordType' },
              { type: intl.formatMessage({ id: 'finance.cost.type.expend' }) },
            ),
            value: FinanceCostTypeEnum.EXPEND,
          },
          {
            label: intl.formatMessage(
              { id: 'finance.cost.recordType' },
              { type: intl.formatMessage({ id: 'finance.cost.type.payable' }) },
            ),
            value: FinanceCostTypeEnum.PAYMENT,
          },
        ]}
      />

      <ProFormObject
        objects={[ObjectType.Suppler, ObjectType.OtherCompany]}
        form={form}
        fieldsName={{
          fieldType: 'objType',
          fieldName: 'objName',
          fieldId: 'objCode',
        }}
        label={intl.formatMessage({ id: 'finance.cost.columns.expendObject' })}
      />

      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="tagId"
        label={intl.formatMessage({ id: 'finance.cost.form.expendType' })}
        mode="single"
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'tagName', value: 'id' },
        }}
        request={async () => await queryTagList({ ledgerType: 2 })}
      />

      <ProFormCurrency rules={[REQUIRED_RULES]} />

      <ProFormDigit
        min={0.01}
        name="amountYuan"
        label={intl.formatMessage({ id: 'finance.cost.form.expendAmount' })}
        rules={[REQUIRED_RULES]}
        fieldProps={{
          controls: false,
          precision: 2,
        }}
      />

      <ProFormDependency name={['ledgerType']}>
        {({ ledgerType }) => {
          if (ledgerType === FinanceCostTypeEnum.EXPEND) {
            return (
              <ProFormSelect
                rules={[REQUIRED_RULES]}
                name="accountId"
                label={intl.formatMessage({ id: 'finance.cost.form.settlementAccount' })}
                mode="single"
                fieldProps={{
                  showSearch: true,
                }}
                request={async (query) => {
                  const data = await queryMemberAccountPage({ pageSize: 1000 });
                  if (data?.data?.length) {
                    form.setFieldValue('accountId', data?.data[0].id);
                  }
                  return data?.data?.map(({ id, memberAccountName }) => ({
                    value: id,
                    label: memberAccountName,
                  }));
                }}
              />
            );
          }
          return null;
        }}
      </ProFormDependency>

      <ProFormDateTimePicker
        name={'occurTime'}
        label={intl.formatMessage({ id: 'finance.flow.columns.occurTime' })}
      />

      <ProFormUploadButton
        name="pic"
        label={intl.formatMessage({ id: 'finance.cost.form.pic' })}
        action="/apigateway/public/upload/object/batch"
        accept=".jpg,.png,.jpeg,.gif,.webp"
        fieldProps={{
          listType: 'picture-card',
          maxCount: 1,
          beforeUpload: (file) => compressImage(file),
        }}
      />

      <ProFormTextArea
        name="remark"
        label={intl.formatMessage({ id: 'finance.cost.form.expendRemark' })}
      />
    </ModalForm>
  );
};
