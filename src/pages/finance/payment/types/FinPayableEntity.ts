import { ObjectType } from "@/components/ProFormItem/ProFormObject";

export interface FinPayableEntity {
  /**
   * 订单完成日期
   */
  billDate?: string;
  /**
   * 买方id
   */
  buyerId?: string;
  /**
   * 买方名称
   */
  buyerName?: string;

  /**
   * id
   */
  id?: string;
  /**
   * 台账类型
   */
  ledgerType?: number;
  /**
   * 订单金额，单位：分
   */
  orderAmount?: number;
  /**
   * 订单金额，单位：元
   */
  orderAmountYuan?: number;
  /**
   * 业务单号XSDTH
   */
  orderNo?: string;
  /**
   * 已付金额，单位：分
   */
  paymentAmount?: number;
  /**
   * 已付金额，单位：元
   */
  paymentAmountYuan?: number;
  /**
   * 剩余应付金额，单位：分
   */
  remainPayableAmount?: number;
  /**
   * 剩余应付金额，单位：元
   */
  remainPayableAmountYuan?: number;
  /**
   * 卖方id
   */
  sellerId?: string;
  /**
   * 卖方名称
   */
  sellerName?: string;
  sellerType?: ObjectType;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
}
