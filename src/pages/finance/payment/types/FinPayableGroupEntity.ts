import { FinPayableEntity } from "@/pages/finance/payment/types/FinPayableEntity";

export interface FinPayableGroupEntity {
  totalPayableAmount?: string;
  totalPayableAmountYuan?: string;
  payableAmount?: string;
  paymentAmount?: string;
  orderAmount?: string;
  finPayableList?: FinPayableEntity[];
  detailRoList?: FinPayableEntity[];
  totalPayableAmountList?: TotalPayableAmountList[];
}



export interface TotalPayableAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}



export interface FinPayableGroupDetail {
  /**
   * 应付明细
   */
  detailRoList?: DetailRoList[];
  /**
   * 订单总额，单位：分
   */
  orderAmount?: number;
  /**
   * 订单总额，单位：元多币种
   */
  orderAmountList?: OrderAmountList[];
  /**
   * 订单总额，单位：元
   */
  orderAmountYuan?: number;
  /**
   * 应付总额，单位：分
   */
  payableAmount?: number;
  /**
   * 应付总额，单位：元多币种
   */
  payableAmountList?: PayableAmountList[];
  /**
   * 应付总额，单位：元
   */
  payableAmountYuan?: number;
  /**
   * 已付总额，单位：分
   */
  paymentAmount?: number;
  /**
   * 已付总额，单位：元多币种
   */
  paymentAmountList?: PaymentAmountList[];
  /**
   * 已付总额，单位：元
   */
  paymentAmountYuan?: number;
}

export interface DetailRoList {
  /**
   * 订单完成日期
   */
  billDate?: string;
  /**
   * 买方id
   */
  buyerId?: string;
  /**
   * 买方名称
   */
  buyerName?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 台账类型
   */
  ledgerType?: number;
  /**
   * 订单金额，单位：分
   */
  orderAmount?: number;
  /**
   * 订单金额，单位：元
   */
  orderAmountYuan?: number;
  /**
   * 业务单号XSDTH
   */
  orderNo?: string;
  /**
   * 应付类型@seecom.ipms.finance.account.api.dto.enums.PayableTypeEnums
   */
  payableType?: number;
  /**
   * 已付金额，单位：分
   */
  paymentAmount?: number;
  /**
   * 已付金额，单位：元
   */
  paymentAmountYuan?: number;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 剩余应付金额，单位：分
   */
  remainPayableAmount?: number;
  /**
   * 剩余应付金额，单位：元
   */
  remainPayableAmountYuan?: number;
  /**
   * 卖方id
   */
  sellerId?: string;
  /**
   * 卖方名称
   */
  sellerName?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
}

export interface OrderAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface PayableAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface PaymentAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}
