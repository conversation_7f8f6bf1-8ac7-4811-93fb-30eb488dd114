import { FinPayableGroupDetail, FinPayableGroupEntity } from '@/pages/finance/payment/types/FinPayableGroupEntity';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { FinPayableEntity } from './types/FinPayableEntity';

/**
 * 应付分页查询
 *
 * @param params
 * @returns
 */
export const queryPayablePageGroup = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
): Promise<PageResponseDataType<FinPayableGroupEntity>> => {
  return request(
    `/ipmsaccount/queryPayablePageGroup`,
    {
      data: params,
    },
  );
};

/**
 * 应付明细分页查询
 *
 * @param params
 * @returns
 */
export const queryPayableDetailPage = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
): Promise<FinPayableGroupDetail> => {
  return request(
    `/ipmsaccount/queryPayableDetailPage`,
    {
      data: params,
    },
  );
};
