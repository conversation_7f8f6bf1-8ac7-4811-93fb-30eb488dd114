import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import type { FinPayableEntity } from '../types/FinPayableEntity';

export const getDetailColumns = (intl: IntlShape): ProColumns<FinPayableEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.businessOrderNo' }),
    dataIndex: 'orderNo',
  },

  {
    title: intl.formatMessage({ id: 'finance.payment.columns.businessCompleteTime' }),
    dataIndex: 'billDate',
  },
  {
    title: intl.formatMessage({ id: 'common.field.currency' }),
    dataIndex: 'currency',
  },
  {
    title: intl.formatMessage({ id: 'common.field.rate' }),
    dataIndex: 'rate',
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.orderAmount' }),
    dataIndex: 'orderAmountYuan',
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.paidAmount' }),
    dataIndex: 'paymentAmountYuan',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.remainPayableAmount' }),
    dataIndex: 'remainPayableAmountYuan',
    valueType: 'money',
  },
];
