import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getTableColumns = (
  intl: IntlShape,
  {
    form,
  }: {
    form: ProFormInstance;
  },
): ProColumns<FinPayableEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    // 应付对象
    title: intl.formatMessage({ id: 'finance.payment.columns.payableObject' }),
    dataIndex: 'sellerId',
    width: 160,
    ellipsis: true,
    hideInTable: true,
    renderFormItem: (text, record) => {
      return (
        <>
          <ProFormObject
            form={form}
            fieldsName={{
              fieldType: 'sellerType',
              fieldName: 'sellerName',
              fieldId: 'sellerId',
            }}
            objects={[ObjectType.Suppler, ObjectType.OtherCompany]}
          />
        </>
      );
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.payableObject' }),
    dataIndex: 'sellerName',
    width: 160,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
    dataIndex: 'storeName',
    width: 160,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
    dataIndex: 'storeIdList',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },

  {
    title: intl.formatMessage({ id: 'finance.payment.columns.payableAmount' }),
    dataIndex: 'remainPayableAmountList',
    width: 100,
    search: false,
    render: (remainPayableAmountList) => {
      return remainPayableAmountList?.map((item, index) => (
        <span key={item.currency}>
          {item.currencySymbol}
          {item.amount?.toFixed(2)}
          {index !== remainPayableAmountList?.length - 1 ? ';' : ''}
        </span>
      ));
    },
  },
];
