import FunProTable from '@/components/common/FunProTable';
import withKeep<PERSON>live from '@/wrappers/withKeepAlive';
import {
  ActionType,
  PageContainer,
  ProColumns,
} from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { Button, Flex, message, Popconfirm } from 'antd';
import { useRef, useState } from 'react';
import DetailDrawer from './components/DetailDrawer';
import SaveForm from './components/SaveForm';
import { queryOtherRelatedCompanyPage, updateOtherRelatedCompanyStatus } from './service';
import { OtherRelatedCompanyEntity, Status } from './types/OtherRelatedCompany.entity';
import { StatusEnum } from './types/OtherRelatedCompany.enum';

const OtherRelatedPage = () => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [currentId, setCurrentId] = useState<string | undefined>(undefined);
  const { initialState } = useModel('@@initialState');


  const columns: ProColumns<OtherRelatedCompanyEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
    },
    {
      title: t('finance.otherRelated.columns.companyCode'),
      dataIndex: 'companyCode',
      render: (text, record) => <a onClick={() => {
        setCurrentId(record.otherRelatedCompanyInfo?.id);
        setDetailVisible(true);
      }}>{record?.otherRelatedCompanyInfo?.companyCode}</a>,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.companyInfo'),
      dataIndex: 'otherRelatedCompanyFuzzy',
      hideInTable: true,
    },
    {
      title: t('finance.otherRelated.columns.companyName'),
      dataIndex: 'companyName',
      render: (_, record) => record?.otherRelatedCompanyInfo?.companyName,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.shortName'),
      dataIndex: 'shortName',
      render: (_, record) => record?.otherRelatedCompanyInfo?.shortName,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.abn'),
      dataIndex: 'abn',
      render: (_, record) => record?.otherRelatedCompanyInfo?.abn,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.defaultContact'),
      dataIndex: ['otherRelatedCompanyConcatList', '0', 'concatLastName'],
      search: false,
      renderText: (text, record) => {
        return `${record.otherRelatedCompanyConcatList?.[0]?.concatFirstName ?? ''} ${record.otherRelatedCompanyConcatList?.[0]?.concatLastName ?? ''}`;
      }
    },
    {
      title: t('finance.otherRelated.columns.contact'),
      dataIndex: 'concatFullName',
      hideInTable: true,
    },
    {
      title: t('finance.otherRelated.columns.defaultContactPhone'),
      dataIndex: ['otherRelatedCompanyConcatList', '0', 'concatPhone'],
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.contactPhone'),
      dataIndex: 'concatPhone',
      hideInTable: true,
    },
    {
      title: t('finance.otherRelated.columns.defaultAddress'),
      dataIndex: ['otherRelatedCompanyAddressList', '0', 'detailAddress'],
      render: (_, record) => {
        const defaultAddress = record.otherRelatedCompanyAddressList?.[0];
        return defaultAddress ? `${defaultAddress.province || ''} ${defaultAddress.city || ''} ${defaultAddress.area || ''} ${defaultAddress.detailAddress || ''}` : '-';
      },
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.remark'),
      dataIndex: 'remark',
      render: (_, record) => record?.otherRelatedCompanyInfo?.remark,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.status'),
      dataIndex: ['otherRelatedCompanyInfo', 'status'],
      valueEnum: StatusEnum,
      search: false,
    },
    {
      title: t('finance.otherRelated.columns.operation'),
      valueType: 'option',
      render: (_, record) => [
        <a key="edit" onClick={() => {
          setCurrentId(record.otherRelatedCompanyInfo?.id);
          setFormVisible(true);
        }}>{t('common.button.edit')}</a>,
        record.otherRelatedCompanyInfo?.status === String(Status.ENABLE) ?
          <Popconfirm
            key="disable"
            title={t('finance.otherRelated.confirm.disable')}
            onConfirm={async () => {
              const res = await updateOtherRelatedCompanyStatus({ id: record.otherRelatedCompanyInfo!.id!, status: String(Status.DISABLE) });
              if (res) {
                message.success(`${t('common.message.operation.success')}`);
                actionRef.current?.reload();
              }
            }}
          >
            <a>{t('common.button.disable')}</a>
          </Popconfirm> :
          <Popconfirm
            key="enable"
            title={t('finance.otherRelated.confirm.enable')}
            onConfirm={async () => {
              const res = await updateOtherRelatedCompanyStatus({ id: record.otherRelatedCompanyInfo!.id!, status: String(Status.ENABLE) });
              if (res) {
                message.success(`${t('common.message.operation.success')}`);
                actionRef.current?.reload();
              }
            }}
          >
            <a>{t('common.button.enable')}</a>
          </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <FunProTable<OtherRelatedCompanyEntity, any>
        rowKey={(record) => record.otherRelatedCompanyInfo!.id!}
        actionRef={actionRef}
        requestPage={(params) => queryOtherRelatedCompanyPage({ ...params })}
        columns={columns}
        scroll={{ x: 'max-content' }}
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={<Flex justify="space-between" align="flex-end">
          <Button key="add" type="primary" onClick={() => {
            setCurrentId(undefined);
            setFormVisible(true);
          }}>
            {t('common.button.add')}
          </Button>
        </Flex>}
      />
      <DetailDrawer
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
        id={currentId}
      />
      <SaveForm
        open={formVisible}
        onClose={() => setFormVisible(false)}
        id={currentId}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default withKeepAlive(OtherRelatedPage);
