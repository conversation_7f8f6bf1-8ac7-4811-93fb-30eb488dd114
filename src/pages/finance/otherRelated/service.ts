import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { CompanyListItem, OtherRelatedCompanyEntity } from './types/OtherRelatedCompany.entity';
import type { SaveOtherRelatedCompany } from './types/SaveOtherRelatedCompany.request';

/**
 * 保存其他往来单位
 */
export const saveOtherRelatedCompany = async (params: SaveOtherRelatedCompany): Promise<boolean> => {
    return request(`/ipmsaccount/saveOtherRelatedCompany`, {
        data: params,
    });
};


/**
 * 查询其他往来单位列表
 */
export const queryOtherRelatedCompanyPage = async (params: Partial<OtherRelatedCompanyEntity> & PageRequestParamsType): Promise<PageResponseDataType<OtherRelatedCompanyEntity>> => {
    return request(`/ipmsaccount/queryOtherRelatedCompanyPage`, {
        data: params,
    });
};

/**
 * 查询其他往来单位详情
 */
export const queryOtherRelatedCompanyDetail = async (params: { id: string }): Promise<OtherRelatedCompanyEntity> => {
    return request(`/ipmsaccount/getFullById`, {
        data: params,
    });
};

/**
 * 更新其他往来单位状态
 */
export const updateOtherRelatedCompanyStatus = async (params: { id: string, status: string }): Promise<boolean> => {
    return request(`/ipmsaccount/updateOtherRelatedCompanyStatus`, {
        data: params,
    });
};

/**
 * 其他往来单位列表-下拉列表
 */
export const getOtherRelatedCompanyList = (): Promise<CompanyListItem[]> => {
    return request(`/ipmsaccount/queryOtherRelatedCompanyList`, {
        data: {
            status: 1
        }
    });
};