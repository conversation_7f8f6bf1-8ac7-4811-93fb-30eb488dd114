import { OtherRelatedCompanyAddressList, OtherRelatedCompanyConcatList, OtherRelatedCompanyInfo } from "./OtherRelatedCompany.entity";

export interface SaveOtherRelatedCompany {
    /**
     * 扩展备注
     */
    extRemark?: string;
    firstName?: string;
    lastName?: string;
    /**
     * 零售商id
     */
    memberId?: string;
    /**
     * 零售商名称
     */
    memberName?: string;
    /**
     * 操作人名称
     */
    operatorName?: string;
    /**
     * 操作人id
     */
    operatorNo?: string;
    /**
     * 财务其他往来单位地址信息
     */
    otherRelatedCompanyAddressList?: OtherRelatedCompanyAddressList[];
    /**
     * 财务其他往来单位联系人信息
     */
    otherRelatedCompanyConcatList?: OtherRelatedCompanyConcatList[];
    otherRelatedCompanyInfo?: OtherRelatedCompanyInfo;
}


