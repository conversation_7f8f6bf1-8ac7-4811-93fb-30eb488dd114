import SubTitle from "@/components/common/SubTitle";
import { AddressCode } from "@/pages/customer/list/components/CustomerCreateDrawerForm/AddressInfo";
import { requiredProps } from "@/types/validateRules";
import { DrawerForm, EditableProTable, ProCard, ProColumns, ProForm, ProFormText } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { Button, Flex, Form, message, Radio, Space } from "antd";
import { useEffect, useState } from "react";
import { IsDefault, OtherRelatedCompanyAddressList, OtherRelatedCompanyConcatList } from "../types/OtherRelatedCompany.entity";
import { SaveOtherRelatedCompany } from "../types/SaveOtherRelatedCompany.request";
import { queryOtherRelatedCompanyDetail, saveOtherRelatedCompany } from "./../service";

interface SaveFormProps {
  open: boolean;
  onClose: () => void;
  id?: string;
  onSuccess: () => void;
}

const SaveForm = ({ open, onClose, id, onSuccess }: SaveFormProps) => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const [form] = Form.useForm<SaveOtherRelatedCompany>();
  const [contactEditableKeys, setContactEditableRowKeys] = useState<React.Key[]>([]);
  const [addressEditableKeys, setAddressEditableRowKeys] = useState<React.Key[]>([]);


  useEffect(() => {
    if (open) {
      if (id) {
        queryOtherRelatedCompanyDetail({ id }).then(res => {
          const { otherRelatedCompanyConcatList = [], otherRelatedCompanyAddressList = [] } = res;
          form.setFieldsValue({
            ...res,
            otherRelatedCompanyConcatList: otherRelatedCompanyConcatList?.map((item) => ({
              ...item,
              rowId: Number(item.id),
            })),
            otherRelatedCompanyAddressList: otherRelatedCompanyAddressList?.map((item) => ({
              ...item,
              rowId: Number(item.id),
            })),
          });
          setContactEditableRowKeys(res?.otherRelatedCompanyConcatList?.map((item) => item.id));
          setAddressEditableRowKeys(res?.otherRelatedCompanyAddressList?.map((item) => item.id));
        })
      } else {
        form.resetFields();
      }
    }
  }, [open, id, form]);

  const handleFinish = async (values: SaveOtherRelatedCompany) => {
    const contactList = form.getFieldValue('otherRelatedCompanyConcatList');
    const addressList = form.getFieldValue('otherRelatedCompanyAddressList');
    const params = {
      otherRelatedCompanyConcatList: contactList,
      otherRelatedCompanyAddressList: addressList,
      otherRelatedCompanyInfo: {
        ...values.otherRelatedCompanyInfo,
        id,
      }
    }
    const res = await saveOtherRelatedCompany(params);
    if (res) {
      message.success(t('message.save.success'));
      onSuccess();
      onClose();
    }
  }

  const contactColumns: ProColumns<OtherRelatedCompanyConcatList>[] = [
    {
      title: t('finance.otherRelated.detail.contact.isDefault'),
      dataIndex: 'isDefault',
      valueType: 'radio',
      renderFormItem: (_, { recordKey, record }) => {
        return <Radio checked={record?.isDefault == IsDefault.YES} onChange={(e) => {
          if (e.target.checked) {
            const contacts = form.getFieldValue('otherRelatedCompanyConcatList') || [];
            const newContacts = contacts.map((c: any) => ({ ...c, isDefault: c.rowId == recordKey ? IsDefault.YES : IsDefault.NO }));
            form.setFieldsValue({ otherRelatedCompanyConcatList: newContacts });
          }
        }} />
      },
      render: (_, record) => <Radio checked={Boolean(record.isDefault)} />
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{t('finance.otherRelated.form.contact.contactPerson')}</span>
        </Flex>
      ),
      dataIndex: 'concatFirstName',
      width: 100,
      colSpan: 2,
      formItemProps: {
        rules: [{ required: true }],
      }
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{t('finance.otherRelated.form.contact.contactPerson')}</span>
        </Flex>
      ),
      dataIndex: 'concatLastName',
      width: 100,
      colSpan: 0,
      formItemProps: {
        rules: [{ required: true }],
      },
    },
    {
      title: t('finance.otherRelated.form.contact.phone'),
      dataIndex: 'concatPhone',
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: t('finance.otherRelated.form.contact.email'),
      dataIndex: 'email',
    },
    {
      title: t('finance.otherRelated.form.contact.post'),
      dataIndex: 'post',
    },
    {
      title: t('finance.otherRelated.form.contact.remark'),
      dataIndex: 'remark',
    },
    {
      title: t('finance.otherRelated.columns.operation'),
      valueType: 'option',
      render: () => {
        return null;
      },
    },
  ]

  const addressColumns: ProColumns<OtherRelatedCompanyAddressList>[] = [
    {
      title: t('finance.otherRelated.detail.address.isDefault'),
      dataIndex: 'isDefault',
      valueType: 'radio',
      renderFormItem: (_, { recordKey, record }) => {
        return <Radio checked={record?.isDefault === IsDefault.YES} onChange={(e) => {
          if (e.target.checked) {
            const addresses = form.getFieldValue('otherRelatedCompanyAddressList') || [];
            const newAddresses = addresses.map((c: any) => ({ ...c, isDefault: c.rowId == recordKey ? IsDefault.YES : IsDefault.NO }));
            form.setFieldsValue({ otherRelatedCompanyAddressList: newAddresses });
          }
        }} />
      },
      // render: (_, row, index) => (
      //   <ProFormRadio
      //     fieldProps={{
      //       width: '80',
      //       checked: row.isDefault == IsDefault.YES,
      //     }}
      //   />
      // ),
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{t('finance.otherRelated.form.address.postCode')}</span>
        </Flex>
      ),
      dataIndex: 'postCode',
      fieldProps: (form, { rowKey }) => ({
        onChange: () => {
          form.setFieldValue([rowKey, 'areaCodeList'], undefined);
        },
      }),
    },
    {
      title: (
        <Flex align="center">
          <span className="text-[#FF7621]">*</span>
          <span>{t('finance.otherRelated.form.address.state')}/{t('finance.otherRelated.form.address.suburb')}</span>
        </Flex>
      ),
      width: 200,
      dataIndex: 'areaCodeList',
      renderFormItem: (_, { record }) => {
        const { postCode = '' } = record;
        return (
          <AddressCode postCode={postCode} />
        )
      },
      renderText: (text, record) => {
        return <>{record?.province} / {record?.area}</>;
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: t('finance.otherRelated.form.address.detail'),
      dataIndex: 'detailAddress',
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: t('finance.otherRelated.form.contact.contactPerson'),
      dataIndex: 'concatFirstName',
      width: 100,
      colSpan: 2,
    },
    {
      title: t('finance.otherRelated.form.contact.contactPerson'),
      dataIndex: 'concatLastName',
      width: 100,
      colSpan: 0,
    },
    {
      title: t('finance.otherRelated.form.address.phone'),
      dataIndex: 'concatPhone',
    },
    {
      title: t('finance.otherRelated.columns.operation'),
      key: 'operation',
      search: false,
      width: 100,
      valueType: 'option',
      // render: (text, record, _, action) => [
      //   <a
      //     key="editable"
      //     onClick={() => {
      //       action?.startEditable?.(record.id, record);
      //     }}
      //   >
      //     {t('common.button.edit')}
      //   </a>,
      //   <a
      //     key="delete"
      //     onClick={() => {
      //       const addresses = form.getFieldValue('otherRelatedCompanyAddressList') || [];
      //       form?.setFieldsValue({
      //         otherRelatedCompanyAddressList: addresses.filter((item) => item.rowId !== record.rowId),
      //       });
      //     }}
      //   >
      //     {t('common.button.delete')}
      //   </a>,
      // ],
    },
  ]


  return (
    <DrawerForm<SaveOtherRelatedCompany>
      form={form}
      layout='vertical'
      title={id ? t('finance.otherRelated.form.title.edit') : t('finance.otherRelated.form.title.add')}
      open={open}
      width={1200}
      drawerProps={{
        maskClosable: false,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose
      }}
      grid
      onFinish={handleFinish}
      submitter={{
        render: () => (
          <Space>
            <Button onClick={onClose}>{t('common.button.cancel')}</Button>
            <Button type="primary" onClick={() => form.submit()}>{t('common.button.save')}</Button>
          </Space>
        ),
      }}
    >
      <Space direction="vertical" className='p-4 bg-gray-100 gap-4'>
        <ProCard title={<SubTitle text={t('finance.otherRelated.form.basicInfo')} />} className="rounded-lg">
          <ProForm.Group>
            <ProFormText colProps={{ span: 8 }} name={['otherRelatedCompanyInfo', 'companyName']} label={t('finance.otherRelated.form.companyName')} placeholder={t('finance.otherRelated.form.companyNamePlaceholder')} rules={[{ required: true }]} />
            <ProFormText colProps={{ span: 8 }} name={['otherRelatedCompanyInfo', 'companyCode']} label={t('finance.otherRelated.form.companyCode')} placeholder={t('finance.otherRelated.form.companyCodePlaceholder')} />
            <ProFormText colProps={{ span: 8 }} name={['otherRelatedCompanyInfo', 'shortName']} label={t('finance.otherRelated.form.shortName')} />
            <ProFormText colProps={{ span: 8 }} name={['otherRelatedCompanyInfo', 'abn']} label={t('finance.otherRelated.form.abn')} rules={[{ required: true }]} />
            <ProFormText colProps={{ span: 8 }} name={['otherRelatedCompanyInfo', 'remark']} label={t('finance.otherRelated.form.remark')} />
          </ProForm.Group>
        </ProCard>

        <ProCard title={<SubTitle text={t('finance.otherRelated.form.contactInfo')} />} className="rounded-lg">
          <EditableProTable<OtherRelatedCompanyConcatList>
            rowKey="rowId"
            name="otherRelatedCompanyConcatList"
            toolBarRender={false}
            columns={contactColumns}
            recordCreatorProps={{
              creatorButtonText: t('finance.otherRelated.form.button.addContact'),
              newRecordType: 'dataSource',
              record: (index, dataSource) => {
                // console.log(dataSource, dataSource.some((c: any) => c.isDefault === IsDefault.YES));
                return {
                  rowId: Date.now(),
                  isDefault: dataSource.some((c: any) => c.isDefault === IsDefault.YES) ? IsDefault.NO : IsDefault.YES,
                };
              },
            }}
            editable={{
              type: 'multiple',
              editableKeys: contactEditableKeys,
              onChange: setContactEditableRowKeys,
              actionRender: (row, _, dom) => {
                return row.isDefault == IsDefault.YES ? [] : [dom.delete];
              },
            }}
          />
        </ProCard>

        <ProCard title={<SubTitle text={t('finance.otherRelated.form.addressInfo')} />} className="rounded-lg">
          <EditableProTable
            rowKey="rowId"
            name="otherRelatedCompanyAddressList"
            toolBarRender={false}
            columns={addressColumns}
            recordCreatorProps={{
              creatorButtonText: t('finance.otherRelated.form.button.addAddress'),
              newRecordType: 'dataSource',
              record: (index, dataSource) => {
                console.log(dataSource, dataSource.some((c: any) => c.isDefault === IsDefault.YES));
                return {
                  rowId: Date.now(),
                  isDefault: dataSource.some((c: any) => c.isDefault === IsDefault.YES) ? IsDefault.NO : IsDefault.YES,
                };
              },
            }}
            editable={{
              type: 'multiple',
              editableKeys: addressEditableKeys,
              onChange: setAddressEditableRowKeys,
              actionRender: (row, _, dom) => {
                return row.isDefault == IsDefault.YES ? [] : [dom.delete];
              },
            }}
          />
        </ProCard>
      </Space>
    </DrawerForm>
  )
}

export default SaveForm;
