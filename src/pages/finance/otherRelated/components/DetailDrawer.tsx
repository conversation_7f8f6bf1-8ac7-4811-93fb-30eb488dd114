import SubTitle from "@/components/common/SubTitle";
import { ProCard } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { Descriptions, Drawer, Space, Spin, Tag } from "antd";
import { useEffect, useState } from "react";
import { StatusEnum } from "../types/OtherRelatedCompany.enum";
import { queryOtherRelatedCompanyDetail } from "./../service";
import { IsDefault, OtherRelatedCompanyEntity } from "./../types/OtherRelatedCompany.entity";

interface DetailDrawerProps {
  open: boolean;
  onClose: () => void;
  id?: string;
}

const DetailDrawer = ({ open, onClose, id }: DetailDrawerProps) => {
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<OtherRelatedCompanyEntity | undefined>(undefined);

  useEffect(() => {
    if (open && id) {
      setLoading(true);
      queryOtherRelatedCompanyDetail({ id }).then(res => {
        setData(res);
      }).finally(() => {
        setLoading(false);
      })
    }
  }, [open, id]);

  return (
    <Drawer
      title={t('finance.otherRelated.detail.title')}
      width={1200}
      open={open}
      onClose={onClose}
      bodyStyle={{
        backgroundColor: '#efefef',
      }}
    >
      <Spin spinning={loading}>
        {data && <div>
          <ProCard className="rounded-lg mb-4">
            <Descriptions title={<Space>
              <span>
                {data.otherRelatedCompanyInfo?.companyName}
              </span>
              <Tag color={StatusEnum[data.otherRelatedCompanyInfo?.status!]?.color}>
                {StatusEnum[data.otherRelatedCompanyInfo?.status!]?.text}
              </Tag>
            </Space>}
              column={6}>
              <Descriptions.Item label={t('finance.otherRelated.columns.companyCode')}>{data.otherRelatedCompanyInfo?.companyCode}</Descriptions.Item>
              <Descriptions.Item label={t('finance.otherRelated.columns.shortName')}>{data.otherRelatedCompanyInfo?.shortName}</Descriptions.Item>
              <Descriptions.Item label={t('finance.otherRelated.columns.abn')}>{data.otherRelatedCompanyInfo?.abn}</Descriptions.Item>
              <Descriptions.Item label={t('finance.otherRelated.columns.remark')}>{data.otherRelatedCompanyInfo?.remark}</Descriptions.Item>
            </Descriptions>
          </ProCard>

          <ProCard
            className="rounded-lg mb-4"
            title={
              <SubTitle text={t('finance.otherRelated.detail.contactInfo')} />
            }>
            {data.otherRelatedCompanyConcatList?.map((contact, index) => (
              <Descriptions
                className='border-solid border border-black/[0.08] rounded px-4 pt-4 pb-2 mt-2'
                key={index}
                column={4}
                title={<div className="font-normal text-black/[0.85]">
                  <div>
                    {contact.concatFirstName} {contact.concatLastName}
                    {contact.isDefault == IsDefault.YES && <Tag color="blue" className='ml-2'>{t('finance.otherRelated.detail.contact.isDefault')}</Tag>}
                  </div>
                </div>}
              >
                <Descriptions.Item label={t('finance.otherRelated.form.contact.post')}>{contact.post}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.contact.phone')}>{contact.concatPhone}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.contact.email')}>{contact.email}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.contact.remark')}>{contact.remark}</Descriptions.Item>
              </Descriptions>
            ))}
          </ProCard>

          <ProCard title={
            <SubTitle text={t('finance.otherRelated.detail.addressInfo')} />
          }>
            {data.otherRelatedCompanyAddressList?.map((address, index) => (
              <Descriptions
                className='border-solid border border-black/[0.08] rounded px-4 pt-4 pb-2 mt-2'
                key={index}
                column={4}
                title={<div className="font-normal text-black/[0.85]">
                  {t('finance.otherRelated.detail.address.number', { number: index + 1 })}
                  {address.isDefault == IsDefault.YES && (
                    <Tag color="blue" className="ml-2">
                      {t('finance.otherRelated.detail.address.isDefault')}
                    </Tag>
                  )}
                </div>}
              >
                <Descriptions.Item label={t('finance.otherRelated.form.address.postCode')}>{address.postCode}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.address.suburb')}>{address.area}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.address.state')}>{address.province}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.address.detail')}>{address.detailAddress}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.address.contact')}>{`${address.concatFirstName ?? ''} ${address.concatLastName ?? ''}`}</Descriptions.Item>
                <Descriptions.Item label={t('finance.otherRelated.form.address.phone')}>{address.concatPhone}</Descriptions.Item>
              </Descriptions>
            ))}
          </ProCard>
        </div>}
      </Spin>
    </Drawer>
  )
}

export default DetailDrawer;
