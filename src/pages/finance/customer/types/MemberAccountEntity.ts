import type { PageRequestParamsType } from '@/types/PageRequestParamsType';

export interface MemberAccountEntity extends PageRequestParamsType {
  /** 零售商账户账户 */
  memberAccountName: string;
  /** 三方账号，非挂账账户对应的支付宝微信账号，银行卡号，账户类型为现金默认：CASH */
  tripartiteAccount?: string;
  /** 三方账号描述，银行账号填写银行名称 */
  tripartiteAccountDesc?: string;
  /** 所属门店 */
  belongToStore?: string[];
  accountExtends?: { belongToStoreName: string; belongToStoreId: string }[]
  accounExtends?: { belongToStoreName: string; belongToStoreId: string }[];
  /** 账户初始金额 */
  totalAmount?: number;
  /** 账户类型 */
  accountType?: number;
  /** 备注 */
  remark?: string;
  /** 账户ID */
  id: string;
  status: number;
  totalAmountYuan?: number;
  currency?: string;
}
