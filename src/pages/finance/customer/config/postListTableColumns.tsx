import ColumnRender from '@/components/ColumnRender';
import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import type { MemberAccountEntity } from '../types/MemberAccountEntity';
import type { MemberAccountQueryType } from '../types/MemberAccountQueryType';

export interface PostListTableColumnsProps {
  handleDeleteItem: (id: number, status: number) => void;
  handleUpdateItem: (params: MemberAccountQueryType) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<MemberAccountEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'finance.customer.columns.accountName' }),
      dataIndex: 'memberAccountName',
      width: 140,
    },
    {
      // 币种
      title: intl.formatMessage({ id: 'finance.customer.columns.currency' }),
      dataIndex: 'currency',
      width: 140,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.customer.columns.bankName' }),
      dataIndex: 'tripartiteAccountDesc',
      search: false,
      width: 160,
    },
    {
      title: intl.formatMessage({ id: 'finance.customer.columns.bankCardNumber' }),
      dataIndex: 'tripartiteAccount',
      search: false,
      width: 160,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.customer.columns.belongToStore' }),
      dataIndex: 'belongToStore',
      search: false,
      width: 200,
      ellipsis: true,
      render: (_text, entity) => {
        const belongToStoreNames = [];
        if (entity?.accounExtends && Array.isArray(entity.accounExtends)) {
          entity.accounExtends.forEach((accounExtend) => {
            if (accounExtend.belongToStoreName) {
              belongToStoreNames.push(accounExtend.belongToStoreName);
            }
          });
        }
        return ColumnRender.ArrayColumnRender(belongToStoreNames);
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.customer.columns.accountBalance' }),
      dataIndex: 'totalAmountYuan',
      search: false,
      width: 120,
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'common.column.remark' }),
      dataIndex: 'remark',
      width: 120,
      search: false,
      ellipsis: true,
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   hideInSearch: true,
    //   search: false,
    //   width: 80,
    //   valueEnum: CommonStatusValueEnum,
    // },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_text, record: MemberAccountEntity) => {
        const { id, memberAccountName } = record;
        const isDefaultAccount = record.accounExtends?.some(extend => extend.belongToStoreId === '********');
        return !isDefaultAccount && (
          <Space>
            <AuthButton
              isHref
              authority="editFinAccount"
              onClick={() =>
                props.handleUpdateItem({
                  customerId: id,
                  memberAccountName,
                })
              }
            >
              {intl.formatMessage({ id: 'common.button.edit' })}
            </AuthButton>
            <Popconfirm
              title={intl.formatMessage({ id: 'finance.customer.confirmDelete' })}
              onConfirm={() => {
                props.handleDeleteItem(id, 0);
              }}
            >
              <AuthButton isHref authority="deleteFinAccount">
                {intl.formatMessage({ id: 'common.button.delete' })}
              </AuthButton>
            </Popconfirm>
          </Space>
        );
      },
    },
  ] as ProColumns<MemberAccountEntity>[];
};
