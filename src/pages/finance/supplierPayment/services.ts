import { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import { PaymentConfirmation } from '@/pages/finance/supplierPayment/types/PaymentConfirmation';
import { PaymentFlowDetailEntity } from '@/pages/finance/supplierPayment/types/PaymentFlowDetailEntity';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';

/**
 * 付款分页查询
 *
 * @param params
 * @returns
 */
export const queryPaymentPage = async (
  params: Partial<FinPaymentEntity> & PageRequestParamsType,
): Promise<PageResponseDataType<FinPaymentEntity>> => {
  return request(`/ipmsaccount/queryPaymentPage`, {
    data: params,
  });
};
/**
 * 付款流水
 *
 * @param params
 * @returns
 */
export const queryPaymentFlowList = async (params: string) => {
  return request<PageResponseDataType<PaymentFlowDetailEntity>>(
    `/ipmsaccount/queryPaymentFlowList`,
    {
      data: { paymentSerialNumber: params },
    },
  );
};

/**
 * 应付列表
 *
 * @param params
 * @returns
 */
export const queryPayableList = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
): Promise<PageResponseDataType<FinPayableEntity>> => {
  return request(`/ipmsaccount/queryPayableList`, {
    data: params,
  });
};

/**
 * 实付
 *
 * @param params
 * @returns
 */
export const paymentConfirmation = async (params: PaymentConfirmation) => {
  return request<PageResponseDataType<string>>(`/ipmsaccount/paymentConfirmation`, {
    data: params,
  });
};


// 应付总额
export interface TotalPayableEntity {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}
export const queryTotalPayable = async (params: { sellerId: string }): Promise<TotalPayableEntity[]> => {
  return request(`/ipmsaccount/queryTotalPayable`, {
    data: params,
  });
};