import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryPaymentPage } from '@/pages/finance/supplierPayment/services';
import type { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import type { PaymentDetailModalType } from '@/pages/finance/supplierPayment/types/PaymentDetailModalType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { Form, Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';

const FinSupplierPayment = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<PaymentDetailModalType>({
    visible: false,
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: FinPaymentEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.supplierPayment.detailTitle' }),
      visible: true,
      serialNumber: record.serialNumber,
    }));
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      serialNumber: undefined,
    }));
  };

  const preColumns: ProColumns<FinPaymentEntity>[] = [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'finance.supplierPayment.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 120,
      search: false,
      render: (text, record) => <a onClick={() => openDetailDrawer(record)}>{text}</a>,
    },
  ];

  return (
    <PageContainer>
      <FunProTable<FinPaymentEntity, any>
        requestPage={queryPaymentPage}
        options={{ setting: true, density: false, reload: false }}
        actionRef={actionRef}
        form={form}
        scroll={{ x: 'max-content' }}
        columns={[...preColumns, ...getTableColumns(intl, form)]}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              authority="addPayment"
              onClick={() => {
                history.push('/finance/supplierPayment/add');
              }}
            >
              {intl.formatMessage({ id: 'finance.supplierPayment.add' })}
            </AuthButton>
          </Space>
        }
      />
      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
    </PageContainer>
  );
};

export default withKeepAlive(FinSupplierPayment);