export interface FinPaymentEntity {
  /**
   * 付款时间
   */
  businessTime?: string;
  /**
   * 客户ID(门店id)
   */
  buyerId?: string;
  /**
   * 客户名称
   */
  buyerName?: string;
  /**
   * 制单人
   */
  createPerson?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 付款类型，1：收入，2：支出
   */
  ledgerType?: number;
  /**
   * 付款账户ID
   */
  paymentAccountId?: string;
  /**
   * 付款账户名称
   */
  paymentAccountName?: string;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 付款备注
   */
  remark?: string;
  /**
   * 供应商ID
   */
  sellerId?: string;
  /**
   * 供应商名称
   */
  sellerName?: string;
  /**
   * 付款单号
   */
  serialNumber?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 付款金额，单位：分
   */
  totalPaymentAmount?: number;
  /**
   * 付款金额，单位：元
   */
  totalPaymentAmountYuan?: number;
}
