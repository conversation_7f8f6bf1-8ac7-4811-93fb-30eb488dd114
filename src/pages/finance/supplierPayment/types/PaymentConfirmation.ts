import { PaymentConfirmationOrderDetail } from "@/pages/finance/supplierPayment/types/PaymentConfirmationOrderDetail";
export interface PaymentConfirmation {
  /**
   * 业务入账时间
   */
  businessTime?: string;
  /**
   * 买方id
   */
  buyerId?: string;
  /**
   * 买方名称
   */
  buyerName?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * None
   */
  extRemark?: string;
  /**
   * 实付流水
   */
  finPaymentOrderDetailCmdList?: PaymentConfirmationOrderDetail[];
  firstName?: string;
  lastName?: string;
  /**
   * 台账类型1是收入2是支出@seecom.ipms.finance.account.api.dto.enums.AccountFlowLedgerTypeEnums
   */
  ledgerType?: number;
  lossAmount?: number;
  /**
   * 本货币汇率损益(元)
   */
  lossAmountYuan?: number;
  /**
   * 零售商账户名称
   */
  memberAccountName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 付款账户ID
   */
  paymentAccountId?: string;
  /**
   * 付款账户名称
   */
  paymentAccountName?: string;
  /**
   * 付款门店id
   */
  paymentStoreId?: string;
  /**
   * 付款门店名称
   */
  paymentStoreName?: string;
  /**
   * 付款图片
   */
  pic?: string;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 卖方id
   */
  sellerId?: string;
  /**
   * 卖方名称
   */
  sellerName?: string;
  /**
   * 付款对象类型(1：供应商，2：其他往来单位)
   */
  sellerType?: number;
  /**
   * 实付总金额，单位：分
   */
  totalPaymentAmount?: number;
  /**
   * 实付总金额，单位：元
   */
  totalPaymentAmountYuan?: number;
}

