import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import type { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/personnel/user/services';
import type { FormInstance, ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getTableColumns = (
  intl: IntlShape,
  form: FormInstance,
): ProColumns<FinPaymentEntity>[] => [
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentStore' }),
    dataIndex: 'buyerName',
    width: 120,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    formItemProps: {
      name: 'paymentStoreList',
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentTime' }),
    dataIndex: 'businessTimeQuery',
    width: 140,
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value: any) => {
        return {
          startBusinessTime: value[0],
          endBusinessTime: value[1],
        };
      },
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentTime' }),
    dataIndex: 'businessTime',
    search: false,
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
    dataIndex: 'sellerName',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
    dataIndex: 'sellerId',
    width: 120,
    hideInTable: true,
    renderFormItem: () => {
      return (
        <>
          <ProFormObject
            objects={[ObjectType.Suppler, ObjectType.OtherCompany]}
            fieldsName={{
              fieldType: 'sellerType',
              fieldName: 'sellerName',
              fieldId: 'sellerId',
            }}
            form={form}
          />
        </>
      );
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAccount' }),
    dataIndex: 'paymentAccountName',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'common.field.currency' }),
    dataIndex: 'currency',
    width: 80,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'common.field.rate' }),
    dataIndex: 'rate',
    width: 80,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAmount' }),
    dataIndex: 'totalPaymentAmountYuan',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.creator' }),
    dataIndex: 'createPerson',
    width: 60,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.creator' }),
    dataIndex: 'createPerson',
    key: 'createPerson',
    search: true,
    width: 60,
    valueType: 'select',
    hideInTable: true,
    fieldProps: {
      showSearch: true,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: (query: any) => {
      return accountListQuerySimple({ name: query.keyWords });
    },
  },
  {
    title: intl.formatMessage({ id: 'common.column.remark' }),
    dataIndex: 'remark',
    search: false,
    width: 100,
  },
];
