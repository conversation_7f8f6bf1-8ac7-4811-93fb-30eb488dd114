import type { FinPaymentFlowEntity } from '@/pages/finance/supplierPayment/types/FinPaymentFlowEntity';
import type { ProColumns } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';

export const DetailColumns: ProColumns<FinPaymentFlowEntity>[] = [
  {
    title: <FormattedMessage id="common.column.index" />,
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },
  {
    title: <FormattedMessage id="finance.supplierPayment.detail.columns.storeName" />,
    dataIndex: 'storeName',
    fixed: 'left',
    width: 100,
  },
  {
    title: <FormattedMessage id="finance.supplierPayment.detail.columns.orderNo" />,
    dataIndex: 'orderNo',
    width: 100,
  },
  {
    title: <FormattedMessage id="finance.supplierPayment.detail.columns.billDate" />,
    dataIndex: 'billDate',
    width: 140,
  },
  {
    title: <FormattedMessage id="common.field.currency" />,
    dataIndex: 'currency',
    width: 80,
  },
  {
    title: <FormattedMessage id="common.field.rate" />,
    dataIndex: 'rate',
    width: 80,
  },
  {
    title: <FormattedMessage id="finance.supplierPayment.detail.columns.orderAmount" />,
    dataIndex: 'orderAmountYuan',
    width: 100,
    valueType: 'money',
  },
  {
    title: <FormattedMessage id="finance.supplierPayment.detail.columns.paymentAmount" />,
    dataIndex: 'paymentAmountYuan',
    width: 100,
    valueType: 'money',
  },
];
