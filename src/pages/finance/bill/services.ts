import { PageRequestParamsType } from "@/types/PageRequestParamsType";
import { PageResponseDataType } from "@/types/PageResponseDataType";
import { request } from "@/utils/request";
import { BillDetailEntity, BillEntity } from "./types/bill.entity";


/**
 * 分页查询账单
 */
export function queryBillPage(params: Partial<BillEntity> & PageRequestParamsType): Promise<PageResponseDataType<BillEntity>> {
    return request('/ipmsaccount/queryBillPage', {
        data: params
    })
}

// 账单详情
export function queryBillDetailPage(params: PageRequestParamsType & { id: string }): Promise<PageResponseDataType<BillDetailEntity>> {
    return request('/ipmsaccount/queryBillDetailPage', {
        data: params
    })
}