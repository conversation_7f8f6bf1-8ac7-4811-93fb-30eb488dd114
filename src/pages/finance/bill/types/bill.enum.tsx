import { FormattedMessage } from '@umijs/max';

export enum BillStatus {
    /**
     * 已出账
     */
    BILLED = 0,
    /**
     * 已逾期
     */
    OVERDUE = 1,
    /**
     * 已结清
     */
    CLEARED = 2,
}


export const billStatusOptions = {
    [BillStatus.BILLED]: { text: <FormattedMessage id="finance.bill.status.billed" />, status: 'Default', color: 'orange' },
    [BillStatus.OVERDUE]: { text: <FormattedMessage id="finance.bill.status.overdue" />, status: 'Error', color: 'red' },
    [BillStatus.CLEARED]: { text: <FormattedMessage id="finance.bill.status.cleared" />, status: 'Success', color: 'green' },
};