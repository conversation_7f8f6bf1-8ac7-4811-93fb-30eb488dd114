import { OrderAmountList, ReceivableList, ReceivedAmountList } from "../../collection/types/FinReceivableGroupEntity.entity";
import { BillStatus } from "./bill.enum";

export interface BillEntity {
    /**
     * 关联账户ID
     */
    accountId?: number;
    /**
     * 账单日
     */
    billDate?: string;
    /**
     * 账单编号
     */
    billNo?: string;
    /**
     * 已收金额，单位：元多币种
     */
    billReceivedAmountList?: BillReceivedAmountList[];
    /**
     * 应收金额，单位：元多币种
     */
    billRemainAmountList?: BillRemainAmountList[];
    /**
     * 账单金额，单位：元多币种
     */
    billTotalAmountList?: BillTotalAmountList[];
    createPerson?: string;
    createTime?: string;
    /**
     * 客户ID
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 周期类型:WEEKLYMONTHLY
     */
    cycleType?: string;
    /**
     * 账单结束日
     */
    endDate?: string;
    /**
     * id,
     */
    id?: string;
    isDelete?: number;
    memberId?: string;
    /**
     * 超期日
     */
    overdueDate?: string;
    /**
     * 支付状态:0:UNPAID,1:PARTI_PAID,2:PAID
     */
    paymentStatus?: number;
    /**
     * 剩余额度，单位：分
     */
    remainAmount?: number;
    /**
     * 剩余额度，单位：元
     */
    remainAmountYuan?: number;
    /**
     * 账单开始日
     */
    startDate?: string;
    /**
     * 逾期状态:0:BILLED,1:OVERDUE,2:CLEARED
     */
    status?: BillStatus;
    /**
     * 归属门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    updatePerson?: string;
    updateTime?: string;
}

export interface BillReceivedAmountList {
    /**
     * 币种金额(元)
     */
    amount?: number;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
}

export interface BillRemainAmountList {
    /**
     * 币种金额(元)
     */
    amount?: number;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
}

export interface BillTotalAmountList {
    /**
     * 币种金额(元)
     */
    amount?: number;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
}



export interface BillDetailEntity {
    /**
     * 账单信息
     */
    billRo?: BillEntity;
    /**
  * 联系人地址
  */
    contactAdress?: string;
    /**
     * 联系人名称
     */
    contactName?: string;
    /**
     * 联系人手机
     */
    contactPhone?: string;
    /**
     * 应收账单明细
     */
    finBillDetailList?: FinBillDetailList[];
    /**
     * 订单总额(多币种)
     */
    orderAmountList?: OrderAmountList[];
    /**
     * 应收总额(多币种)
     */
    receivableList?: ReceivableList[];
    /**
     * 已收总额(多币种)
     */
    receivedAmountList?: ReceivedAmountList[];
}


export interface FinBillDetailList {
    /**
     * 订单完成日期
     */
    billDate?: string;
    /**
     * 买方ID
     */
    buyerId?: string;
    /**
     * 买方名称
     */
    buyerName?: string;
    /**
     * 币种
     */
    currency?: string;
    /**
     * 币种符号
     */
    currencySymbol?: string;
    /**
     * id
     */
    id?: string;
    /**
     * 台账类型
     */
    ledgerType?: number;
    /**
     * 订单金额，单位：分
     */
    orderAmount?: number;
    /**
     * 订单金额，单位：元
     */
    orderAmountYuan?: number;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 付款方式
     */
    payType?: number;
    /**
     * 汇率
     */
    rate?: number;
    /**
     * 已收金额，单位：分
     */
    receivedAmount?: number;
    /**
     * 已收金额，单位：元
     */
    receivedAmountYuan?: number;
    /**
     * 应收类型
     */
    receiveType?: string;
    /**
     * 剩余应收金额，单位：分
     */
    remainReceivableAmount?: number;
    /**
     * 剩余应收金额，单位：元
     */
    remainReceivableAmountYuan?: number;
    /**
     * 卖方ID
     */
    sellerId?: string;
    /**
     * 卖方名称
     */
    sellerName?: string;
    /**
     * 逾期状态
     */
    status?: number;
    /**
     * 销售门店id
     */
    storeId?: string;
    /**
     * 销售门店名称
     */
    storeName?: string;
    /**
     * 标签
     */
    tag?: string;
}
