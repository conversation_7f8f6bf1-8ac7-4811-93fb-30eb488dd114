import FunProTable from '@/components/common/FunProTable';
import { TimeFormat, TimeRangeFormat } from '@/components/common/TimeFormat';
import { getCstList } from '@/pages/customer/list/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { ProColumns } from '@ant-design/pro-table';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import BillDetailDrawer from './components/BillDetailDrawer';
import { queryBillPage } from './services';
import { BillEntity } from './types/bill.entity';
import { billStatusOptions } from './types/bill.enum';

const BillListPage = () => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const actionRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [bill, setBill] = useState<BillEntity>();

  const columns: ProColumns<BillEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 40,
    },
    {
      title: t('finance.bill.columns.billNo'),
      dataIndex: 'billNo',
      key: 'billNo',
      width: 150,
      search: false,
      render: (_, record) => {
        return (
          <a
            onClick={() => {
              setOpen(true);
              setBill(record);
            }}
          >
            {record.billNo}
          </a>
        );
      },
    },
    {
      title: t('finance.bill.columns.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
      },
      request: async (query) => {
        const data = await getCstList({ keyword: query.keyWords });
        return data?.map(({ cstId, cstName }) => ({
          value: cstId,
          label: cstName,
        }));
      },
      width: 150,
      formItemProps: {
        name: 'customerIdList',
      },
    },
    {
      title: t('finance.bill.columns.storeName'),
      dataIndex: 'storeName',
      key: 'storeName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
      width: 150,
      formItemProps: {
        name: 'storeIdList',
      },
    },
    {
      title: t('finance.bill.columns.billCycle'),
      dataIndex: 'billCycle',
      key: 'billCycle',
      width: 200,
      render: (_, record) => (
        <TimeRangeFormat startTime={record.startDate} endTime={record.endDate} />
      ),
      search: false,
    },
    {
      title: t('finance.bill.columns.cycleType'),
      dataIndex: 'cycleType',
      key: 'cycleType',
      width: 100,
      search: false,
    },
    {
      title: t('finance.bill.columns.billTotalAmount'),
      dataIndex: 'billTotalAmountList',
      key: 'billTotalAmountList',
      width: 100,
      render: (_, record) => {
        return record.billTotalAmountList?.map((item, index) => {
          return (
            <span key={item.currency}>
              {`${item.currencySymbol}${item.amount}`}
              {index !== (record?.billTotalAmountList ?? []).length - 1 && ';'}
            </span>
          );
        });
      },
      search: false,
    },
    {
      title: t('finance.bill.columns.billReceivedAmount'),
      dataIndex: 'billReceivedAmountList',
      key: 'billReceivedAmountList',
      width: 100,
      render: (_, record) => {
        return record.billReceivedAmountList?.map((item, index) => {
          return (
            <span key={item.currency}>
              {`${item.currencySymbol}${item.amount}`}
              {index !== (record?.billReceivedAmountList ?? []).length - 1 && ';'}
            </span>
          );
        });
      },
      search: false,
    },
    {
      title: t('finance.bill.columns.remainAmount'),
      dataIndex: 'remainAmountYuan',
      key: 'remainAmountYuan',
      width: 100,
      search: false,
    },
    {
      title: t('finance.bill.columns.billDate'),
      dataIndex: 'billDate',
      key: 'billDate',
      width: 100,
      search: false,
      render: (text) => <TimeFormat time={text} />,
    },
    {
      title: t('finance.bill.columns.overdueDate'),
      dataIndex: 'overdueDate',
      key: 'overdueDate',
      width: 100,
      search: false,
      render: (text) => <TimeFormat time={text} />,
    },
    {
      title: t('finance.bill.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueEnum: billStatusOptions,
    },
    // {
    //   title: t('common.column.action'),
    //   key: 'action',
    //   width: 100,
    //   render: (_, record) => {
    //     return (
    //       <Space>
    //         <AuthButton authority='printBill' type="link">{t('common.button.print')}</AuthButton>
    //         <AuthButton authority='sendBill' type="link">{t('common.button.sendEmail')}</AuthButton>
    //       </Space>
    //     );
    //   },
    // },
  ];

  return (
    <PageContainer>
      <FunProTable<BillEntity, any>
        rowKey="id"
        requestPage={queryBillPage}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={columns}
      />
      <BillDetailDrawer open={open} onClose={() => setOpen(false)} bill={bill} />
    </PageContainer>
  );
};

export default withKeepAlive(BillListPage);
