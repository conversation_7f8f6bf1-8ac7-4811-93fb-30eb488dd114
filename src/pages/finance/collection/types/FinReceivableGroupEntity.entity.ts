import { FinReceivableEntity } from "@/pages/finance/collection/types/FinReceivableEntity.entity";

export interface FinReceivableGroupEntity {
  finReceivableRoList?: FinReceivableEntity[]
  detailRoList?: FinReceivableEntity[]
  totalReceivableAmount?: string;
  totalReceivableAmountYuan?: string;
  totalOverdueAmount?: string;
  orderAmountList?: OrderAmountList[];
  overdueAmountList?: OverdueAmountList[];
  receivableList?: ReceivableList[];
  receivedAmountList?: ReceivedAmountList[];
}


export interface OrderAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface OverdueAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface ReceivableList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface ReceivedAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}