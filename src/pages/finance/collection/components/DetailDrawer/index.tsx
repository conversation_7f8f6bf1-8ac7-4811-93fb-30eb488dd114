import LeftTitle from '@/components/LeftTitle';
import { ObjectType } from '@/components/ProFormItem/ProFormObject';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { getCstDetail } from "@/pages/customer/list/services";
import { queryReceivableDetailPage } from "@/pages/finance/collection/services";
import type { FinReceivableDetailModalType } from "@/pages/finance/collection/types/FinReceivableDetailModalType";
import type { FinReceivableEntity } from "@/pages/finance/collection/types/FinReceivableEntity.entity";
import { queryOtherRelatedCompanyDetail } from '@/pages/finance/otherRelated/service';
import { exportData } from "@/utils/exportData";
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import type { ActionType } from "@ant-design/pro-table/lib";
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex, Space } from 'antd';
import { useRef, useState } from "react";
import { getDetailColumns } from '../../config/DetailColumns';
import { OrderAmountList, ReceivableList } from '../../types/FinReceivableGroupEntity.entity';
export default (props: FinReceivableDetailModalType) => {
  const intl = useIntl();
  const [orderAmount, setOrderAmount] = useState<OrderAmountList[]>([]);
  const [receivableAmount, setReceivableAmount] = useState<ReceivableList[]>([]);
  const [overdueAmount, setOverdueAmount] = useState<OrderAmountList[]>([]);
  const [receivedAmount, setReceivedAmount] = useState<ReceivableList[]>([]);
  const [cstName, setCstName] = useState<string>("0");
  const [receivableFlag, setReceivableFlag] = useState(false);

  const actionRef = useRef<ActionType>();
  useAsyncEffect(async () => {
    actionRef.current?.reload(true);
  }, [receivableFlag]);

  useAsyncEffect(async () => {
    await setReceivableFlag(false);
  }, [props.visible]);

  /**
   * 分页查询数据
   */
  const queryReceivableDetailPageQuery = async ({ current = 1, pageSize = 10 }) => {
    if (!props.record) {
      return;
    }
    const queryResult = await queryReceivableDetailPage({
      ...props.record,
      pageNo: current,
      pageSize,
      storeIdList: props.record.storeId ? [props.record.storeId] : [],
      receivableFlag: receivableFlag ? 1 : 0
    });
    if (!queryResult || !queryResult.data) {
      setReceivableAmount([]);
      setOverdueAmount([]);
      setOrderAmount([]);
      setReceivedAmount([]);
      return [];
    }
    setReceivableAmount(queryResult.data[0].receivableList);
    setOverdueAmount(queryResult.data[0].overdueAmountList);
    setOrderAmount(queryResult.data[0].orderAmountList);
    setReceivedAmount(queryResult.data[0].receivedAmountList);
    return { ...queryResult, data: queryResult.data[0].detailRoList };
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={false}
    >
      <ProCard className="mb-4">
        <ProDescriptions
          key={props.record?.buyerId + props.record?.storeName}
          title={cstName}
          column={3}
          request={async () => {
            if (!props.record?.buyerId) {
              return null;
            }
            if (props.record?.buyerType === ObjectType.Customer) {
              const cstDetail = await getCstDetail({ cstId: props.record?.buyerId || '' });
              setCstName(cstDetail?.base?.cstName);
              let defaultContacts = cstDetail?.contacts?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultContacts) {
                defaultContacts = cstDetail?.contacts?.[0];
              }
              let defaultAddress = cstDetail?.addresses?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultAddress) {
                defaultAddress = cstDetail?.addresses?.[0];
              }

              return Promise.resolve({
                success: true,
                data: {
                  storeName: props.record?.storeName,
                  cstSn: cstDetail?.base?.cstSn,
                  contactsName: `${defaultContacts?.name ?? ''}`,
                  contactsPhone: `${defaultContacts?.phone ?? ''}`,
                  contactsAddress: `${defaultAddress?.provinceName ?? ''}${defaultAddress?.cityName ?? ''}${defaultAddress?.prefectureName ?? ''}${defaultAddress?.address ?? ''}`.trim(),
                },
              });
            }

            if (props.record?.buyerType === ObjectType.OtherCompany) {
              const detail = await queryOtherRelatedCompanyDetail({ id: props.record?.buyerId || '' })
              setCstName(detail?.otherRelatedCompanyInfo?.companyName || '');
              let defaultContacts = detail?.otherRelatedCompanyConcatList?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultContacts) {
                defaultContacts = detail?.otherRelatedCompanyConcatList?.[0];
              }
              let defaultAddress = detail?.otherRelatedCompanyAddressList?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultAddress) {
                defaultAddress = detail?.otherRelatedCompanyAddressList?.[0];
              }

              return Promise.resolve({
                success: true,
                data: {
                  storeName: props.record?.storeName,
                  cstSn: detail?.otherRelatedCompanyInfo?.companyCode,
                  contactsName: `${defaultContacts?.concatFirstName ?? ''} ${defaultContacts?.concatLastName ?? ''}`,
                  contactsPhone: `${defaultContacts?.concatPhone ?? ''}`,
                  contactsAddress: `${defaultAddress?.province ?? ''}${defaultAddress?.area ?? ''}${defaultAddress?.detailAddress ?? ''}`.trim(),
                },
              });
            }
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.collection.columns.store' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'finance.collection.columns.customerCode' }),
              dataIndex: 'cstSn',
            },
            {
              title: intl.formatMessage({ id: 'finance.collection.columns.contact' }),
              dataIndex: 'contactsName',
            },
            {
              title: intl.formatMessage({ id: 'finance.collection.columns.contactPhone' }),
              dataIndex: 'contactsPhone',
            },
            {
              title: intl.formatMessage({ id: 'finance.collection.columns.contactAddress' }),
              dataIndex: 'contactsAddress',
            },
          ]}
        />
      </ProCard>
      <FunProTable<FinReceivableEntity, any>
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        key={props.record?.buyerId + props.record?.storeId + props.record?.status}
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <Space direction="vertical" size={16}>
              <LeftTitle title={intl.formatMessage({ id: 'finance.collection.detailTitle' })} />
              <AuthButton authority="exportCollectionDetail" className='button-outline' onClick={() => {
                exportData({
                  systemId: "GRIPX_STORE_SYS",
                  taskDesc: intl.formatMessage({ id: 'finance.collection.exportDetailDescription' }),
                  moduleId: "RECEIVABLE_DETAIL_EXPORT",
                  params: { ...props.record, receivableFlag: receivableFlag ? 1 : 0, storeIdList: props.record?.storeId ? [props.record.storeId] : [] },
                });
                props.onCancel?.();
              }}>
                {intl.formatMessage({ id: 'common.button.export' })}
              </AuthButton>
            </Space>
            <Checkbox onChange={(e) => setReceivableFlag(e.target.checked)}>
              {intl.formatMessage({ id: 'finance.collection.onlyShowRemaining' })}
            </Checkbox>
          </Flex>
        )}
        rowKey="id"
        search={false}
        options={false}
        request={queryReceivableDetailPageQuery}
        columns={getDetailColumns(intl)}
      />
      <ProCard bordered>
        <div className='flex justify-end'>
          <div className='w-[450px] leading-8'>
            <div className="text-[16px] font-semibold text-[#000000D9] flex justify-between">
              <span>{intl.formatMessage({ id: 'finance.collection.summary.orderTotal' })}:</span>
              <span>{
                orderAmount.map((item, index) => (
                  <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (orderAmount.length - 1) ? "" : ";"} </>
                ))
              }</span>
            </div>
            <div className="text-[16px] font-semibold text-[#000000D9] flex justify-between">
              <span>{intl.formatMessage({ id: 'finance.collection.summary.receivedTotal' })}:</span>
              <span>{
                receivedAmount.map((item, index) => (
                  <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (receivedAmount.length - 1) ? "" : ";"} </>
                ))
              }</span>
            </div>
            <div className="flex justify-between align-center">
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {intl.formatMessage({ id: 'finance.collection.summary.receivableTotal' })}:</span>
              <span className="text-[24px] font-medium text-primary">
                {
                  receivableAmount.map((item, index) => (
                    <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (receivableAmount.length - 1) ? "" : ";"} </>
                  ))
                }
              </span>
            </div>
            <div className="flex justify-between  align-center">
              <span className="text-[16px] font-semibold text-[#000000D9]">{
                intl.formatMessage({ id: 'finance.collection.summary.overdueTotal' })
              }:</span>
              <span className="text-[24px] font-medium text-primary">
                {
                  Boolean(overdueAmount.length) ? overdueAmount.map((item, index) => (
                    <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (overdueAmount.length - 1) ? "" : ";"}  </>
                  )) : '-'
                }
              </span>
            </div>
          </div>
        </div>
      </ProCard>
    </DrawerForm>
  );
};
