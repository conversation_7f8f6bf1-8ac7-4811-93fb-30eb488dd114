import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { FinReceivableListEntity, type FinReceivableEntity } from './types/FinReceivableEntity.entity';
import { FinReceivableGroupEntity } from './types/FinReceivableGroupEntity.entity';

/**
 * 应收分页查询
 *
 * @param params
 * @returns
 */
export const queryFinReceivablePage = async (
  params: Partial<FinReceivableEntity> & PageRequestParamsType,
): Promise<PageResponseDataType<FinReceivableListEntity>> => {
  return request(
    `/ipmsaccount/queryReceivablePageGroup`,
    {
      data: params,
    },
  );
};
/**
 * 应收明细查询
 *
 * @param params
 * @returns
 */
export const queryReceivableDetailPage = async (
  params: Partial<FinReceivableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinReceivableGroupEntity>>(
    `/ipmsaccount/queryReceivableDetailPage`,
    {
      data: params,
    },
  );
};
/**
 * 应收明细查询
 *
 * @param params
 * @returns
 */
export const queryReceivableList = async (
  params: PageRequestParamsType & { buyerId?: string, receivableFlag?: number },
): Promise<FinReceivableEntity[]> => {
  return request(`/ipmsaccount/queryReceivableList`, {
    data: params,
  });
};
