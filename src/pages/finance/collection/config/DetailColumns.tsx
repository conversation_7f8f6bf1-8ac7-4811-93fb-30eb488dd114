import type { FinReceivableEntity } from "@/pages/finance/collection/types/FinReceivableEntity.entity";
import { OverdueStatusValueEnum } from '@/types/CommonStatus';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getDetailColumns = (intl: IntlShape): ProColumns<FinReceivableEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.businessOrderNo' }),
    dataIndex: 'orderNo',
    fixed: 'left',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.businessCompleteTime' }),
    dataIndex: 'billDate',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.currency' }),
    dataIndex: 'currency',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.rate' }),
    dataIndex: 'rate',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.orderAmount' }),
    dataIndex: 'orderAmountYuan',
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.receivedAmount' }),
    dataIndex: 'receivedAmountYuan',
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.remainReceivableAmount' }),
    dataIndex: 'remainReceivableAmountYuan',
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.overdueStatus' }),
    width: 100,
    dataIndex: 'status',
    valueType: 'select',
    valueEnum: OverdueStatusValueEnum,
  },
];
