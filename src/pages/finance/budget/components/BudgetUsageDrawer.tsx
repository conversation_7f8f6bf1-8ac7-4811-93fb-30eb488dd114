import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Drawer, Table, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { queryBudgetDetail } from '../services';
import { BudgetDetailEntity, BudgetEntity, ItemList } from '../types/budget.entity';
import { CycleType } from '../types/budget.enum';

const { Title } = Typography;

interface BudgetUsageDrawerProps {
  open: boolean;
  onClose: () => void;
  budget?: BudgetEntity | null;
}

const BudgetUsageDrawer: React.FC<BudgetUsageDrawerProps> = ({
  open,
  onClose,
  budget,
}) => {
  const intl = useIntl();
  const [loading, setLoading] = useState(false);
  const [budgetDetail, setBudgetDetail] = useState<BudgetDetailEntity | null>(null);

  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);

  useEffect(() => {
    if (open && budget?.id) {
      fetchBudgetDetail();
    }
  }, [open, budget]);

  const fetchBudgetDetail = async () => {
    if (!budget?.id) return;
    
    setLoading(true);
    try {
      const result = await queryBudgetDetail({ id: budget.id });
      setBudgetDetail(result);
    } catch (error) {
      console.error('Failed to fetch budget detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCycleTypeText = (cycleType?: number) => {
    switch (cycleType) {
      case CycleType.Annual:
        return t('finance.budget.cycle.annual');
      case CycleType.Monthly:
        return t('finance.budget.cycle.monthly');
      default:
        return '-';
    }
  };

  const columns = [
    {
      title: t('finance.budget.usage.column.itemName'),
      dataIndex: 'itemName',
      key: 'itemName',
    },
    {
      title: t('finance.budget.usage.column.usedAmount'),
      dataIndex: 'usedAmount',
      key: 'usedAmount',
      render: (value: number) => `¥${(value || 0).toFixed(2)}`,
    },
    {
      title: t('finance.budget.usage.column.paidAmount'),
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      render: (value: number) => `¥${(value || 0).toFixed(2)}`,
    },
  ];

  return (
    <Drawer
      title={t('finance.budget.usage.title')}
      open={open}
      onClose={onClose}
      width={800}
      loading={loading}
    >
      {budgetDetail && (
        <>
          <ProCard title={t('finance.budget.usage.basic')} className="mb-4">
            <ProDescriptions
              column={2}
              dataSource={budgetDetail}
              columns={[
                {
                  title: t('finance.budget.column.budgetName'),
                  dataIndex: 'budgetName',
                },
                {
                  title: t('finance.budget.column.cycle'),
                  dataIndex: 'cycle',
                  render: () => `${getCycleTypeText(budgetDetail.cycleType)} ${budgetDetail.cycle || ''}`,
                },
                {
                  title: t('finance.budget.column.totalBudget'),
                  dataIndex: 'totalBudget',
                  render: (value: number) => `¥${(value || 0).toFixed(2)}`,
                },
                {
                  title: t('finance.budget.usage.column.usedAmount'),
                  dataIndex: 'usedAmount',
                  render: (value: number) => `¥${(value || 0).toFixed(2)}`,
                },
                {
                  title: t('finance.budget.usage.column.paidAmount'),
                  dataIndex: 'paidAmount',
                  render: (value: number) => `¥${(value || 0).toFixed(2)}`,
                },
                {
                  title: t('finance.budget.usage.remainingBudget'),
                  dataIndex: 'remainingBudget',
                  render: () => {
                    const remaining = (budgetDetail.totalBudget || 0) - (budgetDetail.usedAmount || 0);
                    return `¥${remaining.toFixed(2)}`;
                  },
                },
              ]}
            />
          </ProCard>

          <ProCard title={t('finance.budget.usage.detail')}>
            <Table
              columns={columns}
              dataSource={budgetDetail.itemList || []}
              rowKey="itemId"
              pagination={false}
              size="small"
            />
          </ProCard>
        </>
      )}
    </Drawer>
  );
};

export default BudgetUsageDrawer;
