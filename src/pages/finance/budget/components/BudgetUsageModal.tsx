import { useIntl } from '@umijs/max';
import { Card, Divider, Flex, List, Modal, Progress, Row, Spin, Typography } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { queryBudgetDetail } from '../services';
import type { BudgetDetailEntity, BudgetEntity } from '../types/budget.entity';

const { Text } = Typography;

interface BudgetUsageModalProps {
  open: boolean;
  onClose: () => void;
  budget?: BudgetEntity | null;
}

const BudgetUsageModal: React.FC<BudgetUsageModalProps> = ({
  open,
  onClose,
  budget,
}) => {
  const intl = useIntl();
  const [loading, setLoading] = useState(false);
  const [budgetDetail, setBudgetDetail] = useState<BudgetDetailEntity | null>(null);

  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);

  const fetchBudgetDetail = useCallback(async () => {
    if (!budget?.id) return;

    setLoading(true);
    try {
      const result = await queryBudgetDetail({ id: budget.id });
      setBudgetDetail(result);
    } catch (error) {
      console.error('Failed to fetch budget detail:', error);
    } finally {
      setLoading(false);
    }
  }, [budget?.id]);

  useEffect(() => {
    if (open && budget?.id) {
      fetchBudgetDetail();
    }
  }, [open, budget, fetchBudgetDetail]);


  // 计算进度百分比
  const getProgressPercent = () => {
    if (!budgetDetail?.totalBudget || budgetDetail.totalBudget === 0) return 0;
    return Math.round(((budgetDetail.usedAmount || 0) / budgetDetail.totalBudget) * 100);
  };

  return (
    <Modal
      title={t('finance.budget.usage.title')}
      open={open}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      ) : budgetDetail && (
        <div style={{ padding: '16px 0' }}>
          {/* 预算标题 */}
          <div className='text-xl font-semibold mb-1'>
            {budgetDetail.budgetName} - {budgetDetail.cycle}
          </div>

          {/* 预算使用情况概览 */}
          <Flex justify='space-between' align='center'>
            <div>
              <Text type="secondary">{t('finance.budget.usage.column.usedAmount')} A${(budgetDetail.usedAmount || 0).toLocaleString()}</Text>
              <Divider type="vertical" />
              <Text type="secondary">{t('finance.budget.usage.column.paidAmount')} A${(budgetDetail.paidAmount || 0).toLocaleString()}</Text>
            </div>
            <div>
              <Text type="secondary">{t('finance.budget.usage.column.totalAmount')} A${(budgetDetail.totalBudget || 0).toLocaleString()}</Text>
            </div>
          </Flex>

          {/* 进度条 */}
          <Progress
            percent={getProgressPercent()}
            strokeColor="#F49C1F"
            style={{ marginBottom: 24 }}
            showInfo={false}
          />

          <div className='text-lg font-semibold mb-1'>
            {t('finance.budget.usage.detail')}
          </div>

          {/* 费用项列表 */}
          <List
            dataSource={budgetDetail.itemList || []}
            renderItem={(item) => (
              <List.Item style={{ padding: '5px 0', border: 'none' }}>
                <Card
                  size="small"
                  style={{ width: '100%', padding: '0' }}
                >
                  <Row>
                    <Text strong>{item.itemName}</Text>
                  </Row>
                  <Row justify="space-between" align="middle">
                    <Text type="secondary" >
                      {t('finance.budget.usage.column.usedAmount')} A${(item.usedAmount || 0).toLocaleString()}
                    </Text>
                    <Text type="secondary" >
                      {t('finance.budget.usage.column.paidAmount')} A${(item.paidAmount || 0).toLocaleString()}
                    </Text>
                  </Row>
                </Card>
              </List.Item>
            )}
          />
        </div>
      )
      }
    </Modal >
  );
};

export default BudgetUsageModal;
