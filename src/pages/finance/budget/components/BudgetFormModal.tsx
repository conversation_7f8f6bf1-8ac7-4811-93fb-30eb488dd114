import { queryTagList } from '@/pages/finance/tag/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ModalForm, ProFormDatePicker, ProFormDependency, ProFormDigit, ProFormRadio, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form, message } from 'antd';
import { useEffect } from 'react';
import { saveBudget } from '../services';
import { BudgetEntity } from '../types/budget.entity';
import { BudgetType, CycleType } from '../types/budget.enum';

interface BudgetFormModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  budget?: BudgetEntity | null;
}

const BudgetFormModal: React.FC<BudgetFormModalProps> = ({
  open,
  onCancel,
  onOk,
  budget,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();

  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);

  useEffect(() => {
    if (open) {
      if (budget) {
        // 编辑模式
        form.setFieldsValue({
          ...budget,
        });
      } else {
        // 新增模式
        form.resetFields();
      }
    }
  }, [open, budget, form]);

  const handleFinish = async (values: any) => {
    try {
      const params: BudgetEntity = {
        ...values,
        id: budget?.id,
      };

      const result = await saveBudget(params);
      if (result) {
        message.success(t('common.message.operationSuccess'));
        onOk();
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  };

  const handleBudgetTypeChange = (value: BudgetType) => {
    if (value !== BudgetType.Other) {
      form.setFieldValue('itemIds', undefined);
    }
  };

  const handleCycleTypeChange = (value: CycleType) => {
    form.setFieldValue('cycle', undefined);
  };

  return (
    <ModalForm
      title={t(budget ? 'finance.budget.modal.edit' : 'finance.budget.modal.add')}
      open={open}
      form={form}
      width={600}
      layout="vertical"
      modalProps={{
        maskClosable: false,
        onCancel,
      }}
      onFinish={handleFinish}
      initialValues={{
        type: BudgetType.Purchase,
        cycleType: CycleType.Annual,
      }}
    >
      <ProFormText
        name="budgetName"
        label={t('finance.budget.form.budgetName')}
        rules={[REQUIRED_RULES]}
      />

      <ProFormDigit
        name="totalBudgetYuan"
        label={t('finance.budget.form.totalBudget')}
        rules={[REQUIRED_RULES]}
        fieldProps={{
          precision: 2,
          min: 0,
        }}
      />

      <ProFormRadio.Group
        name="type"
        label={t('finance.budget.form.type')}
        rules={[REQUIRED_RULES]}
        options={[
          { label: t('finance.budget.type.purchase'), value: BudgetType.Purchase },
          { label: t('finance.budget.type.other'), value: BudgetType.Other },
        ]}
        fieldProps={{ onChange: (e) => handleBudgetTypeChange(e.target.value) }}
      />

      <ProFormDependency name={['type']}>
        {({ type, setFieldsValue }) => {
          return <>
            {
              type === BudgetType.Other && (
                <ProFormSelect
                  name="itemIds"
                  label={t('finance.budget.form.itemIds')}
                  rules={[REQUIRED_RULES]}
                  mode="multiple"
                  fieldProps={{
                    fieldNames: { label: 'tagName', value: 'id' },
                  }}
                  request={async () => {
                    const result = await queryTagList({ ledgerType: 2 });
                    return result || [];
                  }}
                />
              )
            }
          </>
        }}
      </ProFormDependency>

      <ProFormRadio.Group
        name="cycleType"
        label={t('finance.budget.form.cycleType')}
        rules={[REQUIRED_RULES]}
        options={[
          { label: t('finance.budget.cycle.annual'), value: CycleType.Annual },
          { label: t('finance.budget.cycle.monthly'), value: CycleType.Monthly },
        ]}
        fieldProps={{
          onChange: (e) => handleCycleTypeChange(e.target.value),
        }}
      />
      <ProFormDependency name={['cycleType']}>
        {({ cycleType, setFieldsValue }) => {
          return <>
            {
              cycleType === CycleType.Annual && (
                <ProFormDatePicker.Year
                  name="cycle"
                  label={t('finance.budget.form.year')}
                  rules={[REQUIRED_RULES]}
                  fieldProps={{
                    format: 'YYYY',
                  }}
                />
              )
            }
            {
              cycleType === CycleType.Monthly && (
                <ProFormDatePicker.Month
                  name="cycle"
                  label={t('finance.budget.form.month')}
                  rules={[REQUIRED_RULES]}
                  fieldProps={{
                    format: 'YYYY-MM',
                  }}
                />
              )
            }
          </>
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default BudgetFormModal;
