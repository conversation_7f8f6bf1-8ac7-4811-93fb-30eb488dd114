import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { TimeFormat } from '@/components/common/TimeFormat';
import ProFormPerformanceDate from '@/components/ProFormItem/ProFormPerformanceDate';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space, message } from 'antd';
import { useRef, useState } from 'react';
import BudgetFormModal from './components/BudgetFormModal';
import BudgetUsageModal from './components/BudgetUsageModal';
import { budgetTypeOptions, cycleTypeOptions } from './config/budget.config';
import { deleteBudget, queryBudgetPage } from './services';
import type { BudgetEntity } from './types/budget.entity';

const BudgetManagement = () => {
  const intl = useIntl();
  const actionRef = useRef<any>();
  const formRef = useRef<any>();
  const [budgetFormVisible, setBudgetFormVisible] = useState(false);
  const [usageDrawerVisible, setUsageDrawerVisible] = useState(false);
  const [selectedBudget, setSelectedBudget] = useState<BudgetEntity | null>(null);

  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);


  // 删除预算
  const handleDelete = async (record: BudgetEntity) => {
    try {
      const result = await deleteBudget({ id: record.id });
      if (result) {
        message.success(t('common.message.deleteSuccess'));
        actionRef.current?.reload();
      }
    } catch (error) {
      message.error(t('common.message.deleteFailed'));
    }
  };

  // 编辑预算
  const handleEdit = (record: BudgetEntity) => {
    setSelectedBudget(record);
    setBudgetFormVisible(true);
  };

  // 查看使用情况
  const handleViewUsage = (record: BudgetEntity) => {
    setSelectedBudget(record);
    setUsageDrawerVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<BudgetEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      fixed: 'left',
    },
    {
      title: t('finance.budget.column.budgetName'),
      dataIndex: 'budgetName',
      key: 'budgetName',
      ellipsis: true,
      width: 150,
      hideInSearch: false,
    },
    {
      title: t('finance.budget.column.totalBudget'),
      dataIndex: 'totalBudgetYuan',
      key: 'totalBudgetYuan',
      width: 120,
      render: (_, record) => `${record.totalBudgetYuan?.toFixed(2) || '0.00'}`,
      search: false,
    },
    {
      title: t('finance.budget.column.type'),
      dataIndex: 'type',
      key: 'type',
      valueType: 'select',
      valueEnum: budgetTypeOptions,
      width: 100,
      hideInSearch: false,
    },
    {
      title: t('finance.budget.column.itemNames'),
      dataIndex: 'itemNames',
      key: 'itemNames',
      ellipsis: true,
      width: 200,
      render: (_, record) => {
        if (!record.itemNames || record.itemNames.length === 0) {
          return '-';
        }
        return record.itemNames.join(', ');
      },
      search: false,
    },
    {
      title: t('finance.budget.column.cycle'),
      dataIndex: 'cycle',
      key: 'cycleType',
      width: 120,
      valueType: 'select',
      valueEnum: cycleTypeOptions,
      hideInSearch: false,
      renderFormItem: (_, record) => {
        return <ProFormPerformanceDate form={formRef.current} typeName="cycleType" dateName="cycle" />;
      },
    },
    {
      title: t('finance.budget.column.updater'),
      dataIndex: 'updater',
      key: 'updater',
      width: 100,
      search: false,
    },
    {
      title: t('finance.budget.column.updateTime'),
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      render: (_, record) => <TimeFormat time={record.updateTime || ''} />,
      search: false,
    },
    {
      title: t('common.column.operation'),
      valueType: 'option',
      width: 180,
      fixed: 'right',
      render: (_, record) => [
        <AuthButton
          key="edit"
          isHref
          // authority="editBudget"
          onClick={() => handleEdit(record)}
        >
          {t('common.button.edit')}
        </AuthButton>,
        <AuthButton
          key="usage"
          isHref
          // authority="viewBudgetUsage"
          onClick={() => handleViewUsage(record)}
        >
          {t('finance.budget.button.viewUsage')}
        </AuthButton>,
        <Popconfirm
          key="delete"
          title={t('common.tip.confirm.action', { action: t('common.button.delete') })}
          onConfirm={() => handleDelete(record)}
          okText={t('common.button.confirm')}
          cancelText={t('common.button.cancel')}
        >
          <AuthButton
            isHref
          // authority="deleteBudget"
          >
            {t('common.button.delete')}
          </AuthButton>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <FunProTable<BudgetEntity, any>
        rowKey="id"
        requestPage={queryBudgetPage}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              // authority="addBudget"
              onClick={() => {
                setSelectedBudget(null);
                setBudgetFormVisible(true);
              }}
            >
              {t('finance.budget.button.add')}
            </AuthButton>
          </Space>
        }
      />

      {/* 新增/编辑预算弹框 */}
      <BudgetFormModal
        open={budgetFormVisible}
        onCancel={() => {
          setBudgetFormVisible(false);
          setSelectedBudget(null);
        }}
        onOk={() => {
          setBudgetFormVisible(false);
          setSelectedBudget(null);
          actionRef.current?.reload();
        }}
        budget={selectedBudget}
      />

      {/* 使用情况弹框 */}
      <BudgetUsageModal
        open={usageDrawerVisible}
        onClose={() => {
          setUsageDrawerVisible(false);
          setSelectedBudget(null);
        }}
        budget={selectedBudget}
      />
    </PageContainer>
  );
};

export default withKeepAlive(BudgetManagement);