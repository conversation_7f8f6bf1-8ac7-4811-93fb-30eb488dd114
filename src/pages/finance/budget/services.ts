import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { BudgetDetailEntity, BudgetEntity } from './types/budget.entity';

export async function queryBudgetPage(params: PageRequestParamsType & BudgetEntity): Promise<PageResponseDataType<BudgetEntity>> {
  return request('/ipmsaccount/queryBudgetPage', {
    data: params,
  });
}

export async function queryBudget(params: { id?: string }): Promise<BudgetEntity> {
  return request('/ipmsaccount/queryBudget', {
    data: params,
  });
}

export async function saveBudget(params: BudgetEntity): Promise<boolean> {
  return request('/ipmsaccount/saveBudget', {
    data: params,
  });
}

export async function deleteBudget(params: { id?: string }): Promise<boolean> {
  return request('/ipmsaccount/invalidBudget', {
    data: params,
  });
}

export async function queryBudgetDetail(params: { id?: string }): Promise<BudgetDetailEntity> {
  return request('/ipmsaccount/queryBudgetDetail', {
    data: params,
  });
}