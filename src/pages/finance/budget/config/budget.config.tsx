import { FormattedMessage } from "@umijs/max";
import { BudgetType, CycleType } from "../types/budget.enum";

export const budgetTypeOptions = {
  [BudgetType.Purchase]: { text: <FormattedMessage id="finance.budget.type.purchase" />, },
  [BudgetType.Other]: { text: <FormattedMessage id="finance.budget.type.other" /> },
}


export const cycleTypeOptions = {
  [CycleType.Annual]: { text: <FormattedMessage id="finance.budget.cycle.annual" />, },
  [CycleType.Monthly]: { text: <FormattedMessage id="finance.budget.cycle.monthly" />, },
}