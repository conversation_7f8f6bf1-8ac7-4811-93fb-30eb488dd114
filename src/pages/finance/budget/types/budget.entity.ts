import { BudgetType, CycleType } from "./budget.enum";

export interface BudgetEntity {
  /**
   * 预算编号
   */
  budgetCode?: string;
  /**
   * 预算名称
   */
  budgetName?: string;
  createPerson?: string;
  createTime?: string;
  /**
   * 预算周期
   */
  cycle?: string;
  /**
   * 周期类型
   */
  cycleType?: CycleType;
  /**
   * 主键
   */
  id?: string;
  isDelete?: number;
  /**
   * 预算费用项id
   */
  itemIds?: string[];
  /**
   * 预算费用项名称
   */
  itemNames?: string[];
  memberId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 预算总金额
   */
  totalBudget?: number;
  /**
   * 预算总金额（元）
   */
  totalBudgetYuan?: number;
  /**
   * 预算类型
   */
  type?: BudgetType;
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}


export interface BudgetDetailEntity {
  /**
   * 预算名称
   */
  budgetName?: string;
  /**
   * 预算周期
   */
  cycle?: string;
  /**
   * 周期类型
   */
  cycleType?: number;
  /**
   * 使用明细
   */
  itemList?: ItemList[];
  /**
   * 已付款金额
   */
  paidAmount?: number;
  /**
   * 预算总金额
   */
  totalBudget?: number;
  /**
   * 已使用金额
   */
  usedAmount?: number;
}

export interface ItemList {
  /**
   * 费用项目id
   */
  itemId?: number;
  /**
   * 费用项目名称
   */
  itemName?: string;
  /**
   * 已付款金额
   */
  paidAmount?: number;
  /**
   * 已使用金额
   */
  usedAmount?: number;
}
