export interface CapitalEntity {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 资金流水明细
   */
  finCapitalFlowPageRoList?: FinCapitalFlowPageRoList[];
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 总支出（分）
   */
  totalExpendAmount?: number;
  /**
   * 总支出（元）
   */
  totalExpendAmountList?: TotalExpendAmountList[];
  /**
   * 总支出（元）
   */
  totalExpendAmountYuan?: number;
  /**
   * 总收入（分）
   */
  totalIncomeAmount?: number;
  /**
   * 总收入（元）
   */
  totalIncomeAmountList?: TotalIncomeAmountList[];
  /**
   * 总收入（元）
   */
  totalIncomeAmountYuan?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface FinCapitalFlowPageRoList {
  /**
   * 账户名称
   */
  accountName?: string;
  /**
   * 业务单号
   */
  bizNo?: string;
  /**
   * 业务时间
   */
  bizTime?: string;
  /**
   * 业务类型
   */
  bizType?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 买家供应商名称
   */
  customerOrSupplierName?: string;
  /**
   * 支出金额（分）
   */
  expendAmount?: number;
  /**
   * 支出金额（元）
   */
  expendAmountYuan?: number;
  /**
   * 收入金额（分）
   */
  incomeAmount?: number;
  /**
   * 收入金额（元）
   */
  incomeAmountYuan?: number;
  /**
   * 台账类型：1：收入，2：支出
   */
  ledgerType?: string;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 门店名称
   */
  storeName?: string;
}

export interface TotalExpendAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface TotalIncomeAmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}
