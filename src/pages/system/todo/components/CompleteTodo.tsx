import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import { useEffect } from 'react';
import { completeTodo } from '../services';

const CompleteTodo = ({ open, onCancel, onOk, todo }) => {
  const [form] = Form.useForm();
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

  const onFinish = async (values) => {
    await completeTodo({ ...values, id: todo.id });
    onOk();
  };

  useEffect(() => {
    if (!open) {
      form.resetFields();
    };
  }, [form, open])

  return (
    <ModalForm
      title={t('system.todo.complete')}
      open={open}
      onOpenChange={(visible) => {
        if (!visible) {
          onCancel();
        }
      }}
      form={form}
      onFinish={onFinish}
    >
      <ProFormTextArea
        name="completionDesc"
        label={t('system.todo.taskDesc.optional')}
        fieldProps={{
          showCount: true,
          maxLength: 100,
        }}
      />
    </ModalForm>
  );
};

export default CompleteTodo;
