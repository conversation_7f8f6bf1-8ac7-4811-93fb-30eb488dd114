import { FormattedMessage } from '@umijs/max';

export enum StatusEnum {
  NotCompleted = 1,
  Completed = 2,
  Cancelled = 3,
}

export const StatusEnumOptions = {
  [StatusEnum.NotCompleted]: { text: <FormattedMessage id="system.todo.status.notCompleted" />, status: 'Error', color: 'error' },
  [StatusEnum.Completed]: { text: <FormattedMessage id="system.todo.status.completed" />, status: 'Success', color: 'green' },
  [StatusEnum.Cancelled]: { text: <FormattedMessage id="system.todo.status.cancelled" />, status: 'Default', color: 'default' },
};  
