import FunProTable from '@/components/common/FunProTable';
import { TimeFormat } from '@/components/common/TimeFormat';
import { accountListQuerySimple } from '@/pages/personnel/user/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { <PERSON><PERSON>ontainer, ProColumns } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { Button, Popconfirm, Space } from 'antd';
import { useRef, useState } from 'react';
import CompleteTodo from './components/CompleteTodo';
import CreateTodo from './components/CreateTodo';
import { cancelTodo, queryTodoList } from './services';
import { TodoEntity } from './types/index.d';
import { StatusEnum, StatusEnumOptions } from './types/todo.enum.tsx';

const TodoList = () => {
  const actionRef = useRef<any>();
  const [createTodoVisible, setCreateTodoVisible] = useState(false);
  const [completeTodoVisible, setCompleteTodoVisible] = useState(false);
  const [editingTodo, setEditingTodo] = useState<TodoEntity>();
  const [completingTodo, setCompletingTodo] = useState<TodoEntity>();
  const inlt = useIntl();
  const t = (id: string, ...rest) => inlt.formatMessage({ id }, ...rest);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState;


  const columns: ProColumns<TodoEntity>[] = [
    {
      title: t('common.column.index'),
      dataIndex: 'id',
      valueType: 'index',
      width: 50
    },
    {
      title: t('system.todo.taskDesc'),
      dataIndex: 'taskDesc',
      search: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: t('system.todo.createTime'),
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
      width: 180,
      render: (text, record) => <TimeFormat time={record.createTime} showTime />
    },
    {
      title: t('system.todo.creator'),
      dataIndex: 'creator',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'createPerson'
      },
      width: 120,
    },
    {
      title: t('system.todo.todoPerson'),
      dataIndex: 'todoPersonName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'todoPerson'
      },
      width: 120,
    },
    {
      title: t('system.todo.status'),
      dataIndex: 'status',
      valueEnum: StatusEnumOptions,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'statuses'
      },
      width: 120,
    },
    {
      title: t('system.todo.completionTime'),
      dataIndex: 'completionTime',
      valueType: 'dateTime',
      search: false,
      width: 180,
      render: (text, record) => <TimeFormat time={record.createTime} showTime />
    },
    {
      title: t('system.todo.completionDesc'),
      dataIndex: 'completionDesc',
      search: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: t('common.column.operation'),
      valueType: 'option',
      fixed: 'right',
      render: (text, record, _, action) => <Space>
        {record.status == StatusEnum.NotCompleted && currentUser.accountId === record.createPerson && <a
          key="edit"
          onClick={() => {
            setEditingTodo(record);
            setCreateTodoVisible(true);
          }}
        >
          {t('common.button.edit')}
        </a>}
        {record.status == StatusEnum.NotCompleted && currentUser.accountId === record.todoPerson && <a
          key="complete"
          onClick={() => {
            setCompletingTodo(record);
            setCompleteTodoVisible(true);
          }}
        >
          {t('system.todo.button.complete')}
        </a>}
        {record.status === StatusEnum.NotCompleted && currentUser.accountId === record.createPerson &&
          <Popconfirm key="cancel" title={
            t('common.tip.confirm.action', { action: t('common.button.cancel') })
          } onConfirm={async () => {
            await cancelTodo({ id: record.id });
            actionRef.current?.reload();
          }}>
            <a
              key="cancel"
            >
              {t('common.button.cancel')}
            </a>
          </Popconfirm>}
      </Space>,
    },
  ];

  return (
    <PageContainer>
      <FunProTable<TodoEntity, any>
        rowKey="id"
        actionRef={actionRef}
        columns={columns}
        request={queryTodoList}
        headerTitle={
          <>
            <Button type="primary" onClick={() => setCreateTodoVisible(true)}>
              {t('system.todo.create')}
            </Button>
          </>
        }
      />
      <CreateTodo
        open={createTodoVisible}
        onCancel={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
        }}
        onOk={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
          actionRef.current?.reload();
        }}
        record={editingTodo}
      />
      <CompleteTodo
        open={completeTodoVisible}
        onCancel={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
        }}
        onOk={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
          actionRef.current?.reload();
        }}
        todo={completingTodo}
      />
    </PageContainer>
  );
};

export default withKeepAlive(TodoList);
