import { queryLeaveCalendar } from '@/pages/personnel/leave/services';
import { LeaveEntity } from '@/pages/personnel/leave/types/leave.entity';
import { ProCard } from '@ant-design/pro-components';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import LeaveDetailModal from './components/LeaveDetailModal';

function renderEventContent({ event }) {
  console.log(event)
  return (
    <>
      {event.title}
    </>
  )
}

const MyFullCalendar = () => {
  const calendarRef = useRef(null);
  const [leaveDetailVisible, setLeaveDetailVisible] = useState(false);
  const [selectedLeave, setSelectedLeave] = useState<LeaveEntity>();

  // 处理事件点击，用于查看详情
  const handleEventClick = (clickInfo: any) => {
    // FullCalendar会将我们传入的所有数据保存在event对象中
    // 由于我们在queryLeaveCalendar中使用了展开运算符，所有原始数据都会在event对象上
    const event = clickInfo.event.extendedProps;
    console.log('handleEventClick', event);


    setSelectedLeave(event);
    setLeaveDetailVisible(true);
  };

  // 处理日期点击，用于创建日历
  const handleDateClick = (arg) => {
    console.log('handleDateClick');

  };

  // 从后端获取事件数据
  const fetchEvents = (info, successCallback, failureCallback) => {
    queryLeaveCalendar({
      startTime: dayjs(info.start).format('YYYY-MM-DD 00:00:00'),
      endTime: dayjs(info.end).format('YYYY-MM-DD 23:59:59'),
    })
      .then(response => {
        console.log(response);
        successCallback(response);
      })
      .catch(error => {
        console.error("获取事件数据失败:", error);
        failureCallback(error);
      });
  };

  return (
    <>
      <ProCard>
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          events={fetchEvents}
          views={{
            timeGridFourDay: {
              type: 'timeGrid',
              duration: { days: 4 }
            }
          }}
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay',
          }}
          eventContent={renderEventContent}
          // footerToolbar={{
          //   center: '111'
          // }}
          // headerToolbar={{
          //   left: 'prev,next today',
          //   center: 'title',
          //   right: 'dayGridMonth,timeGridWeek,timeGridDay',
          // }}
          selectable
          eventClick={handleEventClick} // 支持点击events事件. [1]
          dateClick={handleDateClick} // 支持点击日历事件. [11, 16]
        />
      </ProCard>

      {/* 请假详情抽屉 */}
      {leaveDetailVisible && (
        <LeaveDetailModal
          visible={leaveDetailVisible}
          onClose={() => setLeaveDetailVisible(false)}
          leaveData={selectedLeave}
        />
      )}
    </>
  );
};






export default MyFullCalendar;