import { TimeRangeFormat } from '@/components/common/TimeFormat';
import { updateComments } from '@/pages/personnel/leave/services';
import { LeaveEntity } from '@/pages/personnel/leave/types/leave.entity';
import { EditOutlined, FileTextOutlined } from '@ant-design/icons';
import { ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Input, Modal, Space, message } from 'antd';
import { useEffect, useState } from 'react';

interface LeaveDetailModalProps {
  visible: boolean;
  onClose: () => void;
  leaveData: LeaveEntity;
}

const LeaveDetailModal: React.FC<LeaveDetailModalProps> = ({ visible, onClose, leaveData }) => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });

  const [isEditing, setIsEditing] = useState(false);
  const [comments, setComments] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (leaveData?.comments) {
      setComments(leaveData.comments);
    }
  }, [leaveData]);

  // 保存备注
  const handleSaveComments = async () => {
    if (!leaveData?.id) return;

    setLoading(true);
    try {
      await updateComments({
        id: leaveData.id,
        comments: comments,
      });
      message.success('备注更新成功');
      setIsEditing(false);
      // 更新本地数据
      if (leaveData) {
        leaveData.comments = comments;
      }
    } catch (error) {
      message.error('备注更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setComments(leaveData?.comments);
    setIsEditing(false);
  };

  if (!leaveData) return null;

  return (
    <Modal
      title="请假详情"
      width={400} open={visible} footer={null}
      onCancel={onClose}>
      <div className="space-y-4">
        <ProDescriptions
          dataSource={leaveData}
          column={1}
          columns={[
            {
              title: '请假人',
              dataIndex: 'accountName',
              render: (text) => (
                <div className="flex items-center justify-between w-full">
                  <span>{text}</span>
                  <div>
                    <Space>
                      <a type="link" onClick={() => setIsEditing(true)}>
                        <EditOutlined />
                        编辑
                      </a>
                      <a>
                        <FileTextOutlined />
                        详情
                      </a>
                    </Space>
                  </div>
                </div>
              ),
            },
            { title: '请假类型', dataIndex: 'typeDesc' },
            {
              title: '请假时间',
              dataIndex: 'hours',
              render: () => (
                <TimeRangeFormat startTime={leaveData.startTime} endTime={leaveData.endTime} />
              ),
            },
            {
              title: '内部备注',
              dataIndex: 'comments',
              render: (text) => (
                <div className="flex items-center justify-between mb-1 w-full">
                  {isEditing ? (
                    <div className="w-full">
                      <Input.TextArea
                        value={comments}
                        onChange={(e) => setComments(e.target.value)}
                        maxLength={100}
                        showCount
                        rows={3}
                        className="mb-2 w-full"
                      />
                      <Space>
                        <a onClick={handleCancelEdit}>取消</a>
                        <a onClick={handleSaveComments}>
                          保存
                        </a>
                      </Space>
                    </div>
                  ) : (
                    <div>
                      {comments}
                      <Button
                        type="link"
                        size="small"
                        className="ml-2"
                        onClick={() => setIsEditing(true)}
                      >
                        编辑备注
                      </Button>
                    </div>
                  )}
                </div>
              ),
            },
          ]}
        />
      </div>
    </Modal>
  );
};

export default LeaveDetailModal;
