import { TimeFormat } from '@/components/common/TimeFormat';
import LeftTitle from '@/components/LeftTitle';
import CompleteTodo from '@/pages/system/todo/components/CompleteTodo';
import CreateTodo from '@/pages/system/todo/components/CreateTodo';
import { cancelTodo, queryTodoList } from '@/pages/system/todo/services';
import type { TodoEntity } from '@/pages/system/todo/types';
import { StatusEnum, StatusEnumOptions } from '@/pages/system/todo/types/todo.enum';
import type { CurrentUserData } from '@/types/CurrentUserData';
import { FileTextOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useIntl, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Divider, Flex, Popconfirm, Tag } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useActivate, useUnactivate } from 'react-activation';


const TodoList = () => {
  const [createTodoVisible, setCreateTodoVisible] = useState(false);
  const [editingTodo, setEditingTodo] = useState<TodoEntity | null>(null);
  const [completeTodoVisible, setCompleteTodoVisible] = useState(false);
  const [completingTodo, setCompletingTodo] = useState<TodoEntity | null>(null);
  const [isCancellingTodo, setIsCancellingTodo] = useState(false);
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = (initialState || {}) as { currentUser?: CurrentUserData };
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);

  const { data: result, run: refresh } = useRequest(() => queryTodoList({
    pageSize: 5,
  }));

  // 判断是否有用户操作正在进行
  const isUserOperating = createTodoVisible || completeTodoVisible || isCancellingTodo;

  const startRefreshTimer = useCallback(
    () => {
      refreshTimerRef.current = setInterval(() => {
        // 只有在没有用户操作时才刷新
        if (!isUserOperating) {
          refresh();
        }
      }, 3000); // 3秒刷新一次
    }, [isUserOperating, refresh])

  const stopRefreshTimer = () => {
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }
  };

  useUnactivate(() => {
    stopRefreshTimer();
  });

  useActivate(() => {
    startRefreshTimer();
  });

  // 定时刷新功能
  useEffect(() => {
    startRefreshTimer();
    return () => {
      stopRefreshTimer();
    };
  }, [isUserOperating, startRefreshTimer]);


  return (
    <>
      <ProCard headerBordered={false} bordered={false}>
        <Flex justify="space-between" align="center" className="mb-[16px]">
          <LeftTitle title={intl.formatMessage({ id: 'home.task.title' })} />
          <span
            className="cursor-pointer"
            onClick={() => {
              history.push('/system/todo');
            }}
          >
            <span className="text-[#00000099]">{intl.formatMessage({ id: 'home.task.allTasks' })}</span>
            <RightOutlined width={8} height={12} className="text-[#00000073] ml-2" />
          </span>
        </Flex>
        <a
          key="complete"
          onClick={() => setCreateTodoVisible(true)}
        >
          <PlusOutlined /> {t('system.todo.create')}
        </a>

        {
          (result?.data ?? []).map((record) => (
            <div key={record.id} className='mt-2'>
              <div>
                <span className='text-lg font-semibold'>
                  <TimeFormat time={record.createTime} showTime />
                </span>
                <Tag
                  className='ml-2'
                  style={{
                    verticalAlign: '2px'
                  }}
                  color={
                    StatusEnumOptions[record.status as StatusEnum].color
                  }>{
                    StatusEnumOptions[record.status as StatusEnum].text
                  }</Tag>
                {
                  record.completionDesc && <Popconfirm placement="left"
                    title={t('system.todo.completionDesc')}
                    description={record.completionDesc}
                    showCancel={false}
                    icon={null}
                    okButtonProps={{
                      style: {
                        display: 'none'
                      }
                    }}
                  >
                    <FileTextOutlined />
                  </Popconfirm>
                }
              </div>
              <div className='text-gray-400 text-[12px]'>
                {t('system.todo.creator')}：{record.creator}
                <Divider type="vertical" />
                {t('system.todo.todoPerson')}：{record.todoPersonName}
              </div>
              <div className='my-1 line-clamp-4 break-words'>
                {record.taskDesc}
              </div>
              {
                record.completionTime && <div className='text-gray-400 text-[12px]'>
                  {t('system.todo.completionTime')}: <TimeFormat time={record.completionTime} showTime />
                </div>
              }
              {
                <div className='flex gap-2'>
                  {
                    record.status == StatusEnum.NotCompleted && currentUser?.accountId === record.createPerson && <a
                      key="edit"
                      onClick={() => {
                        setEditingTodo(record);
                        setCreateTodoVisible(true);
                      }}
                    >
                      {t('common.button.edit')}
                    </a>
                  }
                  {
                    record.status == StatusEnum.NotCompleted && currentUser?.accountId === record.todoPerson && <a
                      key="complete"
                      onClick={() => {
                        setCompletingTodo(record);
                        setCompleteTodoVisible(true);
                      }}
                    >
                      {t('system.todo.button.complete')}
                    </a>
                  }
                  {
                    record.status == StatusEnum.NotCompleted && currentUser?.accountId === record.createPerson &&
                    <Popconfirm key="cancel" title={
                      t('common.tip.confirm.action', { action: t('common.button.cancel') })
                    } onConfirm={async () => {
                      setIsCancellingTodo(true);
                      try {
                        await cancelTodo({ id: record.id });
                        refresh();
                      } finally {
                        setIsCancellingTodo(false);
                      }
                    }}>
                      <a
                        key="cancel"
                      >
                        {t('common.button.cancel')}
                      </a>
                    </Popconfirm>
                  }
                </div>
              }
            </div >
          ))
        }

      </ProCard >
      <CreateTodo
        open={createTodoVisible}
        onCancel={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
        }}
        onOk={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
          refresh();
        }}
        record={editingTodo}
      />
      <CompleteTodo
        open={completeTodoVisible}
        onCancel={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
        }}
        onOk={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
          refresh();
        }}
        todo={completingTodo}
      />
    </>

  );
};

export default TodoList;
