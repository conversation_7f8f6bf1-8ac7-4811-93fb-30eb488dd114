import LeftTitle from "@/components/LeftTitle";
import { queryFinReceivablePage } from "@/pages/finance/collection/services";
import { queryStoreByAccount } from "@/pages/personnel/user/services";
import { OrderStatus } from "@/pages/purchase/list/types/OrderStatus";
import { queryOrderStatusCount } from "@/pages/sales/order/list/services";
import { queryInventoryPagePost } from "@/pages/stocks/inventory/services";
import { DownOutlined } from "@ant-design/icons";
import { ProCard } from "@ant-design/pro-components";
import { history, useIntl, useModel } from "@umijs/max";
import { useAsyncEffect, useInterval, useReactive } from "ahooks";
import { Dropdown, Flex, Image, MenuProps, Space } from "antd";
import { defaultTo, find, isEmpty } from "lodash";
import { useState } from "react";
import { todoListData } from '../../config';
import { StoreRequestParams } from "../../types/StoreRequestParams";


const TodoList = () => {
  const intl = useIntl();
  const { initialState } = useModel('@@initialState');
  const [items, setItems] = useState<{ key: string; label: string }[]>([]);
  const [storeId, setStoreId] = useState<string>();
  const [storeName, setStoreName] = useState<string>('');
  const [count, setCount] = useState(0);
  const [storeIdListParams, setStoreIdListParams] = useState<StoreRequestParams>();

  const AllStoreItem = { key: '0', label: intl.formatMessage({ id: 'home.store.all' }) };

  // 每10分钟刷新一下
  useInterval(() => {
    setCount(count + 1);
  }, 1000 * 60 * 10);

  // 查询门店列表
  useAsyncEffect(async () => {
    const result = await queryStoreByAccount({ status: 1 });
    if (result) {
      const storeList = [AllStoreItem, ...result.map((t) => ({ label: t.name, key: t.id }))];
      setItems(storeList);
      const selectItem = storeList[0];
      console.log(selectItem);
      setStoreName(selectItem?.label);
      setStoreId(selectItem?.key);
    }
  }, []);


  // 待办事项
  const todoMap = useReactive<Record<string, number>>({
    // 待处理订单
    WillHandle: 0,
    // 待出库订单
    OutBound: 0,
    // 库存预警
    StockWarning: 0,
    // 应收预警
    CollectWarning: 0,
  });

  const onClick: MenuProps['onClick'] = ({ key }) => {
    const selectItem = find(items, ['key', key]);
    if (selectItem) {
      let storeIdList = undefined;
      if (key !== '0') {
        storeIdList = [key];
      }
      setStoreName(selectItem.label);
      setStoreId(key);
      setStoreIdListParams({ storeIdList });
    }
  };

  // 待出库
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryOrderStatusCount({
      ...storeIdListParams,
    });
    const res = result as Record<OrderStatus, number>;
    todoMap.OutBound = defaultTo(res[OrderStatus.WAIT_TO_OUTBOUND], 0);
    todoMap.WillHandle = defaultTo(res[OrderStatus.WAIT_TO_HANDLE], 0);
  }, [storeId, count]);

  // 库存预警
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryInventoryPagePost({
      onlyTotalStatistics: true,
      invLimitStatusList: [1, 2],
      storeId: storeId == '0' ? undefined : storeId,
    });
    todoMap.StockWarning = defaultTo(result?.total, 0);
  }, [storeId, count]);

  // 应收预警
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryFinReceivablePage({
      receivableFlag: 1,
      operatorNo: initialState?.currentUser?.accountId,
      ...storeIdListParams,
    });
    todoMap.CollectWarning = defaultTo(result?.total, 0);
  }, [storeId, count]);

  return <ProCard bordered={false}>
    <div className='flex justify-between items-center w-full'>
      <LeftTitle title={intl.formatMessage({ id: 'home.todo.title' })} />
      <Dropdown
        menu={{ items, onClick }}
      >
        <Space>
          <span>{storeName}</span>
          {!isEmpty(items) && <DownOutlined className="text-[#********]" />}
        </Space>
      </Dropdown>
    </div>

    <Flex className="mt-6 px-6" gap={16} justify="space-between">
      {todoListData.map((t) => (
        <Space
          size={16}
          key={t.key}
          className="cursor-pointer"
          onClick={() => {
            let pathState: any = {};
            switch (t.key) {
              case 'WillHandle':
                pathState = { ...storeIdListParams };
                break;
              case 'OutBound':
                pathState = { ...storeIdListParams };
                break;
              case 'StockWarning':
                pathState = { ...storeIdListParams };
                break;
              case 'CollectWarning':
                pathState.operatorNo = initialState?.currentUser?.accountId;
                pathState = { ...pathState, ...storeIdListParams };
                break;

              default:
                break;
            }
            history.push(t.href, { ...pathState, ...t.state });
          }}
        >
          <div className="bg-[#FDF5E8] rounded-lg w-[48px] h-[48px] flex justify-center items-center">
            <Image preview={false} src={t.icon} width={t.size[0]} height={t.size[1]} />
          </div>
          <div className="flex flex-col h-[60px] justify-between">
            <span className="text-[#********]">
              {intl.formatMessage({ id: t.labelKey })}
            </span>
            <span className="text-[#000000D9] text-[24px] font-semibold hover:text-primary transition-colors">
              {todoMap[t.key]}
            </span>
          </div>
        </Space>
      ))}
    </Flex>
  </ProCard>
}


export default TodoList;