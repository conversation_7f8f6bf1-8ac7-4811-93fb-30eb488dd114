import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { UserCouponDataRequest, UserCouponEntity } from '../../types/UserCouponDataType';


export const pageQueryUserCouponList = async (params: UserCouponDataRequest) => {
    return request<PageResponseDataType<UserCouponEntity>>(
        '/ipmspromotion/userCouponQuery/pageQueryUserCouponList',
        {
            data: params,
        },
    )
}

/**
 * 作废优惠券实体
 */
export const cancelUserCoupon = async (params: { ids: number[] }): Promise<boolean> => {
    return request(
        '/ipmspromotion/userCouponCmd/cancelUserCoupon',
        {
            data: params,
        },
    );
}