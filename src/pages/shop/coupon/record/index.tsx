import AuthButton from "@/components/common/AuthButton";
import FunProTable from "@/components/common/FunProTable";
import { ActionType, PageContainer } from "@ant-design/pro-components";
import { App, Popconfirm, Space } from "antd";
import React, { useState } from "react";
import { useIntl } from "umi";
import { UserCouponStatus, UserCouponStatusEnum } from "../../types/Coupon";
import { UserCouponEntity } from "../../types/UserCouponDataType";
import { cancelUserCoupon, pageQueryUserCouponList } from "./service";



const CouponList = () => {
    const intl = useIntl();
    const actionRef = React.useRef<ActionType>();
    const formRef = React.useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<UserCouponEntity[]>([]);

    const { message, modal, } = App.useApp();

    const handleBatchCancel = () => {
        if (selectedRowKeys.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }

        const notUsedCoupons = selectedRows.filter(row => row.couponStatus === UserCouponStatus.NOT_USED);

        if (notUsedCoupons.length === 0) {
            message.warning(intl.formatMessage({ id: 'shop.couponRecord.message.select.notUsed' }));
            return;
        }

        modal.confirm({
            title: intl.formatMessage({ id: 'common.confirm.title' }),
            content: intl.formatMessage({ id: 'shop.couponRecord.confirm.batchInvalid' }, { count: notUsedCoupons.length }),
            onOk: async () => {
                const ids = notUsedCoupons.map(row => row.userCouponId);
                const result = await cancelUserCoupon({ ids });
                if (result) {
                    message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
                    actionRef.current?.reload(true);
                    setSelectedRowKeys([]);
                    setSelectedRows([]);
                }
            }
        });
    }

    return (
        <PageContainer>
            <FunProTable<UserCouponEntity, any>
                requestPage={pageQueryUserCouponList}
                actionRef={actionRef}
                formRef={formRef}
                rowKey="userCouponId"
                rowSelection={{
                    selectedRowKeys,
                    onChange: (keys, rows) => {
                        setSelectedRowKeys(keys);
                        setSelectedRows(rows);
                    },
                }}
                headerTitle={<Space>
                    <AuthButton
                        authority="invalidUserCoupon"
                        className="button-outline"
                        onClick={handleBatchCancel}
                    >
                        {intl.formatMessage({ id: 'shop.couponRecord.list.operation.batchInvalid' })}
                    </AuthButton>
                </Space>}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        width: 40,
                        fixed: 'left',
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.recordId' }),
                        dataIndex: 'userCouponId',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.couponId' }),
                        dataIndex: 'couponId',
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.couponName' }),
                        dataIndex: 'couponName',
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.couponValidity' }),
                        dataIndex: 'deadline',
                        width: 120,
                        render: (text, record) => {
                            return <span>{record.beginTime} - {record.deadline}</span>
                        },
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.customerCode' }),
                        dataIndex: 'cstId',
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.customerName' }),
                        dataIndex: 'cstName',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.status' }),
                        dataIndex: 'couponStatus',
                        width: 80,
                        valueEnum: UserCouponStatusEnum,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.issuerReceiver' }),
                        dataIndex: 'getPerson',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.receiveTime' }),
                        dataIndex: 'getTime',
                        width: 80,
                        valueType: 'dateRange',
                        search: {
                            transform: (value: any) => {
                                return {
                                    getBeginTime: value[0],
                                    getEndTime: value[1],
                                };
                            },
                        },
                        render: (text, record) => record.getTime ? <span>{record.getTime}</span> : <span>-</span>
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.useTime' }),
                        dataIndex: 'useTime',
                        width: 80,
                        valueType: 'dateRange',
                        search: {
                            transform: (value: any) => {
                                return {
                                    useBeaginTime: value[0],
                                    useEndTime: value[1],
                                };
                            },
                        },
                        render: (text, record) => record.useTime ? <span>{record.useTime}</span> : <span>-</span>
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.couponRecord.list.orderNumber' }),
                        dataIndex: 'useOrderNo',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'common.column.operation' }),
                        width: 80,
                        hideInSearch: true,
                        fixed: 'right',
                        render: (text, record) => {
                            return <Space>
                                {record.couponStatus === UserCouponStatus.NOT_USED && <Popconfirm
                                    title={intl.formatMessage({ id: 'customer.customerList.table.popconfirm.enableDisable' }, { operType: intl.formatMessage({ id: 'shop.couponRecord.list.operation.invalid' }) })}
                                    onConfirm={() => {
                                        cancelUserCoupon({ ids: [record.userCouponId] }).then(result => {
                                            if (result) {
                                                actionRef.current?.reload(true);
                                            }
                                        })
                                    }}>
                                    <AuthButton
                                        isHref
                                        authority="invalidUserCoupon"
                                    >
                                        {intl.formatMessage({ id: 'shop.couponRecord.list.operation.invalid' })}
                                    </AuthButton>
                                </Popconfirm>
                                }
                            </Space>
                        }
                    }
                ]}
            />

        </PageContainer>
    );
};

export default CouponList;