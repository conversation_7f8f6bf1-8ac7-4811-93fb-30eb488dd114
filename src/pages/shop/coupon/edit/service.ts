
import { request } from '@/utils/request';
import { CouponFormRequest } from '../../types/CouponDataType';


export const addCouponTemplate = async (params: CouponFormRequest) => {
    return request<boolean>(
        '/ipmspromotion/couponTemplateCmd/addCouponTemplate',
        {
            data: params,
        },
    );
}

export const updateCouponTemplate = async (params: CouponFormRequest & { id: string }) => {
    return request<boolean>(
        '/ipmspromotion/couponTemplateCmd/updateCouponTemplate',
        {
            data: params,
        },
    );
}

export const queryCouponTemplateById = async (params: { id: string }) => {
    return request<CouponFormRequest>(
        '/ipmspromotion/couponTemplateQuery/queryCouponTemplateById',
        {
            data: params,
        },
    ).then(res => {
        return res || {}
    })
}