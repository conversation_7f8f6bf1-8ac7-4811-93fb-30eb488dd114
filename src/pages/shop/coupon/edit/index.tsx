import AuthButton from '@/components/common/AuthButton';
import ProFormBrand from '@/components/ProFormItem/ProFormBrand';
import ProFormCategory from '@/components/ProFormItem/ProFormCategory';
import {
    PageContainer,
    ProForm,
    ProFormDateRangePicker,
    ProFormDependency,
    ProFormDigit,
    ProFormRadio,
    ProFormText,
    ProFormTextArea
} from '@ant-design/pro-components';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { Button, message, Space } from 'antd';
import dayjs from "dayjs";
import React, { useEffect, useState } from 'react';
import SelectProductDrawer from '../../topic/decoration/CraftEditor/components/LinkSettings/SelectProductDrawer';
import { ProductItem } from '../../topic/preview/components/ProductListViewer';
import { CouponItemScopeTypeEnum, GoodsRangeTypeEnum } from '../../types/Coupon';
import { CouponFormValues } from '../../types/CouponDataType';
import { addCouponTemplate, queryCouponTemplateById, updateCouponTemplate } from './service';



const CouponForm: React.FC = () => {
    const intl = useIntl();
    const [searchParams] = useSearchParams();
    const id = searchParams.get('id');
    const type = searchParams.get('type'); // view | edit | copy
    const readonly = type === 'view';

    const [form] = ProForm.useForm<CouponFormValues>();
    const [selectedProductKeys, setSelectedProductKeys] = useState<React.Key[]>([]);

    const [isModalVisible, setIsModalVisible] = useState(false);

    useEffect(() => {
        if (id) {
            queryCouponTemplateById({ id }).then(result => {
                console.log('result', result);
                const { validityBeginTime, validityEndTime, scopeCategoryList = [], scopeBrandList = [], itemScopeType } = result;
                form.setFieldsValue({
                    ...result,
                    validityTime: [dayjs(validityBeginTime), dayjs(validityEndTime)],
                    scopeBrandIdList: scopeBrandList.map(item => (item.brandId)),
                    scopeCategoryIdList: scopeCategoryList.map(item => (item.categoryId)),
                    enableAmount: Number(result.enableAmount),
                    itemScopeType: [CouponItemScopeTypeEnum.ALL_GOODS, CouponItemScopeTypeEnum.PART_GOODS].includes(itemScopeType) ? itemScopeType : CouponItemScopeTypeEnum.PART_BRAND_CATEGORY
                });
            })
        }
    }, [id]);

    const productColumns: ProColumns<ProductItem>[] = [
        {
            title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
            dataIndex: 'itemId',
            search: false,
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
            dataIndex: 'itemName',
            search: false,
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.category' }),
            dataIndex: 'brandName',
            search: false,
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.brand' }),
            dataIndex: 'categoryName',
            search: false,
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.suggestPrice' }),
            dataIndex: 'salePrice',
            valueType: 'money',
            search: false,
        },
    ];

    const handleAddProduct = (ids, selectedProducts) => {
        console.log('selectedProducts', selectedProducts);
        form.setFieldsValue({
            scopeItemList: selectedProducts.map(item => ({ ...item, salePrice: item.suggestPrice })),
        });
        setIsModalVisible(false);
    };

    const handleDeleteSelected = () => {
        if (selectedProductKeys.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }
        const currentItems: ProductItem[] = form.getFieldValue('scopeItemList') || [];
        const newItems = currentItems.filter(item => !selectedProductKeys.includes(item.itemId));
        form.setFieldsValue({ scopeItemList: newItems });
        setSelectedProductKeys([]); // Clear selection after deletion
        message.success(intl.formatMessage({ id: 'common.message.deleteSuccess' }));
    };

    const onSubmit = async () => {
        form.validateFields().then(_values => {
            const { validityTime, itemScopeType: _itemScopeType, scopeBrandList = [], scopeCategoryList = [], ...values } = _values;
            if (_itemScopeType === CouponItemScopeTypeEnum.PART_BRAND_CATEGORY && scopeBrandList.length === 0 && scopeCategoryList.length === 0) {
                message.warning(intl.formatMessage({ id: 'shop.coupon.validity.scopeBrandCategoryListRequired' }));
                return;
            }
            const itemScopeType = (() => {
                if (values.goodsRangeType === GoodsRangeTypeEnum.ALL_GOODS) return CouponItemScopeTypeEnum.ALL_GOODS;
                if (values.goodsRangeType === GoodsRangeTypeEnum.PART_GOODS) {
                    if (scopeBrandList?.length > 0 && scopeCategoryList?.length > 0) return CouponItemScopeTypeEnum.PART_BRAND_CATEGORY;
                    if (scopeBrandList?.length > 0) return CouponItemScopeTypeEnum.PART_BRAND;
                    if (scopeCategoryList?.length > 0) return CouponItemScopeTypeEnum.PART_CATRGORY;
                    return CouponItemScopeTypeEnum.PART_GOODS;
                }
            })()
            const params = {
                ...values,
                validityBeginTime: validityTime ? validityTime?.[0]?.format('YYYY-MM-DD') : undefined,
                validityEndTime: validityTime ? validityTime?.[1]?.format('YYYY-MM-DD') : undefined,
                scopeItemList: values.scopeItemList?.map(item => ({ itemId: item.itemId })),
                itemScopeType,
                scopeBrandList,
                scopeCategoryList
            };
            console.log('params', params);
            return new Promise((resolve) => {
                if (id && type === 'edit') {
                    resolve(updateCouponTemplate({
                        ...params,
                        id,
                    }))
                } else {
                    resolve(addCouponTemplate(params))
                }
            }).then(result => {
                if (result) {
                    message.success(intl.formatMessage({ id: 'common.message.submitSuccess' }));
                    history.push('/shop/coupon/list');
                    return true;
                }
                return false;
            });
        });
    }

    return (
        <PageContainer>
            <ProForm<CouponFormValues>
                className="flex flex-col h-[calc(100vh-130px)]"
                form={form}
                initialValues={{
                    validityType: 1,
                    goodsRangeType: 0,
                    itemScopeType: 1,
                }}
                readonly={readonly}
                onFinish={onSubmit}
                submitter={{
                    render: (props, doms) => {
                        return (
                            <div className="mt-4 p-4 bg-white shadow-lg flex justify-end">
                                <Space>
                                    {
                                        readonly ?
                                            <Button onClick={() => history.back()}>
                                                {intl.formatMessage({ id: 'common.button.close' })}
                                            </Button> :
                                            <>
                                                <Button onClick={() => props.reset?.()}>
                                                    {intl.formatMessage({ id: 'common.button.cancel' })}
                                                </Button>
                                                <AuthButton authority="submitCoupon" type="primary" onClick={() => props.submit?.()}>
                                                    {intl.formatMessage({ id: 'common.button.save' })}
                                                </AuthButton>
                                            </>
                                    }

                                </Space>
                            </div>
                        );
                    },
                }}
            >
                <div className="flex flex-1 bg-white rounded-lg overflow-auto">
                    {/* 左侧：基础信息 */}
                    <div className="w-[328px] p-4">
                        <h3 className="text-lg font-semibold text-gray-800 module-title">{intl.formatMessage({ id: 'shop.coupon.basicInfo' })}</h3>
                        <ProFormText
                            name="couponName"
                            label={intl.formatMessage({ id: 'shop.coupon.couponName' })}
                            rules={[{ required: true }]}
                        />
                        <ProFormDigit
                            name="totalStock"
                            label={intl.formatMessage({ id: 'shop.coupon.totalStock' })}
                            min={0}
                            fieldProps={{ precision: 0 }}
                            rules={[
                                { required: true, },
                                { type: 'number', min: 0, message: intl.formatMessage({ id: 'shop.coupon.minStockValidation' }) },
                            ]}
                        />
                        <ProFormDigit
                            name="singleAccountNum"
                            label={intl.formatMessage({ id: 'shop.coupon.singleAccountNum' })}
                            min={0}
                            fieldProps={{
                                precision: 0,
                                addonBefore: intl.formatMessage({ id: 'shop.coupon.singleAccountNum.addonBefore' }),
                                addonAfter: intl.formatMessage({ id: 'shop.coupon.singleAccountNum.addonAfter' }),
                            }}
                            rules={[
                                { required: true, },
                                { type: 'number', min: 0 },
                            ]}
                        />
                        <ProFormDigit
                            name="couponAmount"
                            label={intl.formatMessage({ id: 'shop.coupon.discountAmount' })}
                            min={0}
                            fieldProps={{ precision: 2 }}
                            rules={[
                                { required: true, },
                                { type: 'number', min: 0, message: intl.formatMessage({ id: 'shop.coupon.minAmountValidation' }) },
                            ]}
                        />
                        <ProFormDigit
                            name="enableAmount"
                            label={intl.formatMessage({ id: 'shop.coupon.enableAmount' })}
                            min={0}
                            fieldProps={{ precision: 2 }}
                            rules={[
                                { required: true, },
                                { type: 'number', min: 0, message: intl.formatMessage({ id: 'shop.coupon.minAmountValidation' }) },
                            ]}
                        />
                        <ProFormRadio.Group
                            name="validityType"
                            label={intl.formatMessage({ id: 'shop.coupon.validityType' })}
                            options={[
                                { label: intl.formatMessage({ id: 'shop.coupon.validityTypeAbsolute' }), value: 1 },
                                { label: intl.formatMessage({ id: 'shop.coupon.validityTypeRelative' }), value: 2 },
                            ]}
                            fieldProps={{
                                optionType: 'default',
                            }}
                        />
                        <ProFormDependency name={['validityType']}>
                            {({ validityType }) => {
                                if (validityType === 1) {
                                    return (
                                        <ProFormDateRangePicker
                                            placeholder={[intl.formatMessage({ id: 'shop.coupon.validityBeginTime' }), intl.formatMessage({ id: 'shop.coupon.validityEndTime' })]}
                                            name='validityTime'
                                            label=""
                                            fieldProps={{
                                                format: 'YYYY-MM-DD',
                                                allowEmpty: [false, false],
                                                className: 'w-full'
                                            }}
                                            rules={[{ required: true, message: intl.formatMessage({ id: 'shop.coupon.dateRangeValidation' }) }]}
                                        />
                                    );
                                }
                                return (
                                    <ProFormDigit
                                        name="validityDays"
                                        label=""
                                        placeholder={intl.formatMessage({ id: 'shop.coupon.validityDaysPlaceholder' })}
                                        min={1}
                                        max={365}
                                        fieldProps={{
                                            precision: 0,
                                            addonBefore: intl.formatMessage({ id: 'shop.coupon.validityDaysPlaceholder.addonBefore' }),
                                            addonAfter: intl.formatMessage({ id: 'shop.coupon.validityDaysPlaceholder.addonAfter' }),
                                        }}
                                        rules={[
                                            { required: true },
                                            { type: 'number', min: 1, message: intl.formatMessage({ id: 'shop.coupon.minDaysValidation' }) },
                                            { type: 'number', max: 365, message: intl.formatMessage({ id: 'shop.coupon.maxDaysValidation' }) },
                                        ]}
                                    />
                                );
                            }}
                        </ProFormDependency>

                        <ProFormTextArea
                            name="useDesc"
                            label={intl.formatMessage({ id: 'shop.coupon.useDesc' })}
                            placeholder={intl.formatMessage({ id: 'shop.coupon.useDescPlaceholder' })}
                            fieldProps={{
                                maxLength: 200,
                                showCount: true,
                                autoSize: { minRows: 3, maxRows: 5 },
                            }}
                        />
                        <ProFormTextArea
                            name="remark"
                            label={intl.formatMessage({ id: 'shop.coupon.remark' })}
                            placeholder={intl.formatMessage({ id: 'shop.coupon.remarkPlaceholder' })}
                            fieldProps={{
                                maxLength: 200,
                                showCount: true,
                                autoSize: { minRows: 3, maxRows: 5 },
                            }}
                        />
                    </div>

                    {/* 右侧：适用商品 */}
                    <div className="flex-1 min-w-0 p-4">
                        <h3 className="module-title">{intl.formatMessage({ id: 'shop.coupon.applicableProducts' })}</h3>
                        <ProFormRadio.Group
                            name="goodsRangeType"
                            label={intl.formatMessage({ id: 'shop.coupon.goodsRange' })}
                            options={[
                                { label: intl.formatMessage({ id: 'shop.coupon.goodsRangeSpecific' }), value: 0 },
                                { label: intl.formatMessage({ id: 'shop.coupon.goodsRangeGeneral' }), value: 1 },
                            ]}
                            fieldProps={{
                                optionType: 'default',
                            }}
                        />

                        <ProFormDependency name={['goodsRangeType']}>
                            {({ goodsRangeType }) => {
                                if (goodsRangeType === 0) { // 指定商品
                                    return (
                                        <div className="mt-4">
                                            <ProFormRadio.Group
                                                name="itemScopeType"
                                                label={intl.formatMessage({ id: 'shop.coupon.productSettings' })}
                                                options={[
                                                    { label: intl.formatMessage({ id: 'shop.coupon.itemScopeTypeSpecific' }), value: 1 },
                                                    { label: intl.formatMessage({ id: 'shop.coupon.itemScopeTypeBrandCategory' }), value: 4 },
                                                ]}
                                                fieldProps={{
                                                    optionType: 'default',
                                                }}
                                            />

                                            <ProFormDependency name={['itemScopeType']}>
                                                {({ itemScopeType }) => {
                                                    if (itemScopeType === 1) { // 指定商品
                                                        return (
                                                            <>
                                                                {!readonly && <Space className="mb-4">
                                                                    <Button type="primary" onClick={() => setIsModalVisible(true)} >
                                                                        {intl.formatMessage({ id: 'shop.common.button.addProduct' })}
                                                                    </Button>
                                                                    <Button className="button-outline" onClick={handleDeleteSelected} >
                                                                        {intl.formatMessage({ id: 'common.button.delete' })}
                                                                    </Button>
                                                                </Space>}
                                                                <ProTable<ProductItem>
                                                                    tableAlertOptionRender={false}
                                                                    tableAlertRender={false}
                                                                    columns={productColumns}
                                                                    dataSource={form.getFieldValue('scopeItemList')}
                                                                    rowKey="itemId"
                                                                    pagination={false}
                                                                    search={false}
                                                                    options={false}
                                                                    rowSelection={{
                                                                        selectedRowKeys: selectedProductKeys,
                                                                        onChange: (keys) => {
                                                                            setSelectedProductKeys(keys);
                                                                        },
                                                                    }}
                                                                    scroll={{ y: 300 }} // Enable vertical scroll for the table
                                                                    className="mt-4"
                                                                />
                                                                {/* Hidden ProFormList to manage selected items in form values */}
                                                                <ProForm.Item name="scopeItemList" noStyle />
                                                            </>
                                                        );
                                                    } else if (itemScopeType === 4) { // 指定品牌品类
                                                        return (
                                                            <>
                                                                <ProFormBrand name="scopeBrandIdList" onChange={(ids, list) => {
                                                                    form.setFieldsValue({
                                                                        scopeBrandList: list.map(item => ({ brandId: item.value, brandName: item.title })),
                                                                    });
                                                                }} />
                                                                <ProForm.Item name="scopeBrandList" noStyle />
                                                                <ProFormCategory name="scopeCategoryIdList" onChange={(ids, label, extra) => {
                                                                    console.log('ids', ids, label, extra);
                                                                    form.setFieldsValue({
                                                                        scopeCategoryList: ids.filter(id => id).map((item, index) => ({ categoryId: item, categoryName: label?.[index] }))
                                                                    });
                                                                    console.log('scopeCategoryList', ids.filter(id => id).map((item, index) => ({ categoryId: item, categoryName: label?.[index] })));
                                                                }} />
                                                                <ProForm.Item name="scopeCategoryList" noStyle />
                                                            </>
                                                        );
                                                    }
                                                    return null;
                                                }}
                                            </ProFormDependency>
                                        </div>
                                    );
                                }
                                return null;
                            }}
                        </ProFormDependency>
                    </div>
                </div>
            </ProForm>

            <SelectProductDrawer visible={isModalVisible} onClose={() => {
                setIsModalVisible(false);
            }} onOk={handleAddProduct} />
        </PageContainer>
    );
};

export default CouponForm;