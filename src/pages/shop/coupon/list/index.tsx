import AuthButton from "@/components/common/AuthButton";
import FunProTable from "@/components/common/FunProTable";
import { ProFormCustomerList } from "@/components/ProFormItem/ProFormCustomer";
import { ActionType, ModalForm, PageContainer, ProFormDependency, ProFormRadio, ProFormTextArea } from "@ant-design/pro-components";
import { history } from "@umijs/max";
import { Form, message, Popconfirm, Space } from "antd";
import React, { useState } from "react";
import { useIntl } from "umi";
import { CouponEntity, CouponStatus, CouponStatusEnum } from "../../types/Coupon";
import { pageQueryCouponTemplateList, sendCoupon, updateCouponTemplateStatus } from "./service";

const BatchImportModal = (props) => {
    const { id, ...modalProps } = props;
    const intl = useIntl();
    const [form] = Form.useForm<{ name: string; company: string }>();
    return (
        <ModalForm<{
            type: 1 | 2;
            cstSns: string[];
            customerIdText: string;
        }>
            title={intl.formatMessage({ id: 'shop.coupon.list.button.batchSend' })}
            initialValues={{ type: 1 }}
            form={form}
            modalProps={{
                destroyOnClose: true,
                maskClosable: false,
            }}
            onFinish={(values) => {
                const { type, cstSns, customerIdText = '' } = values;
                sendCoupon({
                    id,
                    cstSns: type === 1 ? cstSns : customerIdText.split(',').map(item => item.trim()),
                }).then(result => {
                    props.onSuccess?.();
                })
            }}
            {...modalProps}
        >
            <ProFormRadio.Group
                label=""
                name="type"
                options={[
                    { label: intl.formatMessage({ id: 'shop.coupon.list.batchSend.methodChoose' }), value: 1 },
                    { label: intl.formatMessage({ id: 'shop.coupon.list.batchSend.methodInput' }), value: 2 },
                ]}
            />

            <ProFormDependency name={['type']}>
                {({ type }) => {
                    if (type === 1) {
                        return (
                            <ProFormCustomerList name="cstSns" valueKey="cstSn" label="" />
                        );
                    }
                    return <ProFormTextArea name="customerIdText" placeholder={intl.formatMessage({ id: 'shop.coupon.list.batchSend.methodInput.placeholder' })} />
                }}
            </ProFormDependency>
        </ModalForm>
    )
}

const CouponList = () => {
    const intl = useIntl();
    const actionRef = React.useRef<ActionType>();
    const formRef = React.useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [visible, setVisible] = useState(false);

    const batchSend = () => {
        if (selectedRowKeys.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }
        setVisible(true);
    }
    return (
        <PageContainer>
            <FunProTable<CouponEntity, any>
                requestPage={pageQueryCouponTemplateList}
                actionRef={actionRef}
                formRef={formRef}
                rowSelection={{
                    type: 'radio',
                    selectedRowKeys,
                    onChange: (keys, rows) => {
                        setSelectedRowKeys(keys);
                    }
                }}
                rowKey="couponId"
                headerTitle={
                    <Space>
                        <AuthButton
                            type="primary"
                            authority="addCoupon"
                            href="/shop/coupon/edit"
                        >
                            {intl.formatMessage({ id: 'common.button.add' })}
                        </AuthButton>
                        <AuthButton
                            authority="batchSendCoupon"
                            className="button-outline"
                            onClick={batchSend}
                        >
                            {intl.formatMessage({ id: 'shop.coupon.list.button.batchSend' })}
                        </AuthButton>
                    </Space>
                }
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        width: 40,
                        fixed: 'left',
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.couponId' }),
                        dataIndex: 'id',
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.couponName' }),
                        dataIndex: 'couponName',
                        width: 120,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.startingAmount' }),
                        dataIndex: 'couponAmount',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.couponAmount' }),
                        dataIndex: 'couponAmount',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.totalQuantity' }),
                        dataIndex: 'totalStock',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.remainingQuantity' }),
                        dataIndex: 'useableStock',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.validityPeriod' }),
                        dataIndex: 'validityValue',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.status' }),
                        dataIndex: 'couponStatus',
                        width: 80,
                        valueEnum: CouponStatusEnum,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.updater' }),
                        dataIndex: 'updatePerson',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.updateTime' }),
                        dataIndex: 'updateTime',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'common.column.operation' }),
                        width: 150,
                        hideInSearch: true,
                        fixed: 'right',
                        render: (text, record) => {
                            const updateCouponStatuAction = record.couponStatus === CouponStatus.ACTIVE ? intl.formatMessage({ id: 'shop.coupon.list.operation.invalid' }) : intl.formatMessage({ id: 'shop.coupon.list.operation.valid' })

                            return <Space>
                                <AuthButton
                                    isHref
                                    authority="viewCoupon"
                                    onClick={() => history.push(`/shop/coupon/edit?id=${record.id}&type=view`)}
                                >
                                    {intl.formatMessage({ id: 'common.button.view' })}
                                </AuthButton>

                                {
                                    [CouponStatus.INVALID].includes(record.couponStatus!) && <AuthButton
                                        isHref
                                        authority="editCoupon"
                                        onClick={() => history.push(`/shop/coupon/edit?id=${record.couponId}&type=edit`)}
                                    >
                                        {intl.formatMessage({ id: 'common.button.edit' })}
                                    </AuthButton>
                                }

                                <Popconfirm
                                    title={intl.formatMessage({ id: 'customer.customerList.table.popconfirm.enableDisable' }, { operType: updateCouponStatuAction })}
                                    onConfirm={() => {
                                        updateCouponTemplateStatus({
                                            id: record.id!,
                                            couponStatus: record.couponStatus === CouponStatus.ACTIVE ? CouponStatus.INVALID : CouponStatus.ACTIVE
                                        }).then(result => {
                                            if (result) {
                                                actionRef.current?.reload(true);
                                            }
                                        })
                                    }}>
                                    <AuthButton
                                        isHref
                                        authority="updateCouponStatus"
                                    >
                                        {updateCouponStatuAction}
                                    </AuthButton>
                                </Popconfirm>

                                <AuthButton
                                    isHref
                                    authority="copyCoupon"
                                    onClick={() => history.push(`/shop/coupon/edit?id=${record.id}&type=copy`)}
                                >
                                    {intl.formatMessage({ id: 'common.button.copy' })}
                                </AuthButton>
                            </Space>
                        }
                    }
                ]}
            />

            <BatchImportModal
                open={visible} onOpenChange={(visible) => setVisible(visible)} id={selectedRowKeys[0]}
                onSuccess={() => {
                    actionRef.current?.reload(true);
                    setVisible(false);
                }}
            />
        </PageContainer>
    );
};

export default CouponList;