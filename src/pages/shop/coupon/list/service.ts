
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { CouponEntity } from '../../types/Coupon';
import { CouponDataRequest, SendCouponRequest, UpdateCouponStatusRequest } from '../../types/CouponDataType';

const mockData: CouponEntity[] = [
    {
        "couponId": 12,//优惠券id
        "couponName": "优惠券名称",
        "remark": "备注",
        "couponAmount": "1.0",//优惠金额
        "validityType": 2,//有效期类型(1:绝对时间, 2:相对时间)
        "enableAmount": "1.1",//起用金额
        "totalStock": 100,//总库存
        "useableStock": 100,//剩余可用库存
        "validityValue": "10天",//有效期
        "couponStatus": 2,//状态 1:待生效, 2:生效中, 3:失效
        "updatePerson": "张三",
        "updateTime": "2022-01-01"
    },
    {
        "couponId": 13,//优惠券id
        "couponName": "优惠券名称2",
        "remark": "备注",
        "couponAmount": "1.0",//优惠金额
        "validityType": 2,//有效期类型(1:绝对时间, 2:相对时间)
        "enableAmount": "1.1",//起用金额
        "totalStock": 100,//总库存
        "useableStock": 100,//剩余可用库存
        "validityValue": "10天",//有效期
        "couponStatus": 1,//状态 1:待生效, 2:生效中, 3:失效
        "updatePerson": "张三",
        "updateTime": "2022-01-01"
    },
    {
        "couponId": 14,//优惠券id
        "couponName": "优惠券名称4",
        "remark": "备注",
        "couponAmount": "1.0",//优惠金额
        "validityType": 2,//有效期类型(1:绝对时间, 2:相对时间)
        "enableAmount": "1.1",//起用金额
        "totalStock": 100,//总库存
        "useableStock": 100,//剩余可用库存
        "validityValue": "10天",//有效期
        "couponStatus": 3,//状态 1:待生效, 2:生效中, 3:失效
        "updatePerson": "张三",
        "updateTime": "2022-01-01"
    },
];


export const pageQueryCouponTemplateList = async (params: CouponDataRequest) => {
    return request<PageResponseDataType<CouponEntity>>(
        '/ipmspromotion/couponTemplateQuery/pageQueryCouponTemplateList',
        {
            data: params,
        },
    )
}


export const updateCouponTemplateStatus = async (params: UpdateCouponStatusRequest) => {
    return request<boolean>(
        '/ipmspromotion/couponTemplateCmd/updateCouponTemplateStatus',
        {
            data: params,
        },
    );
}

/**
 * 批量发券
 */
export const sendCoupon = async (params: SendCouponRequest) => {
    return request<boolean>(
        '/ipmspromotion/couponTemplateCmd/sendCoupon',
        {
            data: params,
        },
    );
}