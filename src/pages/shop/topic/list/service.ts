import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { TopicDataRequest, TopicEntity } from '../../types/TopicDataType';

export const queryTopicPage = async (params: TopicDataRequest) => {
    return request<PageResponseDataType<TopicEntity>>(
        '/ipmspromotion/specialSubjectQuery/pageSpecialSubject',
        {
            data: params,
        },
    );
}


export const deleteTopic = async (params: { id: string }) => {
    return request<boolean>(
        '/ipmspromotion/specialSubjectCmd/deleteSpecialSubject',
        {
            data: params,
        },
    );
}

