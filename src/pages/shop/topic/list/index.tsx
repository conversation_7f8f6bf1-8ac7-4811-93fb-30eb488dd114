import AuthButton from "@/components/common/AuthButton";
import FunProTable from "@/components/common/FunProTable";
import { ActionType, PageContainer } from "@ant-design/pro-components";
import { history, useIntl } from "@umijs/max";
import { Popconfirm, Space, Tag } from "antd";
import React from "react";
import { TopicEntity } from "../../types/TopicDataType";
import { TopicPageSource, TopicPageSourceEnum, TopicStatus, TopicStatusEnum } from "../../types/TopicEnumType";
import { editSpecialSubject } from "../decoration/service";
import { deleteTopic, queryTopicPage } from "./service";




const TopicList = () => {

    const actionRef = React.useRef<ActionType>();
    const formRef = React.useRef<any>();
    const intl = useIntl();

    const handlePublish = async (id: number) => {
        const result = await editSpecialSubject({ id, status: TopicStatus.PUBLISHED });
        if (result) {
            actionRef.current?.reload(true);
        }
    };

    const handleUnPublish = async (id: number) => {
        const result = await editSpecialSubject({ id, status: TopicStatus.DRAFT });
        if (result) {
            actionRef.current?.reload(true);
        }
    };


    return (
        <PageContainer>
            <FunProTable<TopicEntity, any>
                requestPage={queryTopicPage}
                actionRef={actionRef}
                formRef={formRef}
                headerTitle={
                    <Space>
                        <AuthButton
                            type="primary"
                            authority="editTopic"
                            href="/shop/topic/decoration"
                        >
                            {intl.formatMessage({ id: 'common.button.add' })}
                        </AuthButton>
                    </Space>
                }
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        width: 40,
                        fixed: 'left',
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.topicPageId' }),
                        dataIndex: 'id',
                        width: 80,
                        render: (text, record) => <>{text}{record.pageSource === TopicPageSource.APP && <Tag color="orange" className="ml-2">{intl.formatMessage({ id: 'shop.topic.topicPageSource.app' })}</Tag>}</>
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.topicPageName' }),
                        dataIndex: 'title',
                        width: 120,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.pageStatus' }),
                        dataIndex: 'status',
                        width: 80,
                        valueEnum: TopicStatusEnum,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.pageSource' }),
                        dataIndex: 'pageSource',
                        width: 80,
                        valueEnum: TopicPageSourceEnum,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.updater' }),
                        dataIndex: 'updatePerson',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.updateTime' }),
                        dataIndex: 'updateTime',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.pageRemark' }),
                        dataIndex: 'remark',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'common.column.operation' }),
                        width: 120,
                        hideInSearch: true,
                        fixed: 'right',
                        render: (text, record) => {
                            return <Space>
                                <AuthButton isHref authority="editTopic"
                                    onClick={() => {
                                        history.push(`/shop/topic/decoration?id=${record.id}&type=edit`);
                                    }}
                                >
                                    {intl.formatMessage({ id: 'shop.topic.list.operation.decoration' })}
                                </AuthButton>

                                <AuthButton isHref authority="copyTopic"
                                    onClick={() => {
                                        history.push(`/shop/topic/decoration?id=${record.id}&type=copy`);
                                    }}
                                >
                                    {intl.formatMessage({ id: 'shop.topic.list.copy' })}
                                </AuthButton>

                                <Popconfirm
                                    title={intl.formatMessage({ id: 'customer.customerList.table.popconfirm.enableDisable' }, { operType: intl.formatMessage({ id: 'common.button.delete' }) })}
                                    onConfirm={() => {
                                        deleteTopic({ id: record.id }).then(result => {
                                            if (result) {
                                                actionRef.current?.reload(true);
                                            }
                                        })
                                    }}>
                                    <AuthButton isHref authority="deleteTopic" >{intl.formatMessage({ id: 'common.button.delete' })}</AuthButton>
                                </Popconfirm>

                                {record.status === TopicStatus.DRAFT && <AuthButton isHref authority="updateTopicStatus" onClick={() => handlePublish(record.id)}>{intl.formatMessage({ id: 'shop.topic.list.publish' })}</AuthButton>}

                                {record.status === TopicStatus.PUBLISHED && <AuthButton isHref authority="updateTopicStatus" onClick={() => handleUnPublish(record.id)}>{intl.formatMessage({ id: 'shop.topic.list.unpublish' })}</AuthButton>}
                            </Space>
                        },
                    },
                ]}
            />
        </PageContainer>
    );
};

export default TopicList;