import { request } from '@/utils/request';
import { TopicEntity, TopicFormValueData } from '../../types/TopicDataType';


export const addSpecialSubject = async (params: Omit<TopicFormValueData, 'id'>) => {
    return request<boolean>(
        '/ipmspromotion/specialSubjectCmd/addSpecialSubject',
        {
            data: params,
        },
    );
}

export const editSpecialSubject = async (params: TopicFormValueData) => {
    return request<boolean>(
        '/ipmspromotion/specialSubjectCmd/editSpecialSubject',
        {
            data: params,
        },
    );
}

export const getSpecialSubject = async (params: { id: string }) => {
    return request<TopicEntity>(
        '/ipmspromotion/specialSubjectQuery/getSpecialSubject',
        {
            data: params,
        },
    )
}

