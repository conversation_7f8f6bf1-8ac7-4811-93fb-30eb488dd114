import ExitIcon from '@/assets/icons/decoration/exit.svg';
import SearchBar from '@/assets/icons/decoration/searchbar.png';
import StatusBar from '@/assets/icons/decoration/statusbar.png';
import TabBar from '@/assets/icons/decoration/tabbar.png';
import AuthButton from '@/components/common/AuthButton';
import { TopicPageSource } from '@/pages/shop/types/TopicEnumType';
import { SaveOutlined } from '@ant-design/icons';
import { Editor, Element, Frame, useEditor } from '@craftjs/core';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { Button, Form, FormInstance, Space } from 'antd';
import LZString from 'lz-string'; // Optional: for compressing saved data
import React, { useEffect, useState } from 'react';
import { addSpecialSubject, editSpecialSubject, getSpecialSubject } from '../service';
import { Container } from './components/Container';
import Coupon from './components/Coupon';
import Divider from './components/Divider';
import ImageAd from './components/ImageAds';
import ImageHotspot from './components/ImageHotspot';
import ModuleTitle from './components/ModuleTitle';
import PageLayout from './components/PageLayout';
import ProductList from './components/ProductList';
import { Settings } from './components/Settings';
import { Toolbar } from './components/Toolbar';


const compress = (string: string) => LZString.compressToUTF16(string);
const decompress = (string: string) => LZString.decompressFromUTF16(string);

const EditorActions = (props: { moduleInfo?: string; form?: FormInstance }) => {
  const { form } = props;
  const { actions, query, } = useEditor((state) => ({
    enabled: state.options.enabled
  }));
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const type = searchParams.get('type'); // edit | copy

  const inlt = useIntl();

  // 保存编辑器状态为JSON
  const handleSave = async () => {
    const valid = await form?.validateFields();
    if (!valid) return;
    try {
      const json = query.serialize();
      const compressed = compress(json);
      localStorage.setItem('craftjs-editor-state', compressed);
      const values = form?.getFieldsValue();
      const params = {
        ...values,
        moduleInfo: compressed,
        title: values.pageTitle,
        remark: values.remark,
        pageSource: values.isHome ? TopicPageSource.APP : TopicPageSource.TOPIC
      }
      return new Promise((resolve, reject) => {
        if (type === 'edit' && id) {
          editSpecialSubject({ id, ...params }).then(res => {
            resolve(res);
          })
        } else {
          addSpecialSubject({ ...params }).then(res => {
            resolve(res);
          })
        }
      }).then((result) => {
        if (result) {
          history.push('/shop/topic/list');
        }
      })

    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 从本地存储加载编辑器状态
  const handleLoad = (savedState) => {
    try {
      if (savedState) {
        const json = JSON.parse(decompress(savedState));
        console.log(json)
        actions.deserialize(json);
      }
    } catch (error) {
      console.error('加载失败:', error);
    }
  };

  useEffect(() => {
    if (props.moduleInfo) {
      handleLoad(props.moduleInfo);
    } else {
      actions.deserialize({
        "ROOT": {
          "type": {
            "resolvedName": "Container"
          },
          "isCanvas": true,
          "props": {
            "background": "#ffffff",
            "padding": 0
          },
          "displayName": "topic.decoration.icon.container",
          "custom": {},
          "hidden": false,
          "nodes": [],
          "linkedNodes": {}
        }
      })
    }
  }, [props.moduleInfo]);

  return (
    <Space>
      <AuthButton
        authority='saveTopic'
        type="primary"
        icon={<SaveOutlined />}
        onClick={handleSave}
      >
        {inlt.formatMessage({ id: 'common.button.save' })}
      </AuthButton>
    </Space>
  );
};

export const CraftEditor: React.FC = () => {
  const [enabled] = useState(true); // 编辑器启用状态
  const inlt = useIntl();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const [form] = Form.useForm();
  const isHome = Form.useWatch('isHome', form);


  const [moduleInfo, setModuleInfo] = useState<string>();


  useEffect(() => {
    if (id) {
      getSpecialSubject({ id }).then(result => {
        form.setFieldsValue({
          pageTitle: result.title,
          isHome: result.pageSource === TopicPageSource.APP,
          remark: result.remark
        })
        setModuleInfo(result.moduleInfo)
      });
    } else {
      setModuleInfo('')
      form.resetFields()
    }
  }, [id]);

  return (
    <div className="flex flex-col" style={{
      height: 'calc(100vh - 170px)'
    }}>
      <Editor
        resolver={{
          ImageAd,
          ImageHotspot,
          ProductList,
          ModuleTitle,
          Coupon,
          Divider,
          Container,
        }}
        enabled={enabled}
      >
        <div className='flex flex-col h-full b-gray-100 min-w-[1300px]'>
          {/* 操作栏 */}
          <div className='flex justify-between bg-white p-3' style={{ borderBottom: '1px solid rgba(13, 13, 13, 0.08)' }}>
            <Button type='text' className='text-primary'>
              <img src={ExitIcon} alt="Exit" className="w-4 h-4 mr-1" />
              {inlt.formatMessage({ id: 'topic.decoration.button.exit' })}
            </Button>
            <EditorActions moduleInfo={moduleInfo} form={form} />
          </div>
          {/* 编辑区域 */}
          <div className="flex flex-1 w-full h-full bg-gray-100">
            <Toolbar form={form} />
            <div className="flex-1 h-full p-5 bg-gray-100 relative overflow-auto">
              <div
                className="flex flex-col w-[375px] h-[844px] bg-white shadow-lg overflow-visible relative m-auto"
                style={{
                  boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.05), 0 10px 30px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div className='flex-1'>
                  <div className='px-5 py-2'>
                    <img src={StatusBar} className='w-full' />
                  </div>
                  {isHome && <img src={SearchBar} className='w-full' />}
                  <Frame>
                    <Element is={Container} padding={0} background="#ffffff" canvas>
                      {/* 初始画布为空，children是必需的 */}
                    </Element>
                  </Frame>
                </div>
                <div>
                  <img src={TabBar} className='w-full' />
                </div>
              </div>
            </div>
            <div className="flex h-full bg-white border-l border-gray-200">
              <PageLayout />
              <Settings />
            </div>
          </div>
        </div >
      </Editor >
    </div >
  );
};

export default CraftEditor;
