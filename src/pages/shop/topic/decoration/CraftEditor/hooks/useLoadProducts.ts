import { queryGoodsPage } from "@/pages/goods/list/services";
import { GoodsEntity } from "@/pages/goods/list/types/GoodsEntity.entity";
import { useIntl } from "@umijs/max";
import { useEffect, useState } from "react";
import { ProductListProps } from "../components/ProductList/types";


/**
 * Display 和 Setting组件各会调用该hook
 */
export default function useLoadProducts(props: Pick<ProductListProps, 'productSource' | 'specifiedProductIds' | 'conditions'>) {
    const [loading, setLoading] = useState(false);

    const { specifiedProductIds = [], productSource, conditions } = props;

    const { displayCountType, displayCount, ...searchConditions } = conditions ?? {};

    const [products, setProducts] = useState<GoodsEntity[]>([]);
    const inlt = useIntl();

    useEffect(() => {
        const loadProducts = async () => {
            const notChooseProductSource = (productSource === 'specified' && specifiedProductIds?.length == 0) || (productSource === 'conditional' && Object.keys(searchConditions ?? {}).length === 0);
            if (notChooseProductSource) {
                setProducts(Array.from({ length: 4 }, (_, i) => ({
                    itemId: `condProd-${i}`,
                    itemName: inlt.formatMessage({ id: 'topic.decoration.productList.mock.itemName' }),
                    price: (Math.random() * 100 + 50).toFixed(2),
                    originalPrice: (Math.random() * 50 + 150).toFixed(2),
                })));
                return;
            }
            setLoading(true);
            if (productSource === 'specified' && specifiedProductIds && specifiedProductIds.length > 0) {
                const fetched = await queryGoodsPage({
                    itemIdList: specifiedProductIds,
                    pageNo: 1,
                    pageSize: 1000
                });
                setProducts(fetched?.data ?? []);
            } else if (productSource === 'conditional') {
                const fetched = await queryGoodsPage({ ...searchConditions, pageNo: 1, pageSize: displayCountType === 'all' ? 1000 : displayCount });
                console.log('fetched', fetched)
                setProducts(fetched.data ?? []);
            } else {
                setProducts([]); // No source or empty IDs
            }
            setLoading(false);
        };
        loadProducts();
    }, [productSource, specifiedProductIds, conditions, displayCountType, displayCount]);

    return {
        loading,
        products
    };
}