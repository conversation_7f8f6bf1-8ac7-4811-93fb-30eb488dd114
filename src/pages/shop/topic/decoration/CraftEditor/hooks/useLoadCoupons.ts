import { pageQueryCouponTemplateList } from "@/pages/shop/coupon/list/service";
import { useEffect, useState } from "react";
import { CouponItem, CouponProps } from "../components/Coupon/type";



export default function useLoadCoupons(props: CouponProps) {
    const { couponsIds } = props;

    const [loading, setLoading] = useState(false);
    const [coupons, setCoupons] = useState<CouponItem[]>([]);

    useEffect(() => {
        if (couponsIds.length === 0) return;
        const loadCoupons = async () => {
            setLoading(true);
            const fetched = await pageQueryCouponTemplateList({ ids: couponsIds, pageSize: 1000 });
            setCoupons(fetched.data ?? []);
            setLoading(false);
        };
        loadCoupons();
    }, [couponsIds]);

    return {
        loading,
        coupons
    };
}