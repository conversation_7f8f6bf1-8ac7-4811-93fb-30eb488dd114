import { useEditor, useNode } from '@craftjs/core';
import React from 'react';
import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import { ModuleTitleSettings } from './Setttings';
import { ModuleTitleProps } from './type';


const ModuleTitle: React.FC<ModuleTitleProps> = ({
    title,
    showSubtitle,
    subtitle,
    showMore,
    moreText,
    linkTarget,
}) => {
    const {
        connectors: { connect, drag },
        selected,
        actions: { setProp },
    } = useNode((node) => ({
        selected: node.events.selected,
    }));
    const { enabled } = useEditor((state) => ({
        enabled: state.options.enabled,
    }));

    // 获取实际的链接URL或模拟跳转
    const getLinkUrl = () => {
        if (!linkTarget?.value) return '#';
        switch (linkTarget.type) {
            case 'topicPage':
            case 'productCategory':
                return `/pages/${linkTarget.value.id}`; // 模拟根据ID跳转
            case 'customLink':
                return linkTarget.value.url || '#';
            default:
                return '#';
        }
    };

    const handleLinkClick = (e: React.MouseEvent) => {
        if (enabled) {
            e.preventDefault(); // 在编辑器模式下阻止实际跳转
            console.log('Link clicked in editor:', getLinkUrl());
        }
        // else { Allow default link behavior in preview/runtime }
    };

    return (
        <div
            ref={(ref) => connect(drag(ref as HTMLElement))}
            className={`px-[10px] py-[5px] border-b border-gray-200 flex justify-between ${selected ? 'border-blue-500 border-2' : ''}`}
        >
            <div className="flex flex-col">
                <h4 className="!text-base !mb-0">{title}</h4>
                {showSubtitle && subtitle && (
                    <div className="text-sm mt-1 text-gray-400">
                        {subtitle}
                    </div>
                )}
            </div>
            {showMore && (
                <a href={getLinkUrl()} onClick={handleLinkClick} className="text-blue-500 text-sm mt-1">
                    {moreText}
                </a>
            )}
        </div>
    );
};

// 定义默认属性
ModuleTitle.craft = {
    props: {} as ModuleTitleProps,
    displayName: 'topic.decoration.icon.moduleTitle',
    // 在这里指定设置面板组件
    related: {
        settings: ModuleTitleSettings
    },
};

export default wrapWithSelectionWrapper(ModuleTitle);