// src/components/ModuleTitle/ModuleTitleSettings.tsx (示例路径)
import {
    ProForm,
    ProFormDependency,
    ProFormText,
    ProFormTextArea
} from '@ant-design/pro-components';
import { useNode } from '@craftjs/core';
import React from 'react';
import { useIntl } from 'react-intl';

import { Form, Switch } from 'antd';
import LinkSettings from '../LinkSettings';
import { ModuleTitleProps } from './type';

export const ModuleTitleSettings: React.FC = () => {
    const intl = useIntl();
    const {
        props,
        actions: { setProp },
    } = useNode((node) => ({
        props: node.data.props as ModuleTitleProps,
    }));

    return (

        <ProForm
            labelAlign="left"
            labelCol={{ span: 21 }}
            wrapperCol={{ span: 3 }}
            layout="vertical"
            initialValues={props}
            onValuesChange={(changedValues, allValues) => {
                // console.log(changedValues, allValues);
                // 当表单值变化时，更新Craft.js节点的props
                // 注意：allValues是整个表单的值，changedValues是本次改变的值
                setProp((draft: ModuleTitleProps) => {
                    Object.assign(draft, allValues);
                });
            }}
            className="p-4"
            submitter={false}
        >
            {/* 标题内容 */}
            <ProFormText
                wrapperCol={{ span: 24 }}
                name="title"
                label={intl.formatMessage({ id: 'topic.decoration.moduleTitle.titleContent' })}
            />

            {/* 显示副标题 */}
            <Form.Item
                label={intl.formatMessage({ id: 'topic.decoration.moduleTitle.showSubtitle' })}
                className='form-item-switch' name="showSubtitle"
                style={{ marginBottom: 0 }}
            >
                <Switch />
            </Form.Item>


            {/* 副标题内容 (条件显示) */}
            <ProFormDependency name={['showSubtitle']}>
                {({ showSubtitle }) => {
                    if (!showSubtitle) {
                        return null;
                    }
                    return (
                        <ProFormTextArea
                            wrapperCol={{ span: 24 }}
                            name="subtitle"
                            fieldProps={{
                                maxLength: 100,
                                showCount: true,
                            }}
                        />
                    );
                }}
            </ProFormDependency>

            {/* 显示更多 */}
            <Form.Item
                label={intl.formatMessage({ id: 'topic.decoration.moduleTitle.showMore' })}
                className='form-item-switch' name="showMore"
                style={{ marginTop: 30 }}
            >
                <Switch />
            </Form.Item>



            {/* 提示文字 (条件显示) */}
            <ProFormDependency name={['showMore']}>
                {({ showMore }) => {
                    if (!showMore) {
                        return null;
                    }
                    return (
                        <>
                            <ProFormText
                                wrapperCol={{ span: 24 }}
                                name="moreText"
                                label={intl.formatMessage({ id: 'topic.decoration.moduleTitle.promptText' })}
                            />
                            <LinkSettings
                                name="linkTarget"
                                value={props.linkTarget}
                                onChange={(value) => setProp((props: ModuleTitleProps) => props.linkTarget = value)}
                                wrapperCol={{ span: 24 }}
                                label={intl.formatMessage({ id: 'topic.decoration.moduleTitle.linkSetting' })}
                            />
                        </>
                    );
                }}
            </ProFormDependency>



        </ProForm>

    )
};