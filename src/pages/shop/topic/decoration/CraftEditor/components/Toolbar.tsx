import CouponIcon from '@/assets/icons/decoration/coupon.svg';
import DividerIcon from '@/assets/icons/decoration/divider.svg';
import HotspotIcon from '@/assets/icons/decoration/hotspot.svg';
import ImageIcon from '@/assets/icons/decoration/image.svg';
import ProductIcon from '@/assets/icons/decoration/product.svg';
import ModuleTitleIcon from '@/assets/icons/decoration/title.svg';
import { useEditor } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import { Divider as AntdDivider, Form, FormInstance, Input, Switch } from 'antd';
import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import Coupon from './Coupon';
import Divider from './Divider';
import ImageAd from './ImageAds';
import ImageHotspot from './ImageHotspot';
import ModuleTitle from './ModuleTitle';
import ProductList from './ProductList';

// 拖拽组件项
const DraggableItem: React.FC<{
  element: React.ReactElement;
  label: string;
  icon?: React.ReactNode;
}> = ({ element, label, icon }) => {

  const { connectors } = useEditor();

  return (
    <div
      ref={(ref) => connectors.create(ref as HTMLElement, element)}
      className="w-2/6 bg-white py-2 hover:bg-gray-50 border border-gray-200 rounded-lg mb-2 cursor-move transition-colors duration-150 text-center"
    >
      {icon && <div className="text-2xl mb-1">{icon}</div>}
      <div className="text-gray-700 text-sm">{label}</div>
    </div>
  );
};

// 工具栏组件接口定义
interface ToolbarProps {
  form: FormInstance;
}

// 工具栏组件
export const Toolbar: React.FC<ToolbarProps> = ({ form }) => {
  const intl = useIntl();

  return (
    <div className="w-[320px] bg-white p-[24px] border-r border-gray-200 overflow-y-auto h-full">

      <h3 className="module-title">{intl.formatMessage({ id: 'topic.decoration.title.pageSetting' })}</h3>
      <Form
        labelAlign="left"
        requiredMark={false}
        layout="vertical"
        labelCol={{ span: 18 }}
        wrapperCol={{ span: 6 }}
        form={form}
      >
        <Form.Item wrapperCol={{ span: 24 }} label={intl.formatMessage({ id: 'topic.decoration.label.pageTitle' })} name="pageTitle" rules={[{ required: true }]}>
          <Input placeholder={intl.formatMessage({ id: 'topic.decoration.placeholder.pageTitle' })} />
        </Form.Item>
        <Form.Item
          className='form-item-switch'
          label={intl.formatMessage({ id: 'topic.decoration.label.isHome' })}
          name="isHome"
        >
          <Switch />
        </Form.Item>
        <Form.Item
          wrapperCol={{ span: 24 }}
          label={intl.formatMessage({ id: 'topic.decoration.label.pageRemark' })}
          name="remark"
        >
          <Input.TextArea maxLength={100} count={{
            show: true,
            max: 100,
          }} />
        </Form.Item>
      </Form>

      <AntdDivider />

      <h3 className="module-title">{intl.formatMessage({ id: 'topic.decoration.title.components' })}</h3>

      <div className='flex flex-wrap'>
        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.hotspot' })}
          icon={<img src={HotspotIcon} alt="Hotspot" className="w-8 h-8" />}
          element={(
            <ImageHotspot hotspots={[]} />
          )}
        />

        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.imageAd' })}
          icon={<img src={ImageIcon} alt="Hotspot" className="w-8 h-8" />}
          element={(
            <ImageAd slides={[]} />
          )}
        />

        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.productList' })}
          icon={<img src={ProductIcon} alt="Hotspot" className="w-8 h-8" />}
          element={(
            <ProductList
              listStyle="two-per-row"
              productType="single"
              productSource="specified"
              specifiedProductIds={[]}
              conditions={{
                displayCountType: 'count',
                displayCount: 5,
              }}
              groups={[
                {
                  id: uuidv4(),
                  title: `${intl.formatMessage({ id: 'topic.decoration.productList.settings.newGroup' }, { number: 1 })}`,
                  productSource: 'specified',
                  specifiedProductIds: [],
                  conditions: {
                    displayCountType: 'count',
                    displayCount: 5,
                  }
                }
              ]}
            />
          )}
        />

        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.coupon' })}
          icon={<img src={CouponIcon} alt="Coupon" className="w-8 h-8" />}
          element={(
            <Coupon couponsIds={[]} />
          )}
        />

        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.moduleTitle' })}
          icon={<img src={ModuleTitleIcon} alt="ModuleTitle" className="w-8 h-8" />}
          element={(
            <ModuleTitle
              title={intl.formatMessage({ id: 'topic.decoration.moduleTitle.titleContent.placeholder' })}
              showSubtitle
              subtitle={intl.formatMessage({ id: 'topic.decoration.moduleTitle.subtitleContent.placeholder' })}
              showMore
              moreText={intl.formatMessage({ id: 'topic.decoration.moduleTitle.showMore.placeholder' })}
            />
          )}
        />

        <DraggableItem
          label={intl.formatMessage({ id: 'topic.decoration.icon.divider' })}
          icon={<img src={DividerIcon} alt="Divider" className="w-8 h-8" />}
          element={(
            <Divider />
          )}
        />


      </div>

      <div className="text-xs text-gray-500 mt-auto pt-4 border-t border-gray-200">
        <p>{intl.formatMessage({ id: 'topic.decoration.tips.dragComponent' })}</p>
        <p>{intl.formatMessage({ id: 'topic.decoration.tips.clickComponent' })}</p>
      </div>
    </div>
  );
};
