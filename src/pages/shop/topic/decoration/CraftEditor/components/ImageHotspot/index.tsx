// import HotspotIcon from '@/assets/icons/decoration/hotspot.svg';
import { ReactComponent as HotspotIcon } from '@/assets/icons/decoration/hotspot.svg';
import { useEditor, useNode } from '@craftjs/core';
import { useIntl } from '@umijs/max'; // 修改 useIntl 导入路径
import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import ImageHotspotSettings from './settings';



interface HotspotData {
    id: string;
    x: number;
    y: number;
    width: number;
    height: number;
    link?: string;
    productId?: string;
    tooltip?: string;
}

interface ImageHotspotProps {
    imageUrl?: string | null;
    altText?: string;
    hotspots?: HotspotData[];
    // otherProps 如果需要，可以在这里定义
}

const ImageHotspot = ({ imageUrl, altText, hotspots }: ImageHotspotProps) => { // 添加类型并移除 otherProps
    const {
        connectors: { connect, drag },
        id
    } = useNode();
    const { enabled } = useEditor((state) => ({ enabled: state.options.enabled }));
    const intl = useIntl(); // 获取 intl 实例

    return (
        <div
            ref={ref => connect(drag(ref))}
            style={{ position: 'relative', display: 'block' /* Prevents extra space below img */ }}
            data-cy={`image-hotspot-${id}`}
            className='p-[10px]'
        >
            {imageUrl ? (
                <img
                    src={imageUrl}
                    alt={altText || 'Hotspot Image'}
                    style={{ display: 'block', maxWidth: '100%', height: 'auto' }}
                />
            ) : (
                <div
                    ref={(ref) => connect(drag(ref as HTMLDivElement))}
                    className={`relative flex flex-col items-center justify-center text-gray-500 bg-[#f3f5f6]`}
                    style={{ minHeight: '200px' }}
                >
                    <HotspotIcon stroke="#F49C1F" />
                    <p className='mt-1 text-sm'>{intl.formatMessage({ id: 'topic.decoration.imageHotspot.placeholder' })}</p>
                </div>
            )}

            {/* 渲染热区 */}
            {hotspots && hotspots.map((hotspot: HotspotData) => ( // 添加 hotspot 类型
                <a // Use <a> tag if link is primary action, or <div> for more complex interactions
                    key={hotspot.id}
                    href={!enabled && hotspot.link ? hotspot.link : undefined}
                    target={!enabled && hotspot.link && hotspot.link.startsWith('http') ? '_blank' : undefined}
                    rel={!enabled && hotspot.link && hotspot.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                    className="hotspot-area"
                    // style={{
                    //     position: 'absolute',
                    //     left: `${hotspot.x * 100}%`,
                    //     top: `${hotspot.y * 100}%`,
                    //     width: `${hotspot.width * 100}%`,
                    //     height: `${hotspot.height * 100}%`,
                    //     // --- 样式，例如半透明边框，以便用户知道可点击 ---
                    //     border: enabled ? '1px dashed rgba(255,0,0,0.7)' : (hotspot.link || hotspot.productId ? '2px solid rgba(0,123,255,0.5)' : 'none'),
                    //     background: enabled ? 'rgba(255,0,0,0.1)' : 'transparent',
                    //     cursor: !enabled && (hotspot.link || hotspot.productId) ? 'pointer' : 'default',
                    //     boxSizing: 'border-box',
                    //     // pointerEvents: enabled ? 'none' : 'auto', // 在编辑器模式下，热区本身不应响应点击，由设置面板控制
                    // }}
                    title={!enabled ? hotspot.tooltip : ''}
                    onClick={(e) => {
                        if (enabled) {
                            e.preventDefault(); // 编辑模式下阻止链接跳转
                            // 可以在这里触发选中该热区进行编辑的逻辑 (如果需要在主画布操作)
                        } else if (hotspot.productId) {
                            e.preventDefault(); // 如果有 productId，可能需要自定义行为而不是简单跳转
                            console.log('Clicked product hotspot:', hotspot.productId);
                            // openProductModal(hotspot.productId);
                        }
                        // Link navigation is handled by href if it's an <a> tag
                    }}
                >
                    {/* 如果需要在热区内显示 tooltip 或其他内容，可以放在这里 */}
                    {/* {enabled && <span style={{color: 'white', background: 'black', padding: '2px'}}>{hotspot.id.substring(0,4)}</span>} */}
                </a>
            ))}
        </div>
    );
};



ImageHotspot.craft = {
    displayName: 'topic.decoration.icon.hotspot',
    props: {
        imageUrl: null,
        altText: 'Image with Hotspots',
        hotspots: [],
    },
    related: {
        settings: () => <ImageHotspotSettings /> // 指向设置面板组件
    }
};

export default wrapWithSelectionWrapper(ImageHotspot);
