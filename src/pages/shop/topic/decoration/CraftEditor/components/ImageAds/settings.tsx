import { compressImage } from '@/utils/fileUtils';
import { DeleteOutlined } from '@ant-design/icons';
import { useNode } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import { Button, message, Upload } from 'antd';
import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import LinkSettings, { LinkType, LinkTypeValue } from '../LinkSettings';
import { ImageAdProps } from './main';


const uploadProps = {
    name: "file",
    multiple: false,
    accept: ".jpg,.png,.jpeg,.gif,.webp",
    action: "/apigateway/public/upload/object/batch",
    showUploadList: false,
    beforeUpload: (file) => compressImage(file),
};
// This interface is also used by ImageAd.tsx
export interface ImageAdSlideProps {
    id: string;
    imageUrl: string;
    linkTarget: LinkTypeValue;
}

const defaultSlide: Omit<ImageAdSlideProps, 'id'> = {
    imageUrl: '',
    linkTarget: { type: LinkType.None, value: '' },
};

export const ImageAdSettings: React.FC = () => {
    const {
        actions: { setProp },
        props, // Get current props from the node
    } = useNode((node) => ({
        props: node.data.props as ImageAdProps, // Cast to our defined props
    }));
    const intl = useIntl();

    const slides = props.slides || [];

    const addSlide = (url) => {
        const newSlides = [...slides, { ...defaultSlide, imageUrl: url, id: uuidv4() }];
        setProp((props: ImageAdProps) => (props.slides = newSlides));
    };

    const changeSlide = (id, field: keyof ImageAdSlideProps, value: any) => {
        const newSlides = slides.map((slide, index) => {
            if (slide.id === id) {
                return { ...slide, [field]: value };
            }
            return slide;
        });
        setProp((props: ImageAdProps) => (props.slides = newSlides), 500); // Debounce
    };

    const removeSlide = (index: number) => {
        const newSlides = slides.filter((_, i) => i !== index);
        setProp((props: ImageAdProps) => (props.slides = newSlides));
    };

    const handleUploadChange = (info: any) => {
        console.log('handleUploadChange:', info);
        if (info.file.status === 'done') {
            const url = info.file?.response?.data?.[0];
            if (url) {
                addSlide(url);
            } else {
                message.error(info.file?.response?.msg);
            }
        } else if (info.file.status === 'error') {
            console.error("Upload error:", info.file.error);
        }
    };

    const handleReUploadChange = (id: string) => (info: any) => {
        if (info.file.status === 'done') {
            const url = info.file?.response?.data?.[0];
            if (url) {
                changeSlide(id, 'imageUrl', url);
            } else {
                message.error(info.file?.response?.msg);
            }
        } else if (info.file.status === 'error') {
            console.error("Upload error:", info.file.error);
        }
    };

    return (
        <div className="space-y-4">
            <p className="text-sm text-gray-500 mb-4">{intl.formatMessage({ id: 'topic.decoration.imageAd.upload.tips' })}</p>

            {slides.map((slide, index) => (
                <div key={slide.id} className="p-3 border border-solid border-gray-200 rounded-md shadow-sm space-y-3 bg-white">
                    <div className="flex w-full">

                        {/* <Upload
                            listType="picture-card"
                            fileList={[
                                {
                                    uid: slide.id,
                                    name: 'image.jpg',
                                    status: 'done',
                                    url: slide.imageUrl,
                                }
                            ]}
                            name="file" multiple={false}
                            accept=".jpg,.png,.jpeg,.gif,.webp"
                            action="/apigateway/public/upload/object/batch"
                            onChange={handleUploadChange}
                        /> */}


                        <div className='relative w-[100px] h-[100px] p-2 rounded-md border border-solid border-gray-200 flex justify-center items-center'>
                            <img src={slide.imageUrl} className='w-full h-full object-contain' />
                            <Upload
                                {...uploadProps}
                                onChange={handleReUploadChange(slide.id)}
                            >
                                <span
                                    className='absolute bottom-0 left-0 right-0 py-1 text-xs text-white text-center bg-[#0D0D0D73] rounded-sm cursor-pointer'
                                >
                                    {intl.formatMessage({ id: 'topic.decoration.imageAd.reUpload' })}
                                </span>
                            </Upload>
                        </div>

                        <div className='flex-1 ml-2 min-w-0'>
                            <div className='text-gray-500 my-1 flex justify-between'>
                                <span>{intl.formatMessage({ id: 'topic.decoration.moduleTitle.linkSetting' })}</span>
                                <DeleteOutlined className='text-gray-400 hover:text-primary cursor-pointer ml-2' onClick={() => {
                                    removeSlide(index);
                                }} />
                            </div>

                            <LinkSettings
                                name={`slides.${index}`}
                                value={slide.linkTarget}
                                onChange={(value) => changeSlide(slide.id, 'linkTarget', value)}
                            />
                        </div>
                    </div>
                </div>
            ))}

            <div style={{ display: 'flex', gap: '10px', marginBottom: '1rem' }}>
                <Upload
                    {...uploadProps}
                    onChange={handleUploadChange}
                >
                    <Button className='button-outline'> {intl.formatMessage({ id: 'topic.decoration.imageAd.upload' })}</Button>
                </Upload>
            </div>

        </div>
    );
};