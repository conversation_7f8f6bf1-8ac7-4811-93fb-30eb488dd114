import { ReactComponent as ImageIcon } from '@/assets/icons/decoration/image.svg';
import { useNode } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import useEmblaCarousel from 'embla-carousel-react';
import React, { useEffect, useRef, useState } from 'react';
import { ImageAdSlideProps } from './settings'; // We'll define this in settings
import { useDotButton } from './useDotButton';

// Basic styles for Embla Carousel. You might want to move this to a global CSS file.
const emblaStyles = `
.embla {
  overflow: hidden;
  width: 100%;
}
.embla__container {
  display: flex;
}
.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  position: relative;
  display: flex;
}
.embla__slide img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: contain; /* Or 'contain' depending on desired behavior */
}
`;

export interface ImageAdProps {
    slides: ImageAdSlideProps[];
    height?: string; // Optional: to control aspect ratio if needed
    autoplay?: boolean;
    autoplayDelay?: number;
}

export const ImageAdComponent: React.FC<ImageAdProps> = ({
    slides = [],
    height: propHeight = '278px', // Default height, adjust as needed
    autoplay = false,
    autoplayDelay = 4000,
}) => {
    const intl = useIntl();
    const {
        connectors: { connect, drag },
        // selected, // Removed as it's not directly used for this task
        // actions: { setProp }, // Removed as it's not directly used for this task
    } = useNode();

    const [carouselHeight, setCarouselHeight] = useState<string>(propHeight);
    const emblaContainerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (slides.length > 0 && slides[0].imageUrl && emblaContainerRef.current) {
            const img = new Image();
            img.src = slides[0].imageUrl;
            img.onload = () => {
                const containerWidth = emblaContainerRef.current?.offsetWidth || 0;
                if (containerWidth > 0 && img.naturalWidth > 0) {
                    const calculatedHeight = (img.naturalHeight / img.naturalWidth) * containerWidth;
                    setCarouselHeight(`${calculatedHeight}px`);
                } else {
                    setCarouselHeight(propHeight);
                }
            };
            img.onerror = () => {
                // Fallback to default height if image fails to load
                setCarouselHeight(propHeight);
            };
        } else {
            setCarouselHeight(propHeight); // Reset to default if no slides or image
        }
    }, [slides, propHeight, emblaContainerRef.current?.offsetWidth]); // Re-run when container width changes

    const [emblaRef, emblaApi] = useEmblaCarousel({
        loop: slides.length > 1,
        ...(autoplay && {
            // playOnInit: true, // Embla Carousel Autoplay plugin handles this
            delay: autoplayDelay,
        }),
    });

    const { selectedIndex, scrollSnaps, onDotButtonClick } =
        useDotButton(emblaApi)

    if (!slides || slides.length === 0) {
        return (
            <div
                ref={(ref) => connect(drag(ref as HTMLDivElement))}
                className={`relative flex flex-col items-center justify-center text-gray-500 bg-[#f3f5f6]`}
                style={{ minHeight: '200px' }}
            >
                <ImageIcon stroke="#F49C1F" />
                <p className='mt-1 text-sm'>{intl.formatMessage({ id: 'topic.decoration.imageAd.empty' })}</p>
            </div>
        );
    }

    return (
        <>
            <style>{emblaStyles}</style>
            <div
                ref={(ref) => connect(drag(ref as HTMLDivElement))}
                className={`relative w-full`}
            >
                <div className="embla" ref={emblaRef} style={{ height: carouselHeight }}>
                    <div className="embla__container" ref={emblaContainerRef} style={{ height: carouselHeight }}>
                        {slides.map((slide) => (
                            <div className="embla__slide" key={slide.id} style={{ height: carouselHeight }}>
                                {slide.linkTarget && slide.linkTarget.type !== 'none' ? (
                                    <a
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="block w-full h-full"
                                    >
                                        <img src={slide.imageUrl} />
                                    </a>
                                ) : (
                                    <img src={slide.imageUrl} />
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                {slides.length > 1 && (
                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {scrollSnaps.map((_, index) => (
                            <span
                                key={index}
                                onClick={() => onDotButtonClick(index)}
                                className={`w-2 h-2 rounded-full ${selectedIndex === index ? 'bg-[#F49C1F]' : 'bg-gray-300'
                                    }`}
                                aria-label={`Go to slide ${index + 1}`}
                            />
                        ))}
                    </div>
                )}
            </div>
        </>
    );
};
