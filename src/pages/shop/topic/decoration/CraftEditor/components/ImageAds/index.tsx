import { UserComponent } from '@craftjs/core';

import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import { ImageAdComponent, ImageAdProps } from './main';
import { ImageAdSettings } from './settings';



const ImageAd: UserComponent<ImageAdProps> = (props: Partial<ImageAdProps>) => {
    return <div className='p-[10px]'><ImageAdComponent {...props} slides={props.slides || []} /></div>;
};

ImageAd.craft = {
    displayName: 'topic.decoration.icon.imageAd',
    props: { // Default props for a new ImageAd component
        slides: [],
        height: '250px', // Default height of the carousel viewport
        autoplay: false,
        autoplayDelay: 4000,
    } as ImageAdProps,
    related: {
        settings: ImageAdSettings,
    },
    // rules: {
    //     canDrag: () => true,
    //     canMoveIn: () => false, // Usually, an image ad doesn't host other components
    //     canMoveOut: () => true,
    // },
};

export default wrapWithSelectionWrapper(ImageAd);