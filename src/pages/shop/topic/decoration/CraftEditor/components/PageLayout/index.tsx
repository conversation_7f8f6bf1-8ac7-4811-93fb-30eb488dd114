import { DeleteOutlined, EllipsisOutlined } from '@ant-design/icons';
import { useEditor } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import { Tooltip } from 'antd';
import React, { useState } from 'react';
import type { DropResult } from 'react-beautiful-dnd';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import './styles.css';

interface PageLayoutProps {
  className?: string;
}

interface NodeData {
  id: string;
  displayName: string;
  hidden: boolean;
}

// Component to display the page layout with floors
export const PageLayout: React.FC<PageLayoutProps> = ({ className }) => {
  const inlt = useIntl();
  const { actions, query, selectedNodeId, nodes: editorNodes } = useEditor((state, queryHelper) => {
    // Get the ROOT node to find the correct order of children
    const rootNode = state.nodes.ROOT;
    const rootChildren = rootNode?.data?.nodes || [];
    const [currentNodeId] = state.events.selected;

    // Create an array of node data for display in the correct order
    const nodes = rootChildren
      .filter(id => id !== 'ROOT' && state.nodes[id]) // Filter out any invalid nodes
      .map(id => ({
        id,
        displayName: state.nodes[id].data.displayName || state.nodes[id].data.name || 'Component',
        hidden: state.nodes[id].data.hidden || false,
      }));

    return {
      nodes,
      enabled: state.options.enabled,
      selectedNodeId: currentNodeId,
      query: queryHelper,
    };
  });

  const [nodes, setNodes] = useState<NodeData[]>([]);

  // Update nodes when editor state changes
  React.useEffect(() => {
    setNodes(editorNodes || []);
  }, [editorNodes]);

  // Handle drag end from react-beautiful-dnd
  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // Return if dropped outside the list or at the same position
    if (!destination ||
      (destination.droppableId === source.droppableId &&
        destination.index === source.index)) {
      return;
    }

    // Create a copy of the nodes array
    const newNodes = [...nodes];

    // Remove the dragged node
    const [draggedNode] = newNodes.splice(source.index, 1);

    // Insert it at the new position
    newNodes.splice(destination.index, 0, draggedNode);

    // Update the state
    setNodes(newNodes);

    // Update the node order in the editor
    try {
      // Get the node IDs in the new order
      const nodeIds = newNodes.map(node => node.id);

      // Log the reordering operation
      console.log('Reordering nodes:', nodeIds);

      // First, select the ROOT node to ensure we're operating at the right level
      actions.selectNode('ROOT');

      // Move each node to its new position
      // We need to move them in the right order to achieve the desired result
      for (let i = 0; i < nodeIds.length; i++) {
        const nodeId = nodeIds[i];
        const targetIndex = i;

        try {
          // Move the node to the specified index in the ROOT node
          // This is the most direct way to reorder nodes in Craft.js
          actions.move(nodeId, 'ROOT', targetIndex);
        } catch (e) {
          console.log(`Error moving node ${nodeId} to index ${targetIndex}:`, e);
        }
      }

      // Force a re-render of the editor
      // This is a workaround to make sure the canvas updates
      setTimeout(() => {
        // Select the node that was being dragged to maintain selection state
        actions.selectNode(draggedNode.id);
      }, 50);
    } catch (error) {
      console.error('Error updating canvas order:', error);
    }
  };

  // Handle node selection
  const handleSelectNode = (nodeId: string) => {
    actions.selectNode(nodeId);

    // 滚动到对应的节点位置
    setTimeout(() => {
      try {
        const node = query.node(nodeId).get();
        if (node && node.dom) {
          // 使用 scrollIntoView 滚动到节点，并居中显示
          node.dom.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          });
        }
      } catch (error) {
        console.warn('Failed to scroll to node:', error);
      }
    }, 100); // 添加小延迟确保节点选中状态已更新
  };

  // Handle node deletion
  const handleDeleteNode = (e: React.MouseEvent, nodeId: string) => {
    e.stopPropagation();
    actions.delete(nodeId);
  };

  return (
    <div className={`bg-white overflow-hidden w-[170px]`} style={{ borderRight: '1px solid rgba(13, 13, 13, 0.08)' }}>
      <div className="py-3 px-4 border-b border-gray-200">
        <h3 className="module-title">{inlt.formatMessage({ id: 'topic.decoration.title.pageLayout' })}</h3>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="page-floors">
          {(droppableProvided) => (
            <div
              className="py-2"
              ref={droppableProvided.innerRef}
              {...droppableProvided.droppableProps}
            >
              {nodes?.map((node, index) => (
                <Draggable key={node.id} draggableId={node.id} index={index}>
                  {(draggableProvided, snapshot) => (
                    <div
                      ref={draggableProvided.innerRef}
                      {...draggableProvided.draggableProps}
                      {...draggableProvided.dragHandleProps}
                      className={`
                        flex items-center px-4 py-2 cursor-pointer transition-colors
                        border-b border-gray-100
                        ${node.hidden ? 'opacity-60' : ''}
                        ${selectedNodeId === node.id ? 'bg-amber-50' : 'hover:bg-gray-50'}
                        ${snapshot.isDragging ? 'bg-amber-50 shadow-md border border-dashed border-amber-400' : ''}
                      `}
                      onClick={() => handleSelectNode(node.id)}
                    >
                      <div className="text-amber-500 mr-2">
                        <EllipsisOutlined style={{ fontSize: '16px' }} />
                      </div>
                      <div className="flex items-center justify-center w-5 h-5 bg-amber-50 rounded mr-3 text-xs text-amber-500 border border-amber-200">
                        {index + 1}
                      </div>
                      <div className="flex-1 text-sm text-gray-800 whitespace-nowrap overflow-hidden text-ellipsis">
                        {inlt.formatMessage({ id: node.displayName })}
                      </div>
                      {selectedNodeId === node.id && (
                        <Tooltip title={inlt.formatMessage({ id: 'common.button.delete' })}>
                          <DeleteOutlined
                            className="text-gray-400 hover:text-red-500 ml-2"
                            onClick={(e) => handleDeleteNode(e, node.id)}
                          />
                        </Tooltip>
                      )}
                    </div>
                  )}
                </Draggable>
              ))}
              {droppableProvided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};

export default PageLayout;
