import { useNode } from '@craftjs/core';
import React from 'react';
import { ContainerSettings } from './ContainerSettings';

type ContainerProps = {
  background: string;
  padding: number;
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
};

export const Container: React.FC<ContainerProps> = ({
  background = '#ffffff',
  padding = 0,
  children,
  className = '',
  ...props
}) => {
  const {
    connectors: { connect, drag },
  } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref as HTMLElement))}
      style={{ background, padding: `${padding}px` }}
      className={`w-full min-h-[50px] relative ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

// 设置组件在Craft.js中的属性和行为
Container.craft = {
  displayName: 'topic.decoration.icon.container',
  props: {
    background: '#ffffff',
    padding: 0,
  },
  related: {
    settings: ContainerSettings,
  },
  rules: {
    canDrag: () => true,
    canDelete: (node) => {
      // 根容器不能删除，其他容器可以删除
      return !node.data.parent || node.data.parent !== 'ROOT';
    },
  },
};