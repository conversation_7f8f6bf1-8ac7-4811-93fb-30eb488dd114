import { EditorState, Node, NodeId, useEditor } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import React from 'react';

interface SelectedNodeDetails {
  id: NodeId;
  name: string;
  settings: React.ElementType | undefined;
  isDeletable: boolean;
}

export const Settings: React.FC = () => {
  const inlt = useIntl();
  const { actions, selected } = useEditor((state: EditorState, query) => {
    const [currentNodeId] = state.events.selected;
    let selectedNodeDetails: SelectedNodeDetails | null = null;

    if (currentNodeId) {
      const node: Node | undefined = state.nodes[currentNodeId];
      if (node) {
        selectedNodeDetails = {
          id: currentNodeId,
          name: node.data.displayName || node.data.name,
          settings: node.related && node.related.settings,
          isDeletable: query.node(currentNodeId).isDeletable(),
        };
      }
    }
    return {
      selected: selectedNodeDetails,
    };
  });

  return (
    <div className="w-[400px] bg-white p-4 border-l border-gray-200 overflow-y-auto h-full">
      {selected ? (
        <div>
          <div className="pb-3 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800 module-title">{
              inlt.formatMessage({ id: selected.name })
            }</h3>
          </div>

          <div className="space-y-4">
            {selected.settings && React.createElement(selected.settings)}
          </div>
        </div>
      ) : (
        <div className="text-center text-gray-500 mt-20">

        </div>
      )}
    </div>
  );
};