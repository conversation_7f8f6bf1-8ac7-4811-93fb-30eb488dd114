import {
    ProForm,
    ProFormDigit
} from '@ant-design/pro-components';
import { useNode } from '@craftjs/core';
import { DividerProps } from 'antd';
import React from 'react';
import { useIntl } from 'react-intl';

const DividerSettings: React.FC = () => {
    const intl = useIntl();
    const {
        props,
        actions: { setProp },
    } = useNode((node) => ({
        props: node.data.props as DividerProps,
    }));

    console.log('DividerSettings props', props);

    return (
        <ProForm
            layout="vertical"
            submitter={false} // 不显示提交按钮
            initialValues={props} // 从Craft.js节点获取初始值
            onValuesChange={(changedValues, allValues) => {
                // 当表单值变化时，更新Craft.js节点的props
                setProp((draft: DividerProps) => {
                    Object.assign(draft, allValues);
                });
            }}
            className="p-4"
        >
            {/* 上下间距输入框 */}
            <ProFormDigit
                name="verticalSpacing"
                label={intl.formatMessage({ id: 'topic.decoration.divider.padding.vertical' })}
                min={0} // 间距不能为负值
                fieldProps={{
                    addonAfter: 'px'
                }}
            />
            <ProFormDigit
                name="horizontalSpacing"
                label={intl.formatMessage({ id: 'topic.decoration.divider.padding.horizontal' })}
                min={0} // 间距不能为负值
                fieldProps={{
                    addonAfter: 'px'
                }}
            />
        </ProForm>
    );
};

export default DividerSettings;