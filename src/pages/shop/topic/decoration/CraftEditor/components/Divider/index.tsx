import { useNode, type UserComponent } from '@craftjs/core';
import { Divider as AntdDivider } from 'antd'; // 导入Ant Design的Divider，并重命名以避免冲突
import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import DividerSettings from './DividerSettings';

export interface DividerProps {
    horizontalSpacing?: number; // 水平间距，单位像素
    verticalSpacing?: number; // 垂直间距，单位像素
}

const DividerComponent: React.FC<DividerProps> = ({ horizontalSpacing = 5, verticalSpacing = 5 }) => {
    const {
        connectors: { connect, drag },
    } = useNode();

    return <div
        ref={(ref) => connect(drag(ref as HTMLElement))}
        style={{
            paddingTop: `${verticalSpacing}px`,
            paddingBottom: `${verticalSpacing}px`,
            minHeight: `${verticalSpacing * 2}px`, // 确保即使间距很小，组件也有足够的高度被选中和拖拽 (20px 为AntdDivider本身高度的估算)
            paddingLeft: `${horizontalSpacing}px`,
            paddingRight: `${horizontalSpacing}px`,
        }}
    >
        {/* AntdDivider 默认有内边距，所以我们用 !my-0 覆盖掉，让父容器的padding来控制总间距 */}
        <AntdDivider className="!my-0 border-gray-300" />
    </div>
}

const Divider: UserComponent<DividerProps> = (props) => {
    return <DividerComponent {...props} />
};

// 定义默认属性
Divider.craft = {
    displayName: 'topic.decoration.icon.divider',
    props: {
        horizontalSpacing: 10,
        verticalSpacing: 5,
    } as DividerProps,
    related: {
        settings: DividerSettings
    },
};

export default wrapWithSelectionWrapper(Divider);