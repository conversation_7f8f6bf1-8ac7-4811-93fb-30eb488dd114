import { useNode } from '@craftjs/core';
import { Form } from 'antd';

export const ContainerSettings = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props,
  }));

  return (
    <Form layout="vertical" className="space-y-4">
      {/* <Form.Item label="背景颜色">
        <Space>
          <Input
            type="color"
            value={props.background}
            onChange={(e) => setProp((props: any) => (props.background = e.target.value))}
            style={{ width: '50px' }}
          />
          <Input
            value={props.background}
            onChange={(e) => setProp((props: any) => (props.background = e.target.value))}
            placeholder="#ffffff"
          />
        </Space>
      </Form.Item>
      
      <Form.Item label="内边距">
        <InputNumber
          min={0}
          value={props.padding}
          onChange={(value) => setProp((props: any) => (props.padding = value))}
          addonAfter="px"
        />
      </Form.Item> */}
    </Form>
  );
};