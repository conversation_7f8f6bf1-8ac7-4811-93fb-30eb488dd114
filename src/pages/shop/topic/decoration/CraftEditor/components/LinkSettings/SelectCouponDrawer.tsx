import FunProTable from '@/components/common/FunProTable';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { pageQueryCouponTemplateList } from '@/pages/shop/coupon/list/service';
import { CouponStatus } from '@/pages/shop/types/Coupon';
import { Button, Drawer, Form, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useIntl } from 'umi';

interface SelectActivityDrawerProps {
    visible: boolean;
    onClose: () => void;
    onOk: (selectedProducts: GoodsEntity[]) => void;
    multiple?: boolean;
    initialSelectedProducts?: GoodsEntity[];
    selected?: GoodsEntity[];
}
const SelectActivityDrawer = ({
    visible,
    onClose,
    onOk,
    multiple = true,
    initialSelectedProducts = [], // Expects array of product objects { id: '...' }
    selected = [],
}: SelectActivityDrawerProps) => {
    const intl = useIntl();
    const [form] = Form.useForm();

    const [selectedRowKeys, setSelectedRowKeys] = useState(selected);
    const [selectedRowsData, setSelectedRowsData] = useState([]);

    // Populate initial selections
    useEffect(() => {
        if (visible) {
            setSelectedRowKeys(selected);
        }
    }, [visible]);

    const handleOk = () => {
        onOk(selectedRowKeys);
    };

    const handleCancel = () => {
        onClose();
    };

    const rowSelection = {
        type: multiple ? 'checkbox' : 'radio',
        selectedRowKeys,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRowsData(rows);
        },
        preserveSelectedRowKeys: true, // Uncomment if selection should be preserved across filter changes
    };

    const drawerFooter = (
        <div className="text-right">
            <Space>
                <Button onClick={handleCancel}>
                    {intl.formatMessage({ id: 'common.button.cancel' })}
                </Button>
                <Button onClick={handleOk} type="primary">
                    {intl.formatMessage({ id: 'common.button.confirm' })}
                </Button>
            </Space>
        </div>
    );

    return (
        <Drawer
            title={`${intl.formatMessage({ id: 'topic.decoration.linkSetting.select' }, { type: intl.formatMessage({ id: 'topic.decoration.linkSetting.type.coupon' }) })} `}
            width={800} // Adjusted width for better layout
            onClose={handleCancel}
            visible={visible}
            bodyStyle={{ paddingBottom: 53 }} // Account for footer height
            destroyOnClose // Resets form and other states when closed
            footer={drawerFooter}
        >

            <FunProTable<GoodsEntity, any>
                rowKey="couponId"
                requestPage={(params) => pageQueryCouponTemplateList({ ...params, couponStatus: CouponStatus.ACTIVE })}
                search={{
                    layout: 'vertical',
                    defaultCollapsed: false,
                }}
                scroll={{ x: 'max-content' }}

                // formRef={formRef}
                // actionRef={actionRef}
                rowSelection={rowSelection}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        fixed: 'left',
                        width: 40,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.couponId' }),
                        dataIndex: 'couponId',
                        ellipsis: true,
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.couponName' }),
                        dataIndex: 'couponName',
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.totalQuantity' }),
                        dataIndex: 'totalStock',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.remainingQuantity' }),
                        dataIndex: 'useableStock',
                        width: 80,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.coupon.list.validityPeriod' }),
                        dataIndex: 'validityValue',
                        width: 100,
                        hideInSearch: true,
                    },
                ]}
            />
        </Drawer>
    );
};

export default SelectActivityDrawer;
