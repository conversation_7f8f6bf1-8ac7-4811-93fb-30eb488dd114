import FunProTable from '@/components/common/FunProTable';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { queryTopicPage } from '@/pages/shop/topic/list/service';
import { TopicStatus } from '@/pages/shop/types/TopicEnumType';
import { Button, Drawer, Form, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useIntl } from 'umi';


interface SelectTopicDrawerProps {
    visible: boolean;
    onClose: () => void;
    onOk: (selectedProducts: GoodsEntity[]) => void;
    multiple?: boolean;
    initialSelectedProducts?: GoodsEntity[];
    selected?: GoodsEntity[];
}
const SelectTopicDrawer = ({
    visible,
    onClose,
    onOk,
    multiple = true,
    initialSelectedProducts = [], // Expects array of product objects { id: '...' }
    selected = [],
}: SelectTopicDrawerProps) => {
    const intl = useIntl();
    const [form] = Form.useForm();

    const [selectedRowKeys, setSelectedRowKeys] = useState(selected);
    const [selectedRowsData, setSelectedRowsData] = useState([]);
    // Populate initial selections
    useEffect(() => {
        if (visible) {
            setSelectedRowKeys(selected);
        }
    }, [visible]);

    const handleOk = () => {
        onOk(selectedRowKeys);
    };

    const handleCancel = () => {
        onClose();
    };

    const rowSelection = {
        type: 'radio',
        selectedRowKeys,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRowsData(rows);
        },
        preserveSelectedRowKeys: true, // Uncomment if selection should be preserved across filter changes
    };

    const drawerFooter = (
        <div className="text-right">
            <Space>
                <Button onClick={handleCancel}>
                    {intl.formatMessage({ id: 'common.button.cancel' })}
                </Button>
                <Button onClick={handleOk} type="primary">
                    {intl.formatMessage({ id: 'common.button.confirm' })}
                </Button>
            </Space>
        </div>
    );

    return (
        <Drawer
            title={`${intl.formatMessage({ id: 'topic.decoration.linkSetting.select' }, { type: intl.formatMessage({ id: 'topic.decoration.linkSetting.type.topic' }) })}`}
            width={800} // Adjusted width for better layout
            onClose={handleCancel}
            visible={visible}
            bodyStyle={{ paddingBottom: 53 }} // Account for footer height
            destroyOnClose // Resets form and other states when closed
            footer={drawerFooter}
        >

            <FunProTable<GoodsEntity, any>
                rowKey="id"
                requestPage={(params) => queryTopicPage({ ...params, status: TopicStatus.PUBLISHED })}
                search={{
                    layout: 'vertical',
                    defaultCollapsed: false,
                }}
                scroll={{ x: 'max-content' }}
                rowSelection={rowSelection}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        fixed: 'left',
                        width: 40,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.topicPageId' }),
                        dataIndex: 'id',
                        ellipsis: true,
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.topicPageName' }),
                        dataIndex: 'title',
                        width: 80,
                        ellipsis: true
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.topic.list.updateTime' }),
                        dataIndex: 'updateTime',
                        width: 140,
                        hideInSearch: true,
                    },
                ]}
            />
        </Drawer>
    );
};

export default SelectTopicDrawer;
