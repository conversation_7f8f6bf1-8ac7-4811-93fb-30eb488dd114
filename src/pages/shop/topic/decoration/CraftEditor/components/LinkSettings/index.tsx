import { queryGoodsPage } from '@/pages/goods/list/services';
import { queryActivityPage } from '@/pages/shop/activity/list/service';
import { pageQueryCouponTemplateList } from '@/pages/shop/coupon/list/service';
import { queryTopicPage } from '@/pages/shop/topic/list/service';
import { CloseOutlined, DeleteOutlined } from '@ant-design/icons';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Form, Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import SelectActivityDrawer from './SelectActivityDrawer';
import SelectCouponDrawer from './SelectCouponDrawer';
import SelectProductDrawer from './SelectProductDrawer';
import SelectTopicDrawer from './SelectTopicDrawer';


export enum LinkType {
    /**
     * 不跳转
     */
    None = 'none',
    /**
     * 跳商品详情
     * 选择一个具体商品
     */
    Product = 'product',
    /**
     * 跳专题页
     * 选择一个具体专题页
     */
    Topic = 'topic',
    /**
     * 领券
     * 选择一个具体优惠券
     */
    Coupon = 'coupon',
    /**
     * 跳活动详情
     * 选择一个具体活动
     */
    Activity = 'activity',
    /**
     * 跳商品搜索列表
     * 输入关键词
     */
    Keyword = 'keyword',
}



const linkTypeOptions = [
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.none" />, value: LinkType.None },
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.product" />, value: LinkType.Product },
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.topic" />, value: LinkType.Topic },
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.coupon" />, value: LinkType.Coupon },
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.activity" />, value: LinkType.Activity },
    { label: <FormattedMessage id="topic.decoration.linkSetting.type.keyword" />, value: LinkType.Keyword },
];

const linkTypeEnums = {
    [LinkType.None]: <FormattedMessage id="topic.decoration.linkSetting.type.none" />,
    [LinkType.Product]: <FormattedMessage id="topic.decoration.linkSetting.type.product" />,
    [LinkType.Topic]: <FormattedMessage id="topic.decoration.linkSetting.type.topic" />,
    [LinkType.Coupon]: <FormattedMessage id="topic.decoration.linkSetting.type.coupon" />,
    [LinkType.Activity]: <FormattedMessage id="topic.decoration.linkSetting.type.activity" />,
    [LinkType.Keyword]: <FormattedMessage id="topic.decoration.linkSetting.type.keyword" />,
}

export interface LinkTypeValue {
    type?: LinkType;
    id?: string; // id  or keyword
}

interface LinkSettingsProps {
    onDelete?: () => void;
    name?: string;
    label?: string;
    value?: LinkTypeValue;
    onChange?: (value: LinkTypeValue) => void;
}

const LinkSettings = React.forwardRef<HTMLDivElement, LinkSettingsProps>((props: LinkSettingsProps, ref) => {

    const [value, setValue] = useState(props.value);
    const { type: linkType } = value || {};
    const [resultData, setResultData] = useState<{
        name: string;
        id: string;
    }>({});
    const intl = useIntl();


    const [visible, setVisible] = useState(false);

    useEffect(() => {
        props.onChange?.(value);
        console.log(value);
        if (value?.type === LinkType.Product && value?.id) {
            queryGoodsPage({
                itemIdList: [value.id],
                pageNo: 1,
                pageSize: 1
            }).then((result) => {
                setResultData(result.data?.map(item => ({ ...item, name: item.itemName, id: item.itemId }))?.[0]);
            });
        }
        if (value?.type === LinkType.Coupon && value?.id) {
            pageQueryCouponTemplateList({
                ids: [value.id]
            }).then((result) => {
                console.log(result);
                setResultData(result.data?.map(item => ({ ...item, name: item.couponName, id: item.couponId }))?.[0]);
            });
        }
        if (value?.type === LinkType.Topic && value?.id) {
            queryTopicPage({
                ids: [value.id]
            }).then((result) => {
                setResultData(result.data?.map(item => ({ ...item, name: item.title }))?.[0]);
            });
        }
        if (value?.type === LinkType.Activity && value?.id) {
            queryActivityPage({
                activityIds: [value.id]
            }).then((result) => {
                setResultData(result.data?.map(item => ({ ...item, id: item.activityId, name: item.activityName }))?.[0]);
            });
        }
    }, [value]);


    const selectedDrawerProps = {
        selected: value?.id ? [value.id as any] : [],
        onClose: () => setVisible(false),
        onOk: (selected) => {
            console.log('selected', selected);
            setValue(prevValue => ({ ...prevValue, id: selected[0] as unknown as string }));
            setVisible(false);
        }
    }

    return (
        <>
            <Form.Item  {...props} style={{ marginBottom: 0 }}>
                <div className="flex items-center gap-2 w-full">
                    <Select
                        options={linkTypeOptions}
                        value={linkType}
                        className='flex-1 min-w-0'
                        placeholder={intl.formatMessage({ id: 'topic.decoration.linkSetting.type.placeholder' })}
                        onChange={(type => {
                            setValue({ type })
                            setResultData({});
                        })}
                    />
                    {props.onDelete && <DeleteOutlined className='text-gray-400 hover:text-primary cursor-pointer flex-shrink-0' onClick={props.onDelete} />}
                </div>

                {resultData?.id ? (
                    <div className='px-2 py-1 mt-2 bg-[#080D0D0D] rounded w-full flex justify-between items-center min-w-0 border'>
                        <div className='flex-1 truncate'>
                            {resultData.id} | {resultData.name}
                        </div>
                        <CloseOutlined className='text-gray-400 hover:text-primary cursor-pointer ml-2 flex-shrink-0' onClick={() => {
                            setResultData({})
                            setValue(prevValue => ({ ...prevValue, id: undefined }))
                        }} />
                    </div>
                ) : (
                    linkType && ![LinkType.None, LinkType.Keyword].includes(linkType) && (
                        <a className='text-primary hover:text-primary mt-2 inline-block' onClick={() => setVisible(true)}>
                            {intl.formatMessage({ id: 'topic.decoration.linkSetting.select' }, { type: linkTypeEnums[linkType] })}
                        </a>
                    )
                )}
                {linkType === LinkType.Keyword && (
                    <Input
                        name="keyword"
                        className='mt-4'
                        placeholder={intl.formatMessage({ id: 'topic.decoration.linkSetting.keyword.placeholder' })}
                        value={value?.id}
                        onChange={(e) => {
                            setValue(prevValue => ({ ...prevValue, id: e.target.value }));
                        }}
                    />
                )}
            </Form.Item>

            <SelectProductDrawer
                multiple={false}
                visible={linkType === LinkType.Product && visible}
                {...selectedDrawerProps}
            />

            <SelectActivityDrawer
                multiple={false}
                visible={linkType === LinkType.Activity && visible}
                {...selectedDrawerProps}
            />

            <SelectCouponDrawer
                multiple={false}
                visible={linkType === LinkType.Coupon && visible}
                {...selectedDrawerProps}
            />

            <SelectTopicDrawer
                multiple={false}
                visible={linkType === LinkType.Topic && visible}
                {...selectedDrawerProps}
            />
        </>

    );
});

export default LinkSettings;
