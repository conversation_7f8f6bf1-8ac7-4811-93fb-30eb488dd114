import FunProTable from '@/components/common/FunProTable';
import { queryGoodsPage } from '@/pages/goods/list/services';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import { ActionType } from '@ant-design/pro-components';
import { Button, Checkbox, Drawer, Form, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import { useIntl } from 'umi';

interface SelectProductDrawerProps {
    visible: boolean;
    onClose: () => void;
    onOk: (selectedProductIds: string[], selectedProducts: GoodsEntity[]) => void;
    multiple?: boolean;
    initialSelectedProducts?: GoodsEntity[];
    selected?: string[];
}
const SelectProductDrawer = ({
    visible,
    onClose,
    onOk,
    multiple = true,
    initialSelectedProducts = [], // Expects array of product objects { id: '...' }
    selected = [],
}: SelectProductDrawerProps) => {
    const intl = useIntl();
    const [form] = Form.useForm();

    const [selectedRowKeys, setSelectedRowKeys] = useState(selected);
    const [selectedRowsData, setSelectedRowsData] = useState([]);

    const [onlyActivity, setOnlyActivity] = useState(false);
    const actionRef = React.useRef<ActionType>();

    // Populate initial selections
    useEffect(() => {
        if (visible) {
            setSelectedRowKeys(selected);
        }
    }, [visible]);

    const handleOk = () => {
        onOk(selectedRowKeys, selectedRowsData);
    };

    const handleCancel = () => {
        onClose();
    };

    const rowSelection = {
        type: multiple ? 'checkbox' : 'radio',
        selectedRowKeys,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRowsData(rows);
        },
        preserveSelectedRowKeys: true, // Uncomment if selection should be preserved across filter changes
    };

    const drawerFooter = (
        <div className="text-right">
            <Space>
                <Button onClick={handleCancel}>
                    {intl.formatMessage({ id: 'common.button.cancel' })}
                </Button>
                <Button onClick={handleOk} type="primary">
                    {intl.formatMessage({ id: 'common.button.confirm' })}
                </Button>
            </Space>
        </div>
    );

    return (
        <Drawer
            title={`${intl.formatMessage({ id: 'topic.decoration.linkSetting.select' }, { type: intl.formatMessage({ id: 'topic.decoration.linkSetting.type.product' }) })}`}
            width={800} // Adjusted width for better layout
            onClose={handleCancel}
            visible={visible}
            bodyStyle={{ paddingBottom: 53 }} // Account for footer height
            destroyOnClose // Resets form and other states when closed
            footer={drawerFooter}
        >

            <FunProTable<GoodsEntity, any>
                rowKey="itemId"
                requestPage={queryGoodsPage}
                search={{
                    layout: 'vertical',
                    defaultCollapsed: false,
                }}
                scroll={{ x: 'max-content' }}
                params={{
                    onlyActivity,
                    itemStatus: '1'
                }}
                headerTitle={
                    <Space>
                        <Checkbox checked={onlyActivity} onChange={() => setOnlyActivity(!onlyActivity)}>
                            {intl.formatMessage({ id: 'topic.decoration.productList.settings.filter.onlyActive' })}
                        </Checkbox>
                    </Space>
                }
                // formRef={formRef}
                actionRef={actionRef}
                rowSelection={rowSelection}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        fixed: 'left',
                        width: 40,
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
                        dataIndex: 'itemSn',
                        ellipsis: true,
                        width: 80,
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
                        dataIndex: 'itemName',
                        width: 80,
                        ellipsis: true
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.brand' }),
                        dataIndex: 'brandId',
                        valueType: 'select',
                        hideInTable: true,
                        fieldProps: {
                            showSearch: true,
                            filterOption: false,
                            optionRender: (option: any) => (
                                <Space>
                                    {option.data.label}
                                </Space>
                            ),
                        },
                        request: async ({ keyWords: brandName }) => {
                            const { data } = await queryGoodsPropertyPage(
                                { brandName, pageNo: 1, pageSize: 1000 },
                                'brand',
                            );
                            return data.map((t) => ({
                                label: t.brandName,
                                dataType: t.dataType,
                                value: t.brandId,
                            }));
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.brandName' }),
                        dataIndex: 'brandName',
                        width: 100,
                        ellipsis: true,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.category' }),
                        dataIndex: 'categoryName',
                        width: 100,
                        ellipsis: true,
                        hideInSearch: true,
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.categoryName' }),
                        dataIndex: 'categoryId',
                        hideInTable: true,
                        valueType: 'treeSelect',
                        fieldProps: {
                            treeCheckable: true,
                            maxTagCount: 3,
                            filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
                        },
                        request: () => {
                            return queryGoodsPropertyPage(
                                { pageSize: 999, pageNo: 1, isReturnTree: true },
                                'category',
                            ).then((result) => transformCategoryTree(result.data));
                        },
                    },
                    {
                        title: intl.formatMessage({ id: 'goods.list.table.suggestPrice' }),
                        dataIndex: 'suggestPrice',
                        valueType: {
                            type: "money",
                            locale: "en-US"
                        },
                        width: 80,
                        hideInSearch: true,
                    },
                ]}
            />
        </Drawer>
    );
};

export default SelectProductDrawer;
