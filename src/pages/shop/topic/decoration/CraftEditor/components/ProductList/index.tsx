import type { UserComponent } from '@craftjs/core';
import { useNode } from '@craftjs/core';
import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import ProductListDisplay from './Display';
import ProductListSettings from './Settings';
import type { ProductListProps } from './types';


const ProductList: UserComponent<ProductListProps> = (props: Partial<ProductListProps>) => {
  const {
    connectors: { connect, drag },
  } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref as HTMLDivElement))}
      className="w-full p-[10px]"
      style={{ position: 'relative' }}
    >
      <ProductListDisplay
        listStyle={props.listStyle || 'two-per-row'}
        productType={props.productType || 'single'}
        productSource={props.productSource}
        specifiedProductIds={props.specifiedProductIds}
        conditions={props.conditions}
        groups={props.groups}
        activeGroupKey={props.activeGroupKey}
      />
    </div>
  );
};

ProductList.craft = {
  displayName: 'topic.decoration.icon.productList',
  props: {
    listStyle: 'two-per-row',
    productType: 'single',
    productSource: 'specified',
    specifiedProductIds: [],
    groups: [],
    displayCountType: 'count',
    displayCount: 5,
  },
  related: {
    settings: ProductListSettings,
  },
  // Make sure this component is selectable and draggable
  rules: {
    canDrag: () => true,
  },
};

export default wrapWithSelectionWrapper(ProductList);
