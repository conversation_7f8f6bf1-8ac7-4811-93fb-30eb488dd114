
.list-style-radio-group{
    .ant-radio.ant-wave-target{
        display: none;
    }

    .ant-radio-wrapper-checked{
        .product-list-settings-style{
            color: #F49C1F;
            &.one-per-row {
                &::before {
                    background-image: url('@/assets/icons/decoration/one-per-row-active.svg');
                }
            }
        
            &.two-per-row {
                &::before {
                    background-image: url('@/assets/icons/decoration/two-per-row-active.svg');
                }
            }
        
            &.horizontal-scroll {
                &::before {
                    background-image: url('@/assets/icons/decoration/horizontal-scroll-active.svg');
                }
            }
        }
    }
}

.product-list-settings-style{
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding-top: 40px;
    color: rgba(13, 13, 13, 0.60);
    font-size: 12px;

    &:hover{
        transition: all .3s;
        color: #F49C1F;
    }

    &::before, &::after {
        content: '';
        display: block;
        width: 32px;
        height: 32px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        transition: opacity 0.3s ease;
    }

    &::before {
        opacity: 1;
    }

    &::after {
        opacity: 0;
    }

    &:hover::before {
        opacity: 0;
    }

    &:hover::after {
        opacity: 1;
    }

    // 为每个样式类添加特定的背景图片
    &.one-per-row {
        &::before {
            background-image: url('@/assets/icons/decoration/one-per-row.svg');
        }

        &::after {
            background-image: url('@/assets/icons/decoration/one-per-row-active.svg');
        }
    }

    &.two-per-row {
        &::before {
            background-image: url('@/assets/icons/decoration/two-per-row.svg');
        }

        &::after {
            background-image: url('@/assets/icons/decoration/two-per-row-active.svg');
        }
    }

    &.horizontal-scroll {
        &::before {
            background-image: url('@/assets/icons/decoration/horizontal-scroll.svg');
        }

        &::after {
            background-image: url('@/assets/icons/decoration/horizontal-scroll-active.svg');
        }
    }
}