// src/components/ProductListDisplay.tsx
import EmptyProduct from '@/assets/imgs/empty-product.svg';
import { Button } from "@/components/ui/button";
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { Empty, Tabs } from 'antd'; // If using grouped type
import React from 'react';
import useLoadProducts from '../../hooks/useLoadProducts';
import { ProductGroup, ProductListProps } from './types'; // Adjust path


interface ProductCardProps {
    product: GoodsEntity;
    listStyle: ProductListProps['listStyle'];
}

const ProductCard: React.FC<ProductCardProps> = ({ product, listStyle = 'two-per-row' }) => {
    const styleClasses = {
        'one-per-row': 'w-full flex mb-3 border border-gray-200 rounded',
        'two-per-row': 'w-[calc(50%-0.5rem)] md:w-[calc(50%-0.5rem)] border border-gray-200 rounded', // Tailwind JIT needs exact values for calc sometimes
        'horizontal-scroll': 'w-36 sm:w-40 flex-shrink-0 border border-gray-200 rounded mr-3',
    };
    const imageContainerClasses = {
        'one-per-row': 'w-24 h-24 mr-3 flex-shrink-0',
        'two-per-row': 'w-full h-32 mb-2',
        'horizontal-scroll': 'w-full h-32 mb-2',
    };

    return (
        <div className={`bg-white ${styleClasses[listStyle]}`}>
            <div className={listStyle === 'one-per-row' ? 'flex w-full' : 'block w-full'}>
                <div className={`${imageContainerClasses[listStyle]} overflow-hidden`}>
                    {
                        product.images?.length > 0 ?
                            <img src={product.images[0]} alt={product.itemName} className="w-full h-full object-cover" />
                            :
                            <img src={EmptyProduct} alt={product.itemName} className="w-full h-full object-cover" />
                    }
                </div>
                <div className={listStyle === 'one-per-row' ? 'flex flex-col justify-between flex-grow' : ''}>
                    <h3 className={`text-sm font-medium text-gray-800 h-[36px] line-clamp-2`}>
                        {product.itemName}
                    </h3>
                    <div className='flex justify-between'>
                        <div>
                            <span className="font-semibold text-md mr-1">${product.suggestPrice}</span>
                            {/* {product.originalPrice && <span className="text-gray-400 line-through text-xs">¥{product.originalPrice}</span>} */}
                        </div>
                        {listStyle === 'one-per-row' && <ShoppingCartOutlined className='text-primary' />}
                    </div>
                    {listStyle !== 'one-per-row' && ( // Example "Add" button for other styles
                        <Button variant={'outline'} className="w-full" >
                            Add
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};


const ProductListContent: React.FC<Pick<ProductListProps, 'listStyle' | 'productSource' | 'specifiedProductIds' | 'conditions'>> = (props) => {
    const { listStyle } = props;
    const { products, loading } = useLoadProducts(props);


    const containerClasses = {
        'one-per-row': 'flex flex-col',
        'two-per-row': 'flex flex-wrap gap-4 justify-between', // gap-x-4, gap-y-4
        'horizontal-scroll': 'flex overflow-x-auto pb-3 -mb-3', // pb-3 for scrollbar space
    };

    return (
        <div className={`${containerClasses[listStyle]}`}>
            {
                products.length > 0 ? products.map(product => (
                    <ProductCard key={product.id} product={product} listStyle={listStyle} />
                )) : <div className='flex justify-center items-center w-full h-full'>
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
            }
            {/* For horizontal scroll, you might want a "View All" if too many items */}
        </div>
    );
};


const ProductListDisplay: React.FC<ProductListProps> = (props) => {
    const {
        listStyle = 'two-per-row',
        productType = 'single',
        productSource,
        specifiedProductIds,
        conditions,
        groups = [],
        activeGroupKey,
    } = props;

    console.log('ProductListDisplay props', props)


    if (productType === 'grouped') {
        if (!groups || groups.length === 0) {
            return (
                <div className="p-4 text-center text-gray-400">
                    请先在设置中添加商品分组
                </div>
            );
        }
        return (
            <div style={{ width: '100%' }}>
                <Tabs defaultActiveKey={activeGroupKey || groups[0]?.id}>
                    {groups.map((group: ProductGroup) => (
                        <Tabs.TabPane tab={group.title} key={group.id}>
                            <ProductListContent
                                listStyle={listStyle}
                                productSource={group.productSource}
                                specifiedProductIds={group.specifiedProductIds}
                                conditions={group.conditions}
                            />
                        </Tabs.TabPane>
                    ))}
                </Tabs>
            </div>
        );
    }

    // Single type
    return (
        <div style={{ width: '100%' }}>
            <ProductListContent
                listStyle={listStyle}
                productSource={productSource}
                specifiedProductIds={specifiedProductIds}
                conditions={conditions}
            />
        </div>
    );
};

export default ProductListDisplay;