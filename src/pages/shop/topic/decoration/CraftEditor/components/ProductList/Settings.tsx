import EmptyProduct from '@/assets/imgs/empty-product.svg';
import ProFormBrand from '@/components/ProFormItem/ProFormBrand';
import ProFormCategory from '@/components/ProFormItem/ProFormCategory';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { DeleteOutlined } from '@ant-design/icons';
import { ProForm, ProFormDigit, ProFormRadio, ProFormSelect, ProFormSwitch } from '@ant-design/pro-components'; // For conditional fields
import { useNode } from '@craftjs/core';
import { useIntl } from '@umijs/max';
import { Button, Form, Input, Radio, Space, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import useLoadProducts from '../../hooks/useLoadProducts';
import SelectProductDrawer from '../LinkSettings/SelectProductDrawer';
import './index.scss';
import type { ProductGroup, ProductListConditions, ProductListProps } from './types'; // Adjust path


const MOCK_TAGS = [{ value: 'tag1', label: '热销' }, { value: 'tag2', label: '新品' }, { value: 'tag3', label: '推荐' }];

const ConditionalProductSettings: React.FC<{
    value: ProductListConditions;
    onChange: (newConditions: ProductListConditions) => void;
}> = (props) => {
    const { value, onChange } = props;
    const { listStyle } = useNode<ProductListProps>(state => state.data.props);
    const [form] = Form.useForm();
    const intl = useIntl();

    React.useEffect(() => {
        form.setFieldsValue(value);
    }, [value, form]);

    const handleValuesChange = (changeValues: any, allValues: any) => {
        onChange(allValues);
    };

    return (
        <ProForm
            form={form}
            initialValues={value}
            submitter={false}
            onValuesChange={handleValuesChange}
        >
            <h5 className='text-base mt-4 mb-2'>{intl.formatMessage({ id: 'topic.decoration.productList.settings.conditionsTitle' })}</h5>
            <ProFormBrand name="brandIdList" />
            <ProFormCategory name="categoryIdList" />
            <ProFormSelect
                name="tagIds"
                label={intl.formatMessage({ id: 'topic.decoration.productList.settings.productTag' })}
                options={MOCK_TAGS}
                mode="multiple"
                placeholder={intl.formatMessage({ id: 'topic.decoration.productList.settings.selectTag' })}
                fieldProps={{ style: { width: '100%' } }}
            />
            <ProFormSwitch
                fieldProps={{
                    className: 'form-item-switch'
                }}
                name="onlyActivity" label={intl.formatMessage({ id: 'topic.decoration.productList.settings.onlyCampaignProducts' })}
            />

            <ProFormRadio.Group label={intl.formatMessage({ id: 'topic.decoration.productList.settings.displayCount' })} name="displayCountType"
                options={
                    [
                        {
                            label: <div className='flex items-center'>
                                <span>{intl.formatMessage({ id: 'topic.decoration.productList.settings.display' })}</span> <ProFormDigit
                                    name="displayCount"
                                    disabled={value.displayCountType === 'all'}
                                    min={1}
                                    max={100}
                                    formItemProps={{ style: { marginBottom: 0 } }}
                                    fieldProps={{
                                        style: { width: 80, marginLeft: 8 },
                                        precision: 0,
                                    }}
                                    placeholder={intl.formatMessage({ id: 'topic.decoration.productList.settings.countPlaceholder' })}
                                />
                            </div>,
                            value: 'count'
                        },
                        { label: intl.formatMessage({ id: 'topic.decoration.productList.settings.displayAll' }), value: 'all' }
                    ].filter(item => {
                        if (listStyle === 'horizontal-scroll') {
                            return item.value === 'count'
                        }
                        return true
                    })
                }
            />
        </ProForm>
    );
};


const SpecifiedProducts: React.FC<{
    onSelect: () => void;
    specifiedProductIds?: string[];
    onDel: (product: GoodsEntity) => void;
}> = (props) => {
    const { specifiedProductIds = [] } = props;
    const intl = useIntl();
    const { products } = useLoadProducts({
        productSource: 'specified',
        specifiedProductIds,
    });

    return <div>
        <Button className='button-outline' onClick={props.onSelect}>{intl.formatMessage({ id: 'topic.decoration.productList.settings.selectProduct' })}</Button>
        <p className="text-xs text-gray-500 mt-1">{intl.formatMessage({ id: 'topic.decoration.productList.settings.selectedCount' }, { count: specifiedProductIds?.length || 0 })}</p>
        {
            specifiedProductIds.length > 0 && products.map(product => (
                <Space direction="vertical" key={product.skuId} className='flex gap-2 mt-2'>
                    <div className='flex'>
                        <div className='flex-1'>
                            <img src={product.images?.[0] ?? EmptyProduct} className='w-[48px] h-[48px] object-cover' alt={product.itemName} />
                            <span className='ml-2'>{product.itemId} | {product.itemName}</span>
                        </div>
                        <DeleteOutlined className='text-gray-400 hover:text-primary cursor-pointer' onClick={() => {
                            props.onDel(product);
                        }} />
                    </div>
                </Space>
            ))
        }
    </div>
}

const SingleProductSourceSettings: React.FC<{
    onSelect: () => void;
}> = (props) => {
    const intl = useIntl();
    const {
        actions: { setProp },
        productSource, specifiedProductIds = [], conditions, displayCountType, displayCount
    } = useNode<ProductListProps>(state => state.data.props);

    return (
        <>
            <ProFormRadio.Group
                label={intl.formatMessage({ id: 'topic.decoration.productList.settings.productSource' })}
                name="productSource"
                options={[
                    {
                        value: 'specified',
                        label: intl.formatMessage({ id: 'topic.decoration.productList.settings.specifiedProducts' }),
                    },
                    {
                        value: 'conditional',
                        label: intl.formatMessage({ id: 'topic.decoration.productList.settings.conditionalProducts' }),
                    },
                ]}
            />
            {productSource === 'specified' && (
                <SpecifiedProducts
                    onSelect={props.onSelect}
                    specifiedProductIds={specifiedProductIds}
                    onDel={(product) => {
                        setProp((props: ProductListProps) => props.specifiedProductIds = props.specifiedProductIds?.filter(id => id !== product.itemId));
                    }}
                />
            )}
            {productSource === 'conditional' && (
                <ConditionalProductSettings
                    value={{ ...conditions, displayCountType, displayCount }}
                    onChange={newConditions => setProp(props => props.conditions = newConditions)}
                />
            )}
        </>
    )
}

const ProductListSettings: React.FC = () => {
    const intl = useIntl();
    const {
        actions: { setProp },
        listStyle, productType, productSource, specifiedProductIds = [], conditions, groups = [], activeGroupKey, displayCountType, displayCount
    } = useNode<ProductListProps>(state => state.data.props);
    const [form] = Form.useForm();

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedProductsInModal, setSelectedProductsInModal] = useState<string[]>([]);
    const [currentGroupForModal, setCurrentGroupForModal] = useState<string | null>(null); // 'root' for single, or group.id

    const openProductSelectorModal = (target: 'root' | string) => {
        setCurrentGroupForModal(target);
        if (target === 'root') {
            setSelectedProductsInModal(specifiedProductIds || []);
        } else {
            const group = groups.find(g => g.id === target);
            setSelectedProductsInModal(group?.specifiedProductIds || []);
        }
        setIsModalVisible(true);
    };

    const handleModalOk = (selectedProductsInModal) => {
        if (currentGroupForModal === 'root') {
            setProp((props: ProductListProps) => props.specifiedProductIds = [...selectedProductsInModal]);
        } else {
            setProp((props: ProductListProps) => {
                props.groups = (props.groups || []).map((g: ProductGroup) =>
                    g.id === currentGroupForModal ? { ...g, specifiedProductIds: [...selectedProductsInModal] } : g
                );
            });
        }
        setIsModalVisible(false);
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
    };

    useEffect(() => {
        console.log('specifiedProductIds', specifiedProductIds);
    }, [specifiedProductIds]);


    // For Grouped Tabs
    const onEditTabs = (targetKey: any, action: 'add' | 'remove') => {
        if (action === 'add') {
            const newGroupId = uuidv4();
            const newGroup: ProductGroup = {
                id: newGroupId,
                title: intl.formatMessage({ id: 'topic.decoration.productList.settings.newGroup' }, { number: groups.length + 1 }),
                productSource: 'specified',
                specifiedProductIds: [],
                conditions: {
                    displayCountType: 'count',
                    displayCount: 5,
                }
            };
            setProp((props: ProductListProps) => props.groups = [...(props.groups || []), newGroup]);
            setProp((props: ProductListProps) => props.activeGroupKey = newGroupId);
        } else { // remove
            setProp((props: ProductListProps) => props.groups = (props.groups || []).filter((g: ProductGroup) => g.id !== targetKey));
            if (activeGroupKey === targetKey) {
                setProp((props: ProductListProps) => props.activeGroupKey = (props.groups || [])?.[0]?.id || undefined);
            }
        }
    };

    const handleGroupTitleChange = (groupId: string, newTitle: string) => {
        setProp((props: ProductListProps) => {
            props.groups = (props.groups || []).map((g: ProductGroup) =>
                g.id === groupId ? { ...g, title: newTitle } : g
            );
        });
    };

    const handleValuesChange = (changeValues: any, allValues: any) => {
        setProp((props: ProductListProps) => {
            Object.keys(changeValues).forEach((key) => {
                props[key] = changeValues[key];
            });
            return props;
        });

    };

    return (
        <div className='space-y-4 p-2'>
            <ProForm
                form={form}
                layout="vertical"
                onValuesChange={handleValuesChange}
                initialValues={{
                    listStyle: listStyle,
                    productType: productType,
                    productSource: productSource,
                    specifiedProductIds: specifiedProductIds,
                    conditions: conditions,
                    groups: groups,
                    activeGroupKey: activeGroupKey,
                    displayCountType: displayCountType,
                    displayCount: displayCount,
                }}
                submitter={false}
                className="conditional-product-settings-form"
            >
                <ProFormRadio.Group
                    fieldProps={{
                        className: 'list-style-radio-group'
                    }}
                    label={intl.formatMessage({ id: 'topic.decoration.productList.settings.listStyle' })}
                    name='listStyle'
                    options={[
                        {
                            value: 'one-per-row',
                            label: <div className='product-list-settings-style one-per-row'>
                                {intl.formatMessage({ id: 'topic.decoration.productList.settings.onePerRow' })}
                            </div>
                        },
                        {
                            value: 'two-per-row',
                            label: <div className='product-list-settings-style two-per-row'>
                                {intl.formatMessage({ id: 'topic.decoration.productList.settings.twoPerRow' })}
                            </div>
                        },
                        {
                            value: 'horizontal-scroll',
                            label: <div className='product-list-settings-style horizontal-scroll'>
                                {intl.formatMessage({ id: 'topic.decoration.productList.settings.horizontalScroll' })}
                            </div>
                        },
                    ]} />
                <ProFormRadio.Group
                    label={intl.formatMessage({ id: 'topic.decoration.productList.settings.type' })}
                    name="productType"
                    options={[
                        {
                            value: 'single',
                            label: intl.formatMessage({ id: 'topic.decoration.productList.settings.product' }),
                        },
                        {
                            value: 'grouped',
                            label: intl.formatMessage({ id: 'topic.decoration.productList.settings.productGroup' }),
                        },
                    ]}
                />

                {productType === 'single' && <SingleProductSourceSettings onSelect={() => openProductSelectorModal('root')} />}

                {productType === 'grouped' && (
                    <div>
                        <Tabs
                            type="editable-card"
                            activeKey={activeGroupKey}
                            onChange={activeKey => setProp((props: ProductListProps) => props.activeGroupKey = activeKey)}
                            onEdit={onEditTabs}
                            hideAdd={groups.length >= 5} // Example: limit to 5 groups
                        >
                            {groups.map((group: ProductGroup) => (
                                <Tabs.TabPane
                                    tab={
                                        <Input
                                            size="small"
                                            value={group.title}
                                            onChange={(e) => handleGroupTitleChange(group.id, e.target.value)}
                                            onBlur={(e) => handleGroupTitleChange(group.id, e.target.value.trim() || intl.formatMessage({ id: 'topic.decoration.productList.settings.group' }))}
                                            style={{ width: 100, border: activeGroupKey === group.id ? undefined : 'none' }}
                                        />
                                    }
                                    key={group.id}
                                    closable={groups.length > 1} // Can't close last group
                                >
                                    <div className="space-y-3 p-2">
                                        <Form.Item label={intl.formatMessage({ id: 'topic.decoration.productList.settings.productSource' })} labelCol={{ span: 24 }}>
                                            <Radio.Group
                                                value={group.productSource}
                                                onChange={e => setProp((props: ProductListProps) => {
                                                    props.groups = (props.groups || []).map((g: ProductGroup) =>
                                                        g.id === group.id ? { ...g, productSource: e.target.value } : g
                                                    );
                                                })}
                                            >
                                                <Radio value="specified">{intl.formatMessage({ id: 'topic.decoration.productList.settings.specifiedProducts' })}</Radio>
                                                <Radio value="conditional">{intl.formatMessage({ id: 'topic.decoration.productList.settings.conditionalProducts' })}</Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                        {group.productSource === 'specified' && (
                                            <SpecifiedProducts
                                                specifiedProductIds={group.specifiedProductIds}
                                                onSelect={() => openProductSelectorModal(group.id)}
                                                onDel={(prodct) => setProp((props: ProductListProps) => {
                                                    props.groups = (props.groups || []).map((g: ProductGroup) =>
                                                        g.id === group.id ? { ...g, specifiedProductIds: g.specifiedProductIds?.filter(id => id !== prodct.itemId) } : g
                                                    );
                                                })}
                                            />
                                        )}
                                        {group.productSource === 'conditional' && (
                                            <ConditionalProductSettings
                                                value={group.conditions ?? {}}
                                                onChange={newConditions => setProp((props: ProductListProps) => {
                                                    props.groups = (props.groups || []).map((g: ProductGroup) =>
                                                        g.id === group.id ? { ...g, conditions: newConditions } : g
                                                    );
                                                })}
                                            />
                                        )}
                                    </div>
                                </Tabs.TabPane>
                            ))}
                        </Tabs>
                    </div>
                )}
            </ProForm>

            <SelectProductDrawer selected={selectedProductsInModal}
                visible={isModalVisible} onClose={handleModalCancel} onOk={handleModalOk} />
        </div>
    );
};


export default ProductListSettings;