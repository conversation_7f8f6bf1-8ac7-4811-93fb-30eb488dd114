// src/components/Coupon/index.tsx (示例路径)
import React from 'react';
import { wrapWithSelectionWrapper } from '../../utils/wrapComponent';
import CouponDisplay from './CouponDisplay';
import CouponSettings from './CouponSettings';
import type { CouponProps } from './type';





const Coupon: React.FC<CouponProps> = (props) => {
    return (
        <div className='p-[10px]'><CouponDisplay {...props} /></div>
    );
};

// 定义默认属性
Coupon.craft = {
    displayName: 'topic.decoration.icon.coupon',
    props: {
        couponsIds: [],
    } as CouponProps,
    related: {
        settings: CouponSettings
    },
};


export default wrapWithSelectionWrapper(Coupon);