// src/types/Coupon.d.ts (示例路径)

export interface CouponItem {
    couponId: string; // 优惠券唯一ID
    couponName: string; // 优惠券名称
    enableAmount: number; // 使用门槛，例如 1000
    couponAmount: string; //优惠金额
    totalQuantity: number; // 总数量
    remainingQuantity: number; // 剩余数量
    validityPeriod: string; // 有效期
    status: string; // 状态
    updater: string; // 更新人  
    updateTime: string; // 更新时间
}


export interface CouponProps {
    couponsIds: string[];
}