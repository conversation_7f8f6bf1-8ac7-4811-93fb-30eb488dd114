// src/components/Coupon/CouponSettings.tsx (示例路径)
import { CloseOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import { useNode } from '@craftjs/core';
import { Button, Space } from 'antd';
import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import useLoadCoupons from '../../hooks/useLoadCoupons';
import SelectCouponDrawer from '../LinkSettings/SelectCouponDrawer';
import { CouponProps } from './type';

const CouponSettings: React.FC = () => {
    const [visible, setVisible] = useState(false);
    const intl = useIntl();
    const {
        props,
        actions: { setProp },
    } = useNode((node) => ({
        props: node.data.props,
    }));

    const { coupons } = useLoadCoupons({ couponsIds: props.couponsIds });

    const handleSelectCoupon = (ids: string[]) => {
        console.log('coupon', ids);
        setVisible(false);
        setProp((draft) => {
            draft.couponsIds = ids;
        });
    };

    // 移除优惠券
    const handleRemoveCoupon = (id: string) => {
        setProp((draft: CouponProps) => {
            draft.couponsIds = draft.couponsIds.filter((coupon) => coupon !== id);
        });
    };

    return (
        <>
            <ProForm
                layout="vertical"
                submitter={false} // 不显示提交按钮
                initialValues={props} // 初始值来自Craft.js节点
                className="p-4"
            >
                <ProForm.Item label={intl.formatMessage({ id: 'topic.decoration.coupon.management' })}>
                    <Space direction="vertical" className="w-full">
                        {coupons.map((coupon) => (
                            <div
                                key={coupon.couponId}
                                className="bg-[#F3F5F6] flex  py-2 px-3 text-base" // 增加Tag的点击区域和视觉大小
                            >
                                <span className='flex-1 min-w-0 truncate'>{coupon.couponName}</span>

                                <CloseOutlined onClick={() => handleRemoveCoupon(coupon.couponId)} />
                            </div>
                        ))}
                        <Button className='button-outline' onClick={() => {
                            setVisible(true)

                        }}>
                            {intl.formatMessage({ id: 'topic.decoration.coupon.select' })}
                        </Button>
                    </Space>
                </ProForm.Item>
            </ProForm>
            <SelectCouponDrawer
                visible={visible}
                onClose={() => setVisible(false)}
                onOk={handleSelectCoupon}
            />
        </>

    );
};

export default CouponSettings;