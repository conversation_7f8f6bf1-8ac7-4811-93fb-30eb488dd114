import { ReactComponent as ImageIcon } from '@/assets/icons/decoration/coupon.svg';
import { useNode } from '@craftjs/core';
import classNames from 'classnames';
import React from 'react';
import { useIntl } from 'react-intl';
import useLoadCoupons from '../../hooks/useLoadCoupons';
import { CouponItem, CouponProps } from './type';


// --- 辅助组件：竖向优惠券卡片（用于2、3、>3个时的布局） ---
const CouponCardVertical: React.FC<{ coupon: CouponItem, className?: string }> = ({ coupon, className }) => {
    const intl = useIntl();
    const { couponName, couponAmount, enableAmount } = coupon;

    const thresholdDisplay = intl.formatMessage({ id: 'topic.decoration.coupon.threshold' }, { threshold: enableAmount });
    const claimText = intl.formatMessage({ id: 'topic.decoration.coupon.claim' });

    return (
        <div className={classNames('overflow-hidden flex-shrink-0', className)}>
            <div className="bg-primary-light rounded-2xl border-solid border border-[#FBD7A5] shadow-sm">
                <div
                    style={{
                        borderBottom: '1px dashed #FBD7A5'
                    }}
                    className="flex flex-col items-center flex-grow px-3 py-5 w-min-0">
                    <div className="text-3xl font-bold mb-1">
                        <span className='text-base font-normal'>$</span>{couponAmount}
                    </div>
                    <div className="text-center w-full text-gray-500 text-sm mb-3 truncate ellipsis">
                        {thresholdDisplay}
                    </div>
                    <div className="text-center w-full font-medium truncate ellipsis">
                        {couponName}
                    </div>
                </div>
                <div className="relative px-3 py-3 flex items-center justify-center">
                    <div className="absolute -left-0 -top-3 transform -translate-x-2/3 w-6 h-6 bg-white rounded-full border border-solid border-orange-200"></div>
                    <div className="absolute -right-0 -top-3 transform translate-x-2/3 w-6 h-6 bg-white rounded-full border border-solid border-orange-200"></div>
                    <button
                        style={{
                            border: 'none'
                        }}
                        className="w-full px-5 py-2 bg-primary text-white rounded-lg font-medium"
                    >
                        {claimText}
                    </button>
                </div>
            </div>

        </div>
    );
};


// --- 辅助组件：横向优惠券卡片（用于1个时的布局） ---
const CouponCardHorizontal: React.FC<{ coupon: CouponItem }> = ({ coupon }) => {
    const intl = useIntl();
    const { couponName, couponAmount, enableAmount } = coupon;

    const thresholdDisplay = intl.formatMessage({ id: 'topic.decoration.coupon.threshold' }, { threshold: enableAmount });
    const claimText = intl.formatMessage({ id: 'topic.decoration.coupon.claim' });

    return (
        <div className="overflow-hidden w-full">
            <div className="flex bg-primary-light rounded-2xl border-solid border border-[#FBD7A5] shadow-sm relative">
                <div style={{
                    borderRight: '1px dashed #FBD7A5'
                }} className="items-center p-4 min-w-0 flex-1">
                    <div className="text-3xl font-bold mb-1">
                        <span className='text-base font-normal'>$</span>{couponAmount}
                    </div>
                    <div className="text-gray-500 text-sm mb-3">
                        {thresholdDisplay}
                    </div>
                    <div className="font-medium truncate">
                        {couponName}
                    </div>
                </div>
                <div className="relative px-3 flex items-center justify-center">
                    <div className="absolute -top-0 -left-3 transform -translate-y-2/3 w-6 h-6 bg-white rounded-full border border-solid border-[#FBD7A5]"></div>
                    <div className="absolute -bottom-0 -left-3 transform translate-y-2/3 w-6 h-6 bg-white rounded-full border border-solid border-[#FBD7A5]"></div>
                    <button
                        style={{
                            border: 'none'
                        }}
                        className="px-5 py-2 bg-primary text-white rounded-lg font-medium"
                    >
                        {claimText}
                    </button>
                </div>
            </div>
        </div>

    );
};
// --- 主组件：优惠券模块 ---
const CouponDisplay: React.FC<CouponProps> = ({ couponsIds = [] }) => {
    const intl = useIntl();
    const {
        connectors: { connect, drag },
    } = useNode();
    const { coupons } = useLoadCoupons({ couponsIds });

    const numCoupons = coupons.length;

    let content;

    if (numCoupons === 0) {
        content = (
            <div
                style={{ minHeight: '160px' }}
                className={`relative flex flex-col items-center justify-center text-gray-500 bg-[#f3f5f6]`}>
                <ImageIcon stroke="#F49C1F" />
                {intl.formatMessage({ id: 'topic.decoration.coupon.noCoupons' })}
            </div>
        );
    } else if (numCoupons === 1) {
        content = (
            <CouponCardHorizontal coupon={coupons[0]} />
        );
    } else if (numCoupons === 2) {
        content = (
            <div className="grid grid-cols-2 gap-2 w-full">
                {coupons.map((coupon) => (
                    <CouponCardVertical key={coupon.couponId} coupon={coupon} />
                ))}
            </div>
        );
    } else if (numCoupons === 3) {
        content = (
            <div className="grid grid-cols-3 gap-2 w-full">
                {coupons.map((coupon) => (
                    <CouponCardVertical key={coupon.couponId} coupon={coupon} />
                ))}
            </div>
        );
    } else { // numCoupons > 3
        content = (
            // 使用 flexbox 实现横向滚动，并确保每个卡片不缩小
            <div className="flex space-x-4 overflow-x-auto w-full pb-2 no-scrollbar" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                {coupons.map((coupon) => (
                    <CouponCardVertical key={coupon.couponId} coupon={coupon} className='min-w-[125px]' />
                ))}
            </div>
        );
    }

    return (
        <div
            ref={(ref) => connect(drag(ref as HTMLElement))}
        >
            {content}
        </div>
    );
};

export default CouponDisplay;