import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { ActivityDataRequest, ActivityEntity } from '../../types/ActivityDataType';

const mockData: ActivityEntity[] = [
    {
        "activityId": 1223,
        "activityName": "测试赠品厂送",
        "activityType": 2,
        "activityStatus": 0,
        "activityStep": 1,
        "startTime": "2025-05-11 00:00:00",
        "endTime": "2025-05-15 00:00:00",
        "remark": "活动备注",
        "updatePerson": "更新人",
        "updateTime": "2025-05-15 00:00:00"
    },
    {
        "activityId": 1224,
        "activityName": "测试赠品厂送3",
        "activityType": 2,
        "activityStatus": 1,
        "activityStep": 2,
        "startTime": "2025-05-11 00:00:00",
        "endTime": "2025-05-15 00:00:00",
        "remark": "活动备注",
        "updatePerson": "更新人",
        "updateTime": "2025-05-15 00:00:00"
    },
    {
        "activityId": 1224,
        "activityName": "测试赠品厂送3",
        "activityType": 2,
        "activityStatus": 2,
        "activityStep": 3,
        "startTime": "2025-05-11 00:00:00",
        "endTime": "2025-05-15 00:00:00",
        "remark": "活动备注活动备注活动备注活动备注活动备注",
        "updatePerson": "更新人",
        "updateTime": "2025-05-15 00:00:00"
    },
];


export const queryActivityPage = async (params: ActivityDataRequest) => {
    return request<PageResponseDataType<ActivityEntity>>(
        '/ipmspromotion/activityQuery/pageQueryActivityList',
        {
            data: params,
        },
    );
}


export const takeEffectActivity = async (params: { activityId: number }) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/takeEffectActivity',
        {
            data: params,
        },
    );
}

export const invalidActivity = async (params: { activityId: number }) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/invalidActivity',
        {
            data: params,
        },
    );
}