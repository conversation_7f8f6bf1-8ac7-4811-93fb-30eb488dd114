import { FormattedMessage } from "@umijs/max";

const commonColumn = [
    {
        title: <FormattedMessage id="goods.list.table.itemSn" />,
        dataIndex: 'itemSn',
        ellipsis: true,
        width: 80,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.itemName" />,
        dataIndex: 'itemName',
        ellipsis: true,
        width: 120,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.category" />,
        dataIndex: 'categoryName',
        width: 120,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.brand" />,
        dataIndex: 'brandName',
        width: 120,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.suggestPrice" />,
        dataIndex: 'suggestPrice',
        valueType: {
            type: "money",
            locale: "en-US"
        },
        width: 120,
        editable: false,
    },
]

const promotionPrice = {
    title: <><FormattedMessage id="shop.activity.products.promotionPrice" /> <span className='text-red-500'>*</span></>,
    dataIndex: 'promotionPrice',
    valueType: {
        type: "money",
        locale: "en-US"
    },
    width: 140,
    fieldProps: {
        precision: 2,
    },
    formItemProps: {
        rules: [
            {
                required: true,
                message: <FormattedMessage id="shop.activity.products.required" />,
            },
        ],
    },
}

const buyNum = {
    title: <><FormattedMessage id="shop.activity.products.quantity" /> <span className='text-red-500'>*</span></>,
    dataIndex: 'num',
    valueType: 'digit',
    width: 120,
    fieldProps: {
        precision: 0,
        min: 1,
    },
    formItemProps: {
        rules: [
            {
                required: true,
                message: <FormattedMessage id="shop.activity.products.required" />,
            },
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

const minBuyNum = {
    title: <><FormattedMessage id="shop.activity.products.minOrderQuantityPerCustomer" /> <span className='text-red-500'>*</span></>,
    dataIndex: 'minBuyNum',
    valueType: 'digit',
    width: 120,
    fieldProps: {
        precision: 0,
        min: 1,
    },
    formItemProps: {
        rules: [
            {
                required: true,
                message: <FormattedMessage id="shop.activity.products.required" />,
            },
        ],
    },
}

const maxBuyNum = {
    title: <FormattedMessage id="shop.activity.products.maxPurchaseQuantityPerCustomer" />,
    dataIndex: 'maxBuyNum',
    valueType: 'digit',
    fieldProps: {
        precision: 0,
        min: 1,
    },
    width: 120,
}

const totalMaxBuyNum = {
    title: <FormattedMessage id="shop.activity.products.totalPurchaseQuantity" />,
    dataIndex: 'totalMaxBuyNum',
    valueType: 'digit',
    fieldProps: {
        precision: 0,
    },
    width: 120,
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 购本品数
const purchasedQuantity = {
    title: <FormattedMessage id="shop.activity.products.purchasedQuantity" />,
    dataIndex: 'buySelfNum',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 赠本品数
const giftQuantity = {
    title: <FormattedMessage id="shop.activity.products.giftQuantity" />,
    dataIndex: 'giftSelfNum',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 商品总限购数
const totalMaxPurchaseQuantity = {
    title: <FormattedMessage id="shop.activity.products.totalMaxPurchaseQuantity" />,
    dataIndex: 'totalMaxBuyNum',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 赠品总限购数
const totalMaxGiftQuantity = {
    title: <FormattedMessage id="shop.activity.products.totalMaxGiftQuantity" />,
    dataIndex: 'giftTotalMaxBuyNum',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}



const sortOrder = {
    title: <FormattedMessage id="shop.activity.products.sortOrder" />,
    dataIndex: 'showOrder',
    editable: false,
    width: 50,
}

export const specialActivityColumns = [
    sortOrder,
    ...commonColumn,
    promotionPrice,
    minBuyNum,
    maxBuyNum,
    totalMaxBuyNum,
];

export const buyGiftSelfActivityColumns = [
    sortOrder,
    ...commonColumn,
    promotionPrice,
    minBuyNum,
    maxBuyNum,
    purchasedQuantity,
    giftQuantity,
    totalMaxPurchaseQuantity,
    totalMaxGiftQuantity,
]

export const fullGiftActivityColumns = [
    sortOrder,
    ...commonColumn,
    promotionPrice,
    minBuyNum,
    maxBuyNum,
    totalMaxBuyNum,
]

// const buyLimit = {
//     title: <FormattedMessage id="shop.activity.products.limit" />,
//     dataIndex: 'packageDiscount',
//     renderFormItem: (item, form, field) => {
//         const fieldProps = { precision: 0 };
//         return <div className="border-left-gray pl-4 h-full">
//             <ProFormDigit
//                 fieldProps={{ ...fieldProps }}
//                 formItemProps={{ rules: [{ required: true, message: <FormattedMessage id="shop.activity.products.required" /> }] }}
//                 label={<FormattedMessage id="shop.activity.products.minOrderPackageNum" />}
//                 name="packageDiscount.minBuyNum" min={1}
//             />
//             <ProFormDigit
//                 fieldProps={fieldProps}
//                 label={<FormattedMessage id="shop.activity.products.singleCustomerLimitPackageNum" />}
//                 name="packageDiscount.maxBuyNum" min={1}
//             />
//             <ProFormDigit
//                 fieldProps={fieldProps}
//                 label={<FormattedMessage id="shop.activity.products.totalLimitPackageNum" />}
//                 name="packageDiscount.totalMaxBuyNum" min={1}
//             />
//         </div>
//     },
//     width: 200,
//     onCell: (record, index) => {
//         if (index === 0) return { rowSpan: record.rowSpan || 1 };
//         return { rowSpan: 0 };
//     }
// }

const suiteCommonColumn = [
    {
        title: <><FormattedMessage id="shop.activity.products.minOrderPackageNum" /> <span className='text-red-500'>*</span></>,
        dataIndex: 'minBuyNum',
        valueType: 'digit',
        fieldProps: {
            precision: 0,
            min: 0,
        },
        formItemProps: {
            rules: [
                {
                    required: true,
                    message: <FormattedMessage id="shop.activity.products.required" />,
                },
            ],
        },
        onCell: (record, index) => {
            if (index === 0) return { rowSpan: record.rowSpan || 1 };
            return { rowSpan: 0 };
        }
    },
    {
        title: <FormattedMessage id="shop.activity.products.singleCustomerLimitPackageNum" />,
        dataIndex: 'maxBuyNum',
        valueType: 'digit',
        fieldProps: {
            precision: 0,
            min: 0,
        },
        onCell: (record, index) => {
            if (index === 0) return { rowSpan: record.rowSpan || 1 };
            return { rowSpan: 0 };
        }
    },
    {
        title: <FormattedMessage id="shop.activity.products.totalPurchaseQuantity" />,
        dataIndex: 'totalMaxBuyNum',
        valueType: 'digit',
        fieldProps: {
            precision: 0,
            min: 0,
        },
        onCell: (record, index) => {
            if (index === 0) return { rowSpan: record.rowSpan || 1 };
            return { rowSpan: 0 };
        }
    }
]

export const suiteProductsColumns = [
    ...commonColumn,
    buyNum,
    ...suiteCommonColumn
]

export const suiteDiscountColumns = [
    ...commonColumn,
    {
        ...promotionPrice,
        title: <><FormattedMessage id="shop.activity.products.packageActivityPrice" /> <span className='text-red-500'>*</span></>,
        formItemProps: {
            rules: [
                {
                    required: true,
                    message: <FormattedMessage id="shop.activity.products.required" />,
                },
            ],
        },
    },
    buyNum,
    ...suiteCommonColumn
]
