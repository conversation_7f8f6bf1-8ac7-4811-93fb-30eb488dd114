import { EditableDragTableRef } from "@/components/EditableDragTable";
import { ProForm } from "@ant-design/pro-components";
import { useIntl, useSearchParams } from "@umijs/max";
import { message } from "antd";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import { ActivityFormProps, SubmitArea } from "..";
import { ActivityFormValues, GiftSettingsFormValues } from "../../../types/ActivityDataType";
import { ActivityArea } from "../../../types/ActivityEnum";
import ActivityBasicForm from "../components/ActivityBasicForm";
import GiftSettings from "../components/GiftSettings";
import ProductActivityTable from "../components/ProductActivityTable";
import { fullGiftActivityColumns } from "../config/productActivityColumn";
import { queryActivityById } from "../service";


const FullGiftActivityForm: React.FC<ActivityFormProps> = (props) => {
    const intl = useIntl();
    const [searchParams] = useSearchParams();
    const activityId = searchParams.get('id');
    const type = searchParams.get('type'); // view | edit | copy
    const activityType = Number(searchParams.get('activityType'));
    const readonly = type === 'view';

    const [form] = useForm<ActivityFormValues>();
    const [gitForm] = ProForm.useForm<GiftSettingsFormValues>();
    const tableRef = useRef<EditableDragTableRef>(null);
    const giftSettingsRef = useRef<any>(null);
    const [dataSource, setDataSource] = useState<any[]>([]);

    useEffect(() => {
        if (activityId) {
            queryActivityById({ activityId }).then(res => {
                form.setFieldsValue({
                    ...res,
                    periodTime: [res.startTime ? dayjs(res.startTime) : undefined, res.endTime ? dayjs(res.endTime) : undefined],
                    activityImageList: res.activityImage ? [{ uid: '1', name: res.activityImage, status: 'done', url: res.activityImage }] : [],

                });
                setDataSource(res?.activityItems?.map(item => ({
                    ...item,
                    promotionPrice: item.promotionPrice ? Number(item.promotionPrice) : '',
                })));

                gitForm.setFieldsValue({
                    giftRuleMode: res.activityType,
                    giftRuleType: res.giftRuleType,
                    activityLadderRules: res.activityLadderRules
                });
            });
        }
    }, [activityId]);


    const onSubmit = async () => {
        const result = await tableRef.current?.validateAndGetData();
        if (!result?.success) return;
        const activityItems = result.data ?? [];

        if (activityItems.length === 0) {
            message.warning(intl.formatMessage({ id: 'shop.common.message.needAddProduct' }));
            return;
        }

        // 校验赠品表格中的必填字段
        try {
            const giftResult = await gitForm.validateFields();

            // 校验赠品表格中的必填字段
            if (giftSettingsRef.current?.validateGiftItems) {
                const giftValidationResult = await giftSettingsRef.current.validateGiftItems();
                if (!giftValidationResult.success) {
                    if (giftValidationResult.errors && giftValidationResult.errors.length > 0) {
                        message.error(giftValidationResult.errors[0]);
                    }
                    return;
                }
            }
        } catch (error) {
            return;
        }

        const basicValues = form.getFieldsValue();
        const giftValues = gitForm.getFieldsValue();
        const { periodTime = [], activityImageList = [] } = basicValues as any;
        const { giftRuleType, activityLadderRules } = giftValues;

        console.log('giftValues', giftValues);

        const params = {
            activityId,
            activityType,
            activityName: basicValues.activityName,
            startTime: periodTime[0] ? periodTime[0].format('YYYY-MM-DD HH:mm:ss') : undefined,
            endTime: periodTime[1] ? periodTime[1].format('YYYY-MM-DD HH:mm:ss') : undefined,
            activityDesc: basicValues.activityDesc,
            activityArea: basicValues.activityArea,
            areaId: basicValues.areaId,
            userCoupon: basicValues.userCoupon ? 1 : 0,
            onCredit: basicValues.onCredit ? 1 : 0,
            orderNoStock: basicValues.orderNoStock ? 1 : 0,
            remark: basicValues.remark,
            giftRuleType,
            activityImage: activityImageList?.[0]?.response?.data?.[0],
            activityItems: activityItems.map((item, index) => ({
                activityId,
                itemId: item.itemId,
                itemSn: item.itemSn,
                itemName: item.itemName,
                categoryId: item.categoryId,
                categoryName: item.categoryName,
                brandId: item.brandId,
                brandName: item.brandName,
                promotionPrice: String(item.promotionPrice ?? 0),
                minBuyNum: item.minBuyNum,
                maxBuyNum: item.maxBuyNum,
                totalMaxBuyNum: item.totalMaxBuyNum,
                showOrder: index + 1
            })),
            activityLadderRules: activityLadderRules?.map((rule, ruleIndex) => ({
                ladder: ruleIndex,
                enableAmount: String(rule.enableAmount),
                giftRuleType,
                activityId,
                activityGiftItems: rule.activityGiftItems?.map((item, index) => ({
                    activityId,
                    itemId: item.itemId,
                    itemSn: item.itemSn,
                    itemName: item.itemName,
                    categoryId: item.categoryId,
                    categoryName: item.categoryName,
                    brandId: item.brandId,
                    brandName: item.brandName,
                    giftNum: item.giftNum,
                    giftTotalMaxBuyNum: item.giftTotalMaxBuyNum,
                }))
            }))
        }

        console.log('params', params);
        props.onSubmit?.(params);
    }


    return <ProForm<ActivityFormValues>
        className="flex flex-col h-[calc(100vh-130px)] overflow-auto"
        form={form}
        initialValues={{
            activityArea: ActivityArea.NO_LIMIT,
            userCoupon: 0,
            onCredit: 1,
            orderNoStock: 0,
        }}
        readonly={readonly}
        onFinish={onSubmit}

        submitter={{
            render: (props, doms) => {
                return (
                    <SubmitArea {...props} readonly={readonly} />
                );
            },
        }}
    >
        <div className="flex flex-1 bg-white rounded-lg  overflow-auto shadow-sm">
            {/* 左侧：基础信息 */}
            <div className="w-[328px] p-4 h-full">
                <h3 className="text-lg font-semibold text-gray-800 module-title">{intl.formatMessage({ id: 'shop.activity.title.basicInfo' })}</h3>
                <ActivityBasicForm form={form} />
            </div>

            {/* 右侧：适用商品 */}
            <div className="flex-1 min-w-0">
                <div className="p-4 min-h-[850px] border-left-gray">
                    <h3 className="module-title">{intl.formatMessage({ id: 'shop.activity.title.applicableProducts' })}</h3>
                    <ProductActivityTable
                        activityType={activityType}
                        tableRef={tableRef}
                        columns={fullGiftActivityColumns}
                        dataSource={dataSource}
                        onDataChange={setDataSource}
                        rowKey="itemId"
                        maxHeight={600}
                    />

                    <div className="mt-5 pb-5">
                        <h3 className="module-title">{intl.formatMessage({ id: 'shop.activity.title.giftInfo' })}</h3>
                        <GiftSettings ref={giftSettingsRef} giftRuleMode={activityType} form={gitForm} readonly={readonly} />
                    </div>
                </div>
            </div>
        </div>
    </ProForm>
}

export default FullGiftActivityForm;