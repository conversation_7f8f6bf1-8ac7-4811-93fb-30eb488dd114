import { EditableDragTableRef } from "@/components/EditableDragTable";
import { ProForm } from "@ant-design/pro-components";
import { useIntl, useSearchParams } from "@umijs/max";
import { message } from "antd";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import { ActivityFormProps, SubmitArea } from "..";
import { ActivityFormValues } from "../../../types/ActivityDataType";
import { ActivityArea, ActivityType, ItemPackageType } from "../../../types/ActivityEnum";
import ActivityBasicForm from "../components/ActivityBasicForm";
import ProductPackage from "../components/ProductPackage";
import { queryActivityById } from "../service";


const SpecialActivityForm: React.FC<ActivityFormProps> = (props) => {
    const intl = useIntl();
    const [searchParams] = useSearchParams();
    const activityId = searchParams.get('id');
    const type = searchParams.get('type'); // view | edit | copy
    const readonly = type === 'view';

    const [form] = useForm<ActivityFormValues>();
    const tableRef = useRef<EditableDragTableRef>(null);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [itemPackageType, setItemPackageType] = useState<ItemPackageType>(ItemPackageType.PRODUCT_COMBINE);


    useEffect(() => {
        if (activityId) {
            queryActivityById({ activityId }).then(res => {
                form.setFieldsValue({
                    ...res,
                    periodTime: [res.startTime ? dayjs(res.startTime) : undefined, res.endTime ? dayjs(res.endTime) : undefined],
                    activityImageList: res.activityImage ? [{ uid: '1', name: res.activityImage, status: 'done', url: res.activityImage }] : [],
                });
                setDataSource(res?.activityItems?.map(item => ({
                    ...item,
                    promotionPrice: item.promotionPrice ? Number(item.promotionPrice) : '',
                    minBuyNum: res.minBuyNum,
                    maxBuyNum: res.maxBuyNum,
                    totalMaxBuyNum: res.totalMaxBuyNum,
                })));
                setItemPackageType(res.itemPackageType);
            });
        }
    }, [activityId]);

    useEffect(() => {
        if (itemPackageType === ItemPackageType.SUITE_PRODUCT) {
            form.setFieldsValue({
                userCoupon: 0,
            });
        }
    }, [itemPackageType]);


    const onSubmit = async () => {
        const result = await tableRef.current?.validateAndGetData();
        if (!result?.success) return;
        const activityItems = result.data ?? [];

        if (activityItems.length === 0) {
            message.warning(intl.formatMessage({ id: 'shop.common.message.needAddProduct' }));
            return;
        }

        const basicValues = form.getFieldsValue();
        const { periodTime = [], activityImageList = [] } = basicValues;

        const params = {
            activityId,
            activityType: ActivityType.SUITE_ITEM,
            activityName: basicValues.activityName,
            startTime: periodTime[0] ? periodTime[0].format('YYYY-MM-DD HH:mm:ss') : undefined,
            endTime: periodTime[1] ? periodTime[1].format('YYYY-MM-DD HH:mm:ss') : undefined,
            activityDesc: basicValues.activityDesc,
            activityArea: basicValues.activityArea,
            areaId: basicValues.areaId,
            userCoupon: basicValues.userCoupon ? 1 : 0,
            onCredit: basicValues.onCredit ? 1 : 0,
            orderNoStock: basicValues.orderNoStock ? 1 : 0,
            remark: basicValues.remark,
            itemPackageType,
            minBuyNum: activityItems[0]?.minBuyNum,
            maxBuyNum: activityItems[0]?.maxBuyNum,
            totalMaxBuyNum: activityItems[0]?.totalMaxBuyNum,
            activityItems: activityItems.map((item, index) => ({
                activityId,
                itemId: item.itemId,
                itemSn: item.itemSn,
                itemName: item.itemName,
                categoryId: item.categoryId,
                categoryName: item.categoryName,
                brandId: item.brandId,
                brandName: item.brandName,
                promotionPrice: itemPackageType === ItemPackageType.PRODUCT_COMBINE ? null : String(item.promotionPrice ?? 0),
                num: item.num,
            })),
            activityImage: activityImageList?.[0]?.response?.data?.[0]
        }
        props.onSubmit?.(params);
    }


    return <ProForm<ActivityFormValues>
        className="flex flex-col h-[calc(100vh-130px)] overflow-auto"
        form={form}
        initialValues={{
            activityArea: ActivityArea.NO_LIMIT,
            userCoupon: itemPackageType === ItemPackageType.PRODUCT_COMBINE ? 0 : 1,
            onCredit: 1,
            orderNoStock: 0,
        }}
        readonly={readonly}
        onFinish={onSubmit}

        submitter={{
            render: (props, doms) => {
                return (
                    <SubmitArea {...props} readonly={readonly} />
                );
            },
        }}
    >
        <div className="flex flex-1 bg-white rounded-lg  overflow-auto shadow-sm">
            {/* 左侧：基础信息 */}
            <div className="w-[328px] p-4">
                <h3 className="text-lg font-semibold text-gray-800 module-title">{intl.formatMessage({ id: 'shop.activity.title.basicInfo' })}</h3>
                <ActivityBasicForm form={form} itemPackageType={itemPackageType} />
            </div>

            {/* 右侧：适用商品 */}
            <div className="flex-1 min-w-0">
                <div className="p-4 min-h-[850px] border-left-gray">
                    <h3 className="module-title">{intl.formatMessage({ id: 'shop.activity.title.applicableProducts' })}</h3>
                    <ProductPackage
                        itemPackageType={itemPackageType}
                        setItemPackageType={setItemPackageType}
                        tableRef={tableRef}
                        dataSource={dataSource}
                        onDataChange={setDataSource}
                    />
                </div>
            </div>
        </div >
    </ProForm >
}

export default SpecialActivityForm;