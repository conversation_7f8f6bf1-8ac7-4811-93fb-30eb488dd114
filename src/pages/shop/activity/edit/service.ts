import { request } from '@/utils/request';
import { ActivityFormValues, ActivityItemEntity } from '../../types/ActivityDataType';


export const addActivity = async (params: ActivityFormValues) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/addActivity',
        {
            data: params,
        },
    );
}

export const editActivity = async (params: ActivityFormValues) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/editActivity',
        {
            data: params,
        },
    );
}

export const queryActivityById = async (params: { activityId: number }) => {
    return request<ActivityFormValues>(
        '/ipmspromotion/activityQuery/queryActivityById',
        {
            data: params,
        },
    )
}


export const queryActivityItemByTaskId = async (params: { taskId: number }): Promise<ActivityItemEntity[]> => {
    return request(
        '/ipmspromotion/activityManager/queryActivityItemByTaskId',
        {
            data: params,
        },
    )
}
