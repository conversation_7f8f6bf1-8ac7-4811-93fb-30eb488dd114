import SelectProductDrawer from '@/pages/shop/topic/decoration/CraftEditor/components/LinkSettings/SelectProductDrawer';
import { ActivityGiftItem } from '@/pages/shop/types/ActivityDataType';
import { EditableProTable, ProColumns } from '@ant-design/pro-table';
import { Button, message, Space } from 'antd';
import { uniqBy } from 'lodash';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useIntl } from 'react-intl';

export interface GiftItemsTableRef {
    validateFields: () => Promise<{ success: boolean; errors?: string[] }>;
}

interface GiftItemsTableProps {
    dataSource: ActivityGiftItem[];
    // 当表格数据（增删改）变化时，通知父组件更新
    onChange: (data: ActivityGiftItem[]) => void;
    readonly?: boolean;
}

const GiftItemsTable = forwardRef<GiftItemsTableRef, GiftItemsTableProps>(({ dataSource, onChange, readonly }, ref) => {
    const intl = useIntl();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [editableKeys, setEditableKeys] = useState<string[]>(() => dataSource.map(item => String(item.itemId))); // 初始时所有行都可编辑
    const [isModalVisible, setIsModalVisible] = useState(false);
    const tableRef = useRef<any>(null);

    // 暴露校验方法给父组件
    useImperativeHandle(ref, () => ({
        validateFields: async () => {
            const errors: string[] = [];

            // 校验每一行的必填字段
            dataSource.forEach((item, index) => {
                if (!item.giftNum || item.giftNum <= 0) {
                    errors.push(`on ${index + 1} line：${intl.formatMessage({ id: 'shop.activity.giftInfo.giftQuantityRequired' })}`);
                }
                if (!item.giftTotalMaxBuyNum || item.giftTotalMaxBuyNum <= 0) {
                    errors.push(`on ${index + 1} line：：${intl.formatMessage({ id: 'shop.activity.giftInfo.maxBuyQuantityRequired' })}`);
                }
            });

            if (errors.length > 0) {
                // 如果有错误，设置所有行为编辑状态以显示校验错误
                setEditableKeys(dataSource.map(item => String(item.itemId)));
                return { success: false, errors };
            }

            return { success: true };
        }
    }));

    useEffect(() => {
        if (dataSource.length > 0) {
            setEditableKeys(dataSource.map(item => String(item.itemId)));
        }
    }, [dataSource]);

    // 赠品表格列配置
    const columns: ProColumns<ActivityGiftItem>[] = [
        {
            title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
            dataIndex: 'itemId',
            readonly: true,
            width: '15%',
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
            dataIndex: 'itemName',
            readonly: true,
            width: '15%',
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.category' }),
            dataIndex: 'categoryName',
            readonly: true,
            width: '10%',
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.brand' }),
            dataIndex: 'brandName',
            readonly: true,
            width: '10%',
        },
        {
            title: intl.formatMessage({ id: 'goods.list.table.suggestPrice' }),
            dataIndex: 'suggestPrice',
            valueType: 'money',
            readonly: true,
            width: '10%',
        },
        {
            title: <>{intl.formatMessage({ id: 'shop.activity.giftInfo.giftQuantity' })}<span className='text-red-500'>*</span></>,
            dataIndex: 'giftNum',
            valueType: 'digit',
            width: '15%',
            formItemProps: {
                rules: [
                    { required: true, message: intl.formatMessage({ id: 'shop.activity.giftInfo.giftQuantityRequired' }) },
                    { type: 'number', min: 1, message: intl.formatMessage({ id: 'shop.activity.giftInfo.giftQuantityMin' }) },
                ],
            },
            fieldProps: { precision: 0 },
        },
        {
            title: <>{intl.formatMessage({ id: 'shop.activity.giftInfo.totalMaxBuyQuantity' })}<span className='text-red-500'>*</span></>,
            dataIndex: 'giftTotalMaxBuyNum',
            valueType: 'digit',
            width: '15%',
            formItemProps: {
                rules: [
                    { required: true, message: intl.formatMessage({ id: 'shop.activity.giftInfo.maxBuyQuantityRequired' }) },
                    { type: 'number', min: 1, message: intl.formatMessage({ id: 'shop.activity.giftInfo.maxBuyQuantityMin' }) },
                ],
            },
            fieldProps: { precision: 0 },
        },
    ];

    const onOpenProductDrawer = () => {
        setIsModalVisible(true);
    }

    const handleProductSelected = (ids, selectedProducts) => {
        const newItems = selectedProducts.filter(item => item?.itemId).map(item => ({
            itemId: item.itemId,
            itemSn: item.itemSn,
            itemName: item.itemName,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            brandId: item.brandId,
            brandName: item.brandName,
            suggestPrice: item.suggestPrice,
            giftNum: undefined,
            giftTotalMaxBuyNum: undefined
        }));
        const newDataSource = uniqBy(dataSource.concat(newItems), 'itemId');
        onChange(newDataSource); // 更新父组件数据
        setEditableKeys(prevKeys => [...prevKeys, ...newItems.map(item => item.itemId)]);
        setIsModalVisible(false);
    };

    const handleDeleteSelected = () => {
        if (selectedRowKeys.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }
        const newItems = dataSource.filter(item => !selectedRowKeys.includes(item.itemId!));
        onChange(newItems); // 更新父组件数据
        setSelectedRowKeys([]); // 清除选中项
    };

    return (
        <>
            {!readonly && <Space className="mb-4">
                <Button type="primary" onClick={onOpenProductDrawer}>
                    {intl.formatMessage({ id: 'shop.common.button.addGift' })}
                </Button>
                <Button className='button-outline' onClick={handleDeleteSelected} disabled={selectedRowKeys.length === 0}>
                    {intl.formatMessage({ id: 'common.button.delete' })}
                </Button>
            </Space>}
            <EditableProTable<ActivityGiftItem>
                ref={tableRef}
                rowKey="itemId"
                columns={columns}
                value={dataSource} // 使用 value 而不是 dataSource，因为 ProForm.Item 需要 value
                onChange={(editableData) => {
                    // 当表格数据（包括编辑、新增、删除）发生变化时，通知父组件
                    onChange([...editableData]);
                }}
                editable={{
                    type: 'multiple', // 支持多行同时编辑
                    editableKeys: readonly ? [] : editableKeys,
                    onChange: setEditableKeys, // 更新处于编辑状态的key
                    onDelete: async (key, row) => {
                        // ProTable 的 onDelete 已经处理了数据的过滤，我们不需要额外操作
                        // 只需要确保父组件的 onChange 被触发即可 (ProTable 内部会调用 onChange)
                        return true; // 返回 true 表示删除成功
                    },
                    onValuesChange: (changedValues, allValues) => {
                        onChange(allValues);
                    }
                }}
                recordCreatorProps={false} // 不使用ProTable自带的创建按钮，由外部按钮控制
                pagination={false}
                search={false}
                options={false}
                tableAlertRender={() => false}
                rowSelection={readonly ? undefined : {
                    columnWidth: 48,
                    selectedRowKeys,
                    onChange: (keys) => {
                        setSelectedRowKeys(keys);
                    },
                    preserveSelectedRowKeys: true,
                }}
                scroll={{ x: 'max-content', y: 300 }} // 可横向滚动
                className="mt-4"
            />

            <SelectProductDrawer
                visible={isModalVisible}
                selected={dataSource.map(item => item.itemId)}
                onClose={() => {
                    setIsModalVisible(false);
                }}
                onOk={handleProductSelected}
            />
        </>
    );
});

export default GiftItemsTable;