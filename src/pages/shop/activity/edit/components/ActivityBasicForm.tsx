import ProFormCustomerTag from '@/components/ProFormItem/ProFormCustomerTag';
import { ProFormUploadSingleCard } from '@/components/ProFormItem/ProFormUpload';
import { ActivityType, ActivityTypeEnum, ItemPackageType } from '@/pages/shop/types/ActivityEnum';
import {
    ProForm,
    ProFormDateTimeRangePicker,
    ProFormDependency,
    ProFormRadio,
    ProFormText,
    ProFormTextArea
} from '@ant-design/pro-components';
import { useSearchParams } from '@umijs/max';
import { FormInstance, Switch, Tag } from 'antd';
import React from 'react';
import { useIntl } from 'react-intl';



const ActivityForm: React.FC<{
    form: FormInstance;
    itemPackageType?: ItemPackageType; // 仅组合套餐才有这个属性传递
}> = (props) => {
    const intl = useIntl();
    const [searchParams] = useSearchParams();
    const type = searchParams.get('type'); // view | edit | copy
    const activityType = Number(searchParams.get('activityType'));
    const readonly = type === 'view';
    const { form } = props;

    return (
        <>
            <Tag color="red" className='mb-3'>{ActivityTypeEnum[activityType as ActivityType].text}</Tag>
            {/* 活动名称 */}
            <ProFormText
                name="activityName"
                label={intl.formatMessage({ id: 'shop.activity.name' })}
                rules={[{ required: true }]}
            />

            {/* 活动说明 */}
            <ProFormTextArea
                name="activityDesc"
                label={intl.formatMessage({ id: 'shop.activity.description' })}
                placeholder={intl.formatMessage({ id: 'shop.activity.descriptionPlaceholder' })}
                fieldProps={{
                    maxLength: 200,
                    showCount: true,
                    autoSize: { minRows: 3, maxRows: 5 },
                }}
            />

            {/* 活动周期 */}
            <ProFormDateTimeRangePicker
                name='periodTime'
                label={intl.formatMessage({ id: 'shop.activity.period' })}
                fieldProps={{
                    format: 'YYYY-MM-DD HH:mm:ss', // 格式化时间
                    showTime: true,
                    className: 'w-full'
                }}
                rules={[{ required: true }]}
            />

            {/* 活动人群 */}
            <ProFormRadio.Group
                name="activityArea"
                label={intl.formatMessage({ id: 'shop.activity.audience' })}
                options={[
                    { label: intl.formatMessage({ id: 'shop.activity.audienceUnlimited' }), value: 0 },
                    { label: intl.formatMessage({ id: 'shop.activity.audienceSpecificTags' }), value: 1 },
                ]}
            />
            <ProFormDependency name={['activityArea']}>
                {({ activityArea }) => {
                    if (activityArea === 1) {
                        return (
                            <ProFormCustomerTag
                                name="areaId"
                                label=""
                                rules={[{ required: true, message: intl.formatMessage({ id: 'shop.activity.tagRequired' }) }]}
                            />
                        );
                    }
                    return null;
                }}
            </ProFormDependency>

            <ProFormUploadSingleCard
                name="activityImageList"
                label={intl.formatMessage({ id: 'shop.activity.image' })}
                onChange={(fileList) => {
                    form?.setFieldsValue({
                        activityImageList: fileList
                    });
                }}
            />

            <ProForm.Item
                labelCol={{ span: 18 }}
                wrapperCol={{ span: 6 }}
                className='form-item-switch'
                name="userCoupon"
                label={intl.formatMessage({ id: 'shop.activity.supportCouponOverlay' })}>
                <Switch disabled={
                    [ActivityType.SPECIAL_PRICE, ActivityType.BUY_GIFT_SELF, ActivityType.EVERY_FULL_GIFT, ActivityType.LADDER_FULL_GIFT].includes(activityType) ||
                    (props.itemPackageType === ItemPackageType.SUITE_PRODUCT && activityType === ActivityType.SUITE_ITEM) ||
                    readonly} />
            </ProForm.Item>

            <ProForm.Item
                labelCol={{ span: 18 }}
                wrapperCol={{ span: 6 }}
                className='form-item-switch'
                name="onCredit"
                label={intl.formatMessage({ id: 'shop.activity.supportAccountPayment' })}>
                <Switch disabled={readonly} />
            </ProForm.Item>
            <ProForm.Item
                labelCol={{ span: 18 }}
                wrapperCol={{ span: 6 }}
                className='form-item-switch'
                name="orderNoStock"
                label={intl.formatMessage({ id: 'shop.activity.allowOrderLowStock' })}>
                <Switch disabled={readonly} />
            </ProForm.Item>

            {/* 活动备注 */}
            <ProFormTextArea
                name="remark"
                label={intl.formatMessage({ id: 'shop.activity.remarks' })}
                placeholder={intl.formatMessage({ id: 'shop.activity.remarksPlaceholder' })}
                fieldProps={{
                    maxLength: 200,
                    showCount: true,
                    autoSize: { minRows: 3, maxRows: 5 },
                }}
            />
        </>
    );
};

export default ActivityForm;