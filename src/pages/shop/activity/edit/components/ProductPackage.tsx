import DiscountPackageOutlined from '@/assets/icons/shop/suite-discounts.svg';
import SuiteProductOutlined from '@/assets/icons/shop/suite-products.svg';
import Selector from '@/components/Selector';
import { ActivityItemEntity } from '@/pages/shop/types/ActivityDataType';
import { ActivityType, ItemPackageType } from '@/pages/shop/types/ActivityEnum';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useUpdateEffect } from 'ahooks';
import NP from 'number-precision';
import React, { useMemo, useState } from 'react';
import { suiteDiscountColumns, suiteProductsColumns } from '../config/productActivityColumn';
import ProductActivityTable from './ProductActivityTable';

const options = [
	{
		key: ItemPackageType.PRODUCT_COMBINE,
		label: <FormattedMessage id="shop.activity.products.type.productCombination" />,
		icon: SuiteProductOutlined,
	},
	{
		key: ItemPackageType.SUITE_PRODUCT,
		label: <FormattedMessage id="shop.activity.products.type.discountPackage" />,
		icon: DiscountPackageOutlined,
	},
];


interface ProductPackageProps {
	tableRef: React.RefObject<EditableProTable<ActivityItemEntity>>;
	itemPackageType: ItemPackageType;
	setItemPackageType: (type: ItemPackageType) => void;
}


const ProductPackage: React.FC<ProductPackageProps> = (props) => {
	const { tableRef, dataSource, onDataChange, itemPackageType: activeKey, setItemPackageType: setActiveKey } = props;
	const [calculatedData, setCalculatedData] = useState<any>(dataSource);

	const intl = useIntl();

	useUpdateEffect(() => {
		setCalculatedData(dataSource);
	}, [dataSource]);

	const MemoProductActivityTable = useMemo((props) => {
		console.log('activeKey', activeKey);
		return <ProductActivityTable
			activityType={ActivityType.SUITE_ITEM}
			tableRef={tableRef}
			columns={activeKey === ItemPackageType.SUITE_PRODUCT ? suiteDiscountColumns : suiteProductsColumns}
			dataSource={dataSource}
			onDataChange={onDataChange}
			onValuesChange={(allValues) => {
				console.log('allValues', allValues);
				setCalculatedData(allValues);
			}}
			rowKey="itemId"
			maxHeight={600}
		/>
	}, [activeKey, dataSource]);

	const calculatedValues = useMemo(() => {
		let calculatedTotalPurchasePrice = 0; // 单买合计
		let calculatedPackageTotalPrice = 0; // 优惠价格

		console.log('calculatedData', calculatedData);

		calculatedData.forEach((item: ActivityItemEntity) => {
			calculatedTotalPurchasePrice = NP.plus(calculatedTotalPurchasePrice, NP.times(item.suggestPrice ?? 0, item.num ?? 0));
			calculatedPackageTotalPrice = NP.plus(calculatedPackageTotalPrice, NP.times(item.promotionPrice ?? 0, item.num ?? 0));
		});

		return {
			totalPurchasePrice: calculatedTotalPurchasePrice.toFixed(2),
			packageTotalPrice: calculatedPackageTotalPrice.toFixed(2),
			discountPrice: NP.minus(calculatedTotalPurchasePrice, calculatedPackageTotalPrice).toFixed(2),
		};

	}, [calculatedData]);

	return (
		<>
			<Selector options={options} activeKey={activeKey} onSelect={setActiveKey} className="mb-5" />

			{MemoProductActivityTable}

			{
				activeKey == ItemPackageType.SUITE_PRODUCT && (
					<div className="mt-4 flex">
						<div>
							<span className="text-[16px] font-bold mr-2">
								{intl.formatMessage({ id: 'shop.activity.products.totalPurchasePrice' })}:
							</span>
							<span className="text-[24px] text-primary">
								${calculatedValues.totalPurchasePrice}
							</span>
						</div>

						<div className="ml-10">
							<span className="text-[16px] font-bold mr-2">
								{intl.formatMessage({ id: 'shop.activity.products.packageTotalPrice' })}:
							</span>
							<span className="text-[24px] text-primary">
								${calculatedValues.packageTotalPrice}
								<span className="text-[16px]">
									(
									{intl.formatMessage({ id: 'shop.activity.products.discountPrice' })}:
									${calculatedValues.discountPrice}
									)
								</span>

							</span>
						</div>

					</div>
				)
			}
			{
				activeKey == ItemPackageType.PRODUCT_COMBINE && (
					<div className="mt-4">
						<span className="text-[16px] font-bold mr-2">
							{intl.formatMessage({ id: 'shop.activity.products.discountPrice' })}:
						</span>
						<span className="text-[24px] text-primary">
							${calculatedValues.totalPurchasePrice}
						</span>
					</div>
				)
			}
		</>
	);
};

export default ProductPackage;