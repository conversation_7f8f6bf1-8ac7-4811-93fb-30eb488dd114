import { FormattedMessage } from "@umijs/max";

export enum CouponStatus {
	WAIT = 1,
	ACTIVE = 2,
	INVALID = 3,
}

export enum CouponValidityType {
	ABSOLUTE = 1,
	RELATIVE = 2,
}

export const CouponStatusEnum = {
	[CouponStatus.WAIT]: { text: <FormattedMessage id="shop.coupon.couponStatus.wait" />, status: 'Warning' },
	[CouponStatus.ACTIVE]: { text: <FormattedMessage id="shop.coupon.couponStatus.active" />, status: 'Success' },
	[CouponStatus.INVALID]: { text: <FormattedMessage id="shop.coupon.couponStatus.invalid" />, status: 'Error' },
};

// export const CouponValidityTypeEnum = {
//     [CouponValidityType.ABSOLUTE]: { text: '绝对时间', status: 'Success' },
//     [CouponValidityType.RELATIVE]: { text: '相对时间', status: 'Success' },
// };



export enum GoodsRangeTypeEnum {
	PART_GOODS = 0,
	ALL_GOODS = 1,
}

export enum CouponItemScopeTypeEnum {
	ALL_GOODS = 0,
	PART_GOODS = 1,
	PART_CATRGORY = 2,
	PART_BRAND = 3,
	PART_BRAND_CATEGORY = 4,
}

export interface CouponEntity {
	id?: number;
	couponId?: number;
	couponName?: string;
	remark?: string;
	couponAmount?: string;
	validityType?: number;
	enableAmount?: string;
	totalStock?: number;
	useableStock?: number;
	validityDays?: number;
	validityBeginTime?: string;
	validityEndTime?: string;
	updatePerson?: string;
	updateTime?: string;
	couponStatus?: CouponStatus;
	validityValue?: string;
}



export enum UserCouponStatus {
	NOT_USED = 1,
	USED = 2,
	EXPIRED = 3,
	CANCELLED = 4,
}

export const UserCouponStatusEnum = {
	[UserCouponStatus.USED]: { text: <FormattedMessage id="shop.couponRecord.list.used" />, status: 'Success' },
	[UserCouponStatus.NOT_USED]: { text: <FormattedMessage id="shop.couponRecord.list.notUsed" />, status: 'Default' },
	[UserCouponStatus.EXPIRED]: { text: <FormattedMessage id="shop.couponRecord.list.expired" />, status: 'Error' },
	[UserCouponStatus.CANCELLED]: { text: <FormattedMessage id="shop.couponRecord.list.invalid" />, status: 'Error' },
};

