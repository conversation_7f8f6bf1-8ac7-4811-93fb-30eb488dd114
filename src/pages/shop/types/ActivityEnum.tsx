import { FormattedMessage } from "@umijs/max";

export enum ActivityStatus {
  DRAFT = 0,
  TAKE_EFFECT = 1,
  INVALID = 2,
}
export const ActivityStatusEnum = {
  [ActivityStatus.DRAFT]: { text: <FormattedMessage id="shop.activity.activityStatus.draft" />, status: 'Warning' },
  [ActivityStatus.TAKE_EFFECT]: { text: <FormattedMessage id="shop.activity.activityStatus.takeEffect" />, status: 'Success' },
  [ActivityStatus.INVALID]: { text: <FormattedMessage id="shop.activity.activityStatus.invalid" />, status: 'Default' },
};

export enum ActivityType {
  /**
   * 每满赠
   */
  EVERY_FULL_GIFT = 1,
  /**
   * 阶梯满赠
   */
  LADDER_FULL_GIFT = 2,
  /**
   * 买赠活动
   */
  BUY_GIFT_SELF = 3,
  /**
   * 限时特价
   */
  SPECIAL_PRICE = 4,
  /**
   * 组合套餐
   */
  SUITE_ITEM = 5,
}

export const ActivityTypeEnum = {
  [ActivityType.EVERY_FULL_GIFT]: { text: <FormattedMessage id="shop.activity.activityType.everyFullGift" /> },
  [ActivityType.LADDER_FULL_GIFT]: { text: <FormattedMessage id="shop.activity.activityType.ladderFullGift" /> },
  [ActivityType.BUY_GIFT_SELF]: { text: <FormattedMessage id="shop.activity.activityType.buyGiftSelf" /> },
  [ActivityType.SPECIAL_PRICE]: { text: <FormattedMessage id="shop.activity.activityType.specialPrice" /> },
  [ActivityType.SUITE_ITEM]: { text: <FormattedMessage id="shop.activity.activityType.suiteItem" /> },
};


export enum ActivityProgress {
  NOT_START_ACTIVITY = 1,
  IN_ACTIVITY = 2,
  END_ACTIVITY = 3,
}

export const ActivityProgressEnum = {
  [ActivityProgress.NOT_START_ACTIVITY]: { text: <FormattedMessage id="shop.activity.activityProgress.notStartActivity" />, status: 'Warning' },
  [ActivityProgress.IN_ACTIVITY]: { text: <FormattedMessage id="shop.activity.activityProgress.inActivity" />, status: 'Success' },
  [ActivityProgress.END_ACTIVITY]: { text: <FormattedMessage id="shop.activity.activityProgress.endActivity" />, status: 'Default', },
}


export enum ActivityArea {
  /**
   * 不限
   */
  NO_LIMIT = 0,
  /**
   * 指定标签客户
   */
  CST_TAG = 1,
}


export enum ItemPackageType {
  /**
   * 商品组合
   */
  PRODUCT_COMBINE = 1,
  /**
   * 优惠套餐
   */
  SUITE_PRODUCT = 2,
}