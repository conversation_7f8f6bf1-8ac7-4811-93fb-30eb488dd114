import dayjs from "dayjs";
import { CouponItemScopeTypeEnum, CouponStatus, CouponValidityType, GoodsRangeTypeEnum } from "./Coupon";

export interface CouponDataRequest {
  id: number;
  ids: number[];
  couponStatus: CouponStatus;
  couponName: string;
}

export interface UpdateCouponStatusRequest {
  id: number;
  couponStatus: CouponStatus;
}

export interface SendCouponRequest {
  id: number;
  cstSns: string[];
}

interface CategoryItem {
  categoryId: string;
  categoryName: string;
}

interface BrandItem {
  brandId: string;
  brandName: string;
}

export interface ItemItem {
  itemId: string;
  itemName: string;
  brandName: string;
  categoryName: string;
  salePrice: string;
}

/**
 * 添加优惠券表单值
 */
export interface CouponFormValues {
  couponName: string;
  totalStock: number;
  enableAmount: number;
  validityType: CouponValidityType;
  validityTime?: [dayjs.Dayjs, dayjs.Dayjs];
  validityDays?: number;
  useDesc?: string;
  remark?: string;
  goodsRangeType: GoodsRangeTypeEnum; // 0: 指定商品, 1: 全部商品
  itemScopeType?: CouponItemScopeTypeEnum; // 0:通用, 1:指定商品: 2:指定类目, 3:指定品牌, 4:指定类目+品牌
  scopeItemList?: ItemItem[];
  scopeCategoryIdList?: string[];
  scopeBrandIdList?: string[];
  scopeCategoryList?: CategoryItem[];
  scopeBrandList?: BrandItem[];
}


export type CouponFormRequest = Omit<CouponFormValues, 'validityTime' | 'scopeCategoryIdList' | 'scopeBrandIdList' | 'scopeItemList'> & {
  validityBeginTime?: string;
  validityEndTime?: string;
  scopeItemList?: Pick<ItemItem, 'itemId'>[];
}
