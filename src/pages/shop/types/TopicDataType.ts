import { TopicPageSource, TopicStatus } from "./TopicEnumType";


export interface TopicEntity {
	id?: number;
	title?: string;
	status?: TopicStatus;
	pageSource?: TopicPageSource;
	updateTime?: string;
	updatePerson?: string;
	remark?: string;
}

export type TopicDataRequest = {
	ids?: number[];
	pageNo?: number;
	pageSize?: number;
} & Pick<TopicEntity, 'title' | 'status' | 'pageSource' | 'id'>

export type TopicFormValueData = Pick<TopicEntity, 'title' | 'pageSource' | 'id' | 'remark'> & {
	moduleInfo?: string;
	status?: TopicStatus;
}