import { request } from '@/utils/request';
import { CategoryEntity } from './types/category.entity';
import { CategoryAddRequest } from './types/category.add.request';

export const queryAppExtCategoryTree = async () => {
  return request<CategoryEntity[]>(
    '/ipmsgoods/ecommerce/icAppExtCategoryFacade/queryAppExtCategoryTree',
    {
      data: { extCategoryType: 1 },
    },
  );
};

export const categoryAdd = async (params: CategoryAddRequest) => {
  return request<boolean>('/ipmsgoods/IcAppExtCategoryFacade/insert', {
    data: params,
  });
};

export const categoryUpdate = async (params: CategoryAddRequest) => {
  return request<boolean>('/ipmsgoods/IcAppExtCategoryFacade/update', {
    data: params,
  });
};

export const categoryDelete = async (id: string) => {
  return request<boolean>('/ipmsgoods/IcAppExtCategoryFacade/delete', {
    data: { id },
  });
};

export const categoryBatchUpdate = async (params: CategoryEntity[]) => {
  return request<boolean>('/ipmsgoods/IcAppExtCategoryFacade/batchUpdate', {
    data: { categoryList: params },
  });
};
