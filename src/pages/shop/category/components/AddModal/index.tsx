import { CategoryEntity } from '../../types/category.entity';
import { message, Modal, Upload } from 'antd';
import { ProForm, ProFormInstance, ProFormRadio, ProFormSelect } from '@ant-design/pro-components';
import React, { useRef } from 'react';
import { ProFormField } from '@ant-design/pro-form';
import { compressImage } from '@/utils/fileUtils';
import { useIntl } from 'react-intl';
import { PlusOutlined } from '@ant-design/icons';
import { categoryAdd, categoryUpdate } from '@/pages/shop/category/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { CategoryAddRequest } from '@/pages/shop/category/types/category.add.request';
import { useDebounceEffect } from 'ahooks';

export interface AddModalProps {
  visible: boolean;
  onClose?: () => void;
  onSuccess?: () => void;
  isEdit?: boolean;
  currentCategoryItem?: CategoryEntity;
  parentCategoryItem?: CategoryEntity;
}

export default function AddModal(props: AddModalProps) {
  const {
    visible,
    onClose,
    onSuccess,
    isEdit = false,
    currentCategoryItem,
    parentCategoryItem,
  } = props;
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();

  const [url, setUrl] = React.useState<string | undefined>(undefined);

  useDebounceEffect(
    () => {
      if (visible && isEdit && currentCategoryItem) {
        formRef.current?.setFieldsValue(currentCategoryItem);
        setUrl(currentCategoryItem.extCategoryPic);
      } else if (visible && !isEdit) {
        formRef.current?.setFieldsValue({
          jumpType: 1,
          isShow: 1,
          parentId: parentCategoryItem?.id ?? '0',
        });
      }
      return () => {
        formRef.current?.resetFields();
      };
    },
    [visible, isEdit, currentCategoryItem, parentCategoryItem],
    { wait: 50 },
  );

  const handleOk = async (values: CategoryAddRequest) => {
    if (isEdit) {
      const res = await categoryUpdate({
        ...currentCategoryItem,
        ...values,
        extCategoryPic: url,
      });
      if (res) {
        message.success(intl.formatMessage({ id: 'common.message.save.success' }));
        onClose?.();
        onSuccess?.();
      }
    } else {
      const res = await categoryAdd({
        ...values,
        extCategoryType: 1,
        categoryLevel: (parentCategoryItem?.categoryLevel ?? 0) + 1,
        extCategoryPic: url,
      });
      if (res) {
        message.success(intl.formatMessage({ id: 'common.message.save.success' }));
        onClose?.();
        onSuccess?.();
      }
    }
  };

  const handleUploadChange = (info: any) => {
    if (info.file.status === 'done') {
      const url = info.file?.response?.data?.[0];
      if (url) {
        setUrl(url);
      } else {
        message.error(info.file?.response?.msg);
      }
    } else if (info.file.status === 'error') {
      console.error('Upload error:', info.file.error);
    }
  };

  return (
    <Modal
      title={
        isEdit
          ? intl.formatMessage({ id: 'shop.category.edit' })
          : intl.formatMessage({ id: 'shop.category.addNew' })
      }
      open={visible}
      destroyOnClose
      footer={false}
      onCancel={onClose}
    >
      <ProForm
        layout="vertical"
        labelAlign="left"
        className="p-4"
        formRef={formRef}
        onFinish={handleOk}
        submitter={{
          resetButtonProps: false,
        }}
      >
        <ProFormField
          label={intl.formatMessage({ id: 'shop.category.categoryName' })}
          name="extCategoryName"
          rules={[REQUIRED_RULES]}
        />
        {parentCategoryItem?.categoryLevel && parentCategoryItem?.categoryLevel > 0 && (
          <ProFormSelect
            label={intl.formatMessage({ id: 'shop.category.parent' })}
            name="parentId"
            rules={[REQUIRED_RULES]}
            disabled={true}
            options={[
              { label: parentCategoryItem?.extCategoryName, value: parentCategoryItem?.id },
            ]}
          />
        )}
        <ProFormRadio.Group
          label={intl.formatMessage({ id: 'shop.category.status' })}
          name="isShow"
          options={[
            { label: intl.formatMessage({ id: 'shop.category.show' }), value: 1 },
            { label: intl.formatMessage({ id: 'shop.category.hide' }), value: 0 },
          ]}
        />
        {((currentCategoryItem?.categoryLevel === 3 && isEdit) ||
          (parentCategoryItem?.categoryLevel === 2 && !isEdit)) && (
          <>
            <ProFormRadio.Group
              label={intl.formatMessage({ id: 'shop.category.jumpType' })}
              name="jumpType"
              options={[{ label: intl.formatMessage({ id: 'shop.category.keyword' }), value: 1 }]}
            />
            <ProFormField
              label={intl.formatMessage({ id: 'shop.category.jumpContent' })}
              name="keyWord"
              rules={[REQUIRED_RULES]}
            />
            <ProForm.Item
              label={intl.formatMessage({ id: 'shop.category.image' })}
              rules={[REQUIRED_RULES]}
            >
              <Upload
                name="file"
                listType="picture-card"
                showUploadList={false}
                action="/apigateway/public/upload/object/batch"
                beforeUpload={(file) => compressImage(file)}
                onChange={handleUploadChange}
              >
                {url ? (
                  <img src={url} className="w-full h-full" />
                ) : (
                  <div className="opacity-40">
                    <PlusOutlined className="text-[30px]" />
                    <div>{intl.formatMessage({ id: 'shop.category.updateImage' })}</div>
                  </div>
                )}
              </Upload>
            </ProForm.Item>
          </>
        )}
      </ProForm>
    </Modal>
  );
}
