import { CategoryStatus } from './category.status';

export interface CategoryEntity {
  /**
   * 类目等级1级类目2级类目3级类目
   */
  categoryLevel: number;
  /**
   * 子节点
   */
  children?: CategoryEntity[];
  /**
   * 前台类目名称
   */
  extCategoryName?: string;
  /**
   * 前台类目排序
   */
  extCategoryOrder?: number;
  /**
   * 前台类目图片
   */
  extCategoryPic?: string;
  /**
   * 前台类目类型(可扩展)：1:商城
   */
  extCategoryType?: number;
  /**
   * 主键
   */
  id: string;
  /**
   * 前台类目状态0删除1使用
   */
  isDelete?: number;
  /**
   * 是否显示1显示0不显示
   */
  isShow?: CategoryStatus;
  /**
   * 跳转类型(可扩展)：0不跳转，查询并展示下级扩展类目；1跳转到搜索页并使用扩展分类配置的搜索关键字进行搜索
   */
  jumpType?: number;
  /**
   * 匹配关键字
   */
  keyWord?: string;
  /**
   * memberId
   */
  memberId?: string;
  /**
   * 父类ID
   */
  parentId?: string;
}
