import {
  PageContainer,
  ProCard,
  ProDescriptions,
  type ProFormInstance,
} from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'umi';
import { queryPurchaseReplenishListPage, queryPurchaseSuggestionDetail } from './services';
import { type SuggestionItemTableEntity } from './types/detail.response.entity';
import { Button, Space, Tag } from 'antd';
import { StockUpStatusOptions } from '../list/types/StockUpStatus';
import LeftTitle from '@/components/LeftTitle';
import { PostListTableColumns } from './config/postListTableColumns';
import FunProTable from '@/components/common/FunProTable';
import { useIntl } from '@@/exports';
import { exportData } from '@/utils/exportData';

export default () => {
  const [searchParams] = useSearchParams();
  const suggestionNo = searchParams.get('suggestionNo');
  const intl = useIntl();
  const [extraColumns, setExtraColumns] = useState<any[]>([]);
  const formRef = useRef<ProFormInstance>();

  const {
    data: suggestionDetail,
    loading,
    run,
  } = useRequest(() => queryPurchaseSuggestionDetail(suggestionNo!));

  useEffect(() => {
    if (suggestionNo) {
      run();
    }
  }, [suggestionNo]);

  return (
    <PageContainer loading={loading}>
      <ProCard>
        <ProDescriptions
          title={
            <Space>
              <span>{suggestionNo}</span>
              {/**@ts-ignore**/}
              <Tag color={StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.status}>
                {/**@ts-ignore**/}
                {StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.text}
              </Tag>
            </Space>
          }
          dataSource={suggestionDetail}
          column={3}
        >
          <ProDescriptions.Item dataIndex="createTime" label="创建时间" />
          <ProDescriptions.Item dataIndex="updateTime" label="更新时间" />
          <ProDescriptions.Item dataIndex="createPerson" label="创建人" />
        </ProDescriptions>
      </ProCard>
      <ProCard className="mt-4">
        <LeftTitle title="采购建议明细" />
        <FunProTable<SuggestionItemTableEntity>
          rowKey="id"
          ghost={true}
          scroll={{ x: '1300' }}
          recordCreatorProps={false}
          params={{
            suggestionNo,
          }}
          formRef={formRef}
          form={{ className: '!pb-0 !px-0' }}
          headerTitle={
            <Button
              type="primary"
              ghost={true}
              onClick={() => {
                exportData({
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: '调拨建议明细导出',
                  moduleId: 'TRANSFER_SUGGEST_LINE_EXPORT',
                  params: {
                    ...formRef.current?.getFieldsValue(),
                    suggestionNo,
                  },
                });
              }}
            >
              下载明细
            </Button>
          }
          // @ts-ignore
          request={async (params) => {
            if (params?.suggestionNo) {
              const result = await queryPurchaseReplenishListPage(params);
              const _extraColumns: any[] = [];
              // @ts-ignore
              result?.data?.forEach((m: any, index) => {
                m.storeDetails?.forEach((n: any) => {
                  if (index === 0) {
                    _extraColumns.push({
                      title: `${n.warehouseName}可用库存`,
                      dataIndex: n.warehouseId,
                      width: 120,
                      hideInSearch: true,
                    });
                  }
                  m[n.warehouseId] = n.totalAvaNum;
                });
              });
              setExtraColumns(_extraColumns);
              return {
                data: result?.data,
                success: true,
                // @ts-ignore
                total: result?.total,
              };
            } else {
              return { data: [], success: true, total: 0 };
            }
          }}
          toolbar={{ settings: [] }}
          tableAlertRender={false}
          columns={[...PostListTableColumns({ intl }), ...extraColumns]}
        />
      </ProCard>
    </PageContainer>
  );
};
