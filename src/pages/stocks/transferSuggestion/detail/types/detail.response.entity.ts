export enum SuggestionStatus {
  /**
   * 生效(已生成)
   */
  EFFECTIVE = '1',
  /**
   * 失效
   */
  INVALID = '2',
  /**
   * 生成中
   */
  GENERATING = '3',
  /**
   * 已补货
   */
  REPLENISHED = '4',
}

/**
 * 补货建议类型
 */
export enum SuggestionType {
  /**
   * 一体系补货
   */
  Stocking = '1',
  /**
   * 门店补货
   */
  Replenish = '2',
}
/**
 * 补货建议详情查询
 */
export interface DetailResponseEntity {
  /**
   * 补货建议类型
   */
  suggestionType?: SuggestionType;
  /**
   * 批次号
   */
  batchNo?: string;
  /**
   * 创建人（供大数据使用）
   */
  createPerson?: string;
  /**
   * 创建时间（供大数据使用）
   */
  createTime?: string;
  /**
   * 一体系零售商id
   */
  etcMemberId?: string;
  /**
   * 一体系补货建议id
   */
  etcSuggestionId?: string;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商name
   */
  memberName?: string;
  /**
   * 门店id，多个逗号拼接
   */
  storeId?: string;
  /**
   * 补货建议名称
   */
  suggestionName?: string;
  /**
   * 补货单号
   */
  suggestionNo?: string;
  /**
   * 补货建议状态0-生效1-失效
   */
  suggestionStatus?: SuggestionStatus;
  /**
   * 更新人（供大数据使用）
   */
  updatePerson?: string;
  /**
   * 更新时间（供大数据使用）
   */
  updateTime?: string;
}

export interface SuggestionItemTableEntity {
  /**
   * 标准适用车型
   */
  adaptCarModel?: string;
  /**
   * 适用车系
   */
  adaptSeries?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 供应商编码
   */
  brandPartNoList?: string[];
  /**
   * 分类
   */
  categoryName?: string;
  /**
   * 近30日销量
   */
  dSales30?: number;
  /**
   * 日均销量
   */
  dSalesAvg?: number;
  /**
   * etc号
   */
  etcNo?: string;
  /**
   * 有无活动：0-无活动1-有活动
   */
  hasActivity?: number;
  /**
   * 一体系库存：0-无货1-有货
   */
  hasInventory?: string;
  /**
   * id
   */
  id?: string;

  brandId?: string;
  skuId?: string;
  unitId?: string;
  categoryId?: string;
  /**
   * 有无套装
   */
  isSuit?: number;
  /**
   * 本地库存
   */
  localStock?: number;
  /**
   * 库存上限
   */
  maxStock?: number;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装数
   */
  minPackNum?: number;
  /**
   * 库存下限
   */
  minStock?: number;
  /**
   * OE
   */
  oe?: string[];
  /**
   * 采购单价
   */
  price?: number;
  /**
   * 采购在途
   */
  purchaseOnTheWay?: number;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 建议补货等级0-高频件1-中频件2-低频件
   */
  suggestionGrade?: string;
  /**
   * 建议补货数量
   */
  suggestionNum?: number;
  /**
   * 单位
   */
  unit?: string;
  /**
  /**
   * 关联零售商商品ID
   */
  memberItemId?: string;
}

export interface ReplenishListItemEntity {
  /**
   * 标准适用车型
   */
  adaptCarModel?: string;
  /**
   * 车型备注
   */
  adaptModel?: string;
  /**
   * 适用车系
   */
  adaptSeries?: string;
  /**
   * 调拨在途
   */
  allocateOnTheWay?: number;
  /**
   * 已补货数量
   */
  alreadyReplenishNum?: number;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNoList?: string[];
  /**
   * 供应商编码
   */
  brandPartNos?: string;
  /**
   * 分类id
   */
  categoryId?: string;
  /**
   * 分类
   */
  categoryName?: string;
  /**
   * 近30日销量
   */
  dSales30?: number;
  /**
   * 日均销量
   */
  dSalesAvg?: number;
  /**
   * etc号
   */
  etcNo?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 逻辑删除，0否，1是，默认0
   */
  isDelete?: number;
  /**
   * 商品通用组组成明细
   */
  itemGroupDetail?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 可用库存
   */
  localAvailableStock?: number;
  /**
   * 本地库存
   */
  localStock?: number;
  /**
   * 采购库存上限
   */
  maxPurchaseStock?: number;
  /**
   * 库存上限
   */
  maxStock?: number;
  /**
   * 商品id
   */
  memberItemId?: string;
  /**
   * 采购库存下限
   */
  minPurchaseStock?: number;
  /**
   * 库存下限
   */
  minStock?: number;
  /**
   * OE,前端下拉展示
   */
  oeList?: string[];
  /**
   * 采购单价
   */
  price?: number;
  /**
   * 采购单号
   */
  purchaseNos?: string[];
  /**
   * 采购在途
   */
  purchaseOnTheWay?: number;
  /**
   * 建议补货数量
   */
  replenishNum?: number;
  /**
   * 销售数量
   */
  saleNum?: number;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 建议门店组成
   */
  storeDetails?: StoreDetail[];
  /**
   * 补货单号
   */
  suggestionNo?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商信息
   */
  supplierList?: SupplierList[];
  /**
   * 供应商名称
   */
  supplierName?: string;
  /**
   * 总可用库存
   */
  totalAvaNum?: number;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库name
   */
  warehouseName?: string;
}

export interface StoreDetail {
  /**
   * 销售数量
   */
  saleNum?: number;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 建议数量
   */
  suggestNum?: number;
  /**
   * 可用库存
   */
  totalAvaNum?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

export interface SupplierList {
  /**
   * None
   */
  supplierId?: string;
  /**
   * None
   */
  supplierName?: string;
}
