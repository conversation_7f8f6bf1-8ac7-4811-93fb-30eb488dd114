import ColumnRender from '@/components/ColumnRender';
import { type ProColumns } from '@ant-design/pro-components';
import type { SuggestionItemTableEntity } from '../types/detail.response.entity';
import { Space } from 'antd';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { transformCategoryTree } from '@/utils/transformCategoryTree';

export interface PostListTableColumnsProps {
  intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '商品名称',
      dataIndex: 'itemName',
      width: 140,
      hideInSearch: true,
    },

    {
      title: props.intl.formatMessage({ id: 'goods.search.form.goodsInfo' }),
      dataIndex: 'queryKeyWord',
      fieldProps: {
        placeholder: props.intl.formatMessage({ id: 'goods.search.form.goodsInfoPlaceholder' }),
      },
      width: 140,
      hideInTable: true,
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 100,
      valueType: 'select',
      formItemProps: {
        name: 'brandIdList',
      },
      fieldProps: {
        showSearch: true,
        filterOption: false,
        mode: 'multiple',
        optionRender: (option: any) => <Space>{option.data.label}</Space>,
      },
      request: async ({ keyWords: brandName }) => {
        const { data } = await queryGoodsPropertyPage(
          { brandName, pageNo: 1, pageSize: 1000 },
          'brand',
        );
        return data.map((t: any) => ({
          label: t.brandName,
          dataType: t.dataType,
          value: t.brandId,
        }));
      },
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      width: 100,
      valueType: 'treeSelect',
      formItemProps: {
        name: 'categoryIdList',
      },
      fieldProps: {
        treeCheckable: true,
        maxTagCount: 3,
        filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
      },
      request: () => {
        return queryGoodsPropertyPage(
          { pageSize: 999, pageNo: 1, isReturnTree: true },
          'category',
        ).then((result) => transformCategoryTree(result.data));
      },
    },
    {
      title: '供应商编码',
      dataIndex: 'brandPartNoList',
      width: 100,
      hideInSearch: true,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '补货仓库',
      dataIndex: 'warehouseName',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '可用总库存',
      dataIndex: 'totalAvaNum',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '库存下限',
      dataIndex: 'minStock',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '库存上限',
      dataIndex: 'maxStock',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '建议补货数量',
      dataIndex: 'replenishNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '建议调货仓库',
      dataIndex: 'suggestWarehouseName',
      width: 100,
      hideInSearch: true,
    },
  ] as ProColumns<SuggestionItemTableEntity>[];
