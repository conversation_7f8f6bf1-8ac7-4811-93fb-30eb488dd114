import { FormattedMessage } from "@umijs/max";

export enum StockUpStatus {
  EFFECTIVE = '1',
  EXPIRE = '2',
  GENERATING = '3',
  REPLENISHED = '4',
}

export enum StockUpStatusName {
  EFFECTIVE = 'purchase.stockUpStatus.effective',
  EXPIRE = 'purchase.stockUpStatus.expire',
  GENERATING = 'purchase.stockUpStatus.generating',
  REPLENISHED = 'purchase.stockUpStatus.replenished',
}
export const StockUpStatusOptions = {
  [StockUpStatus.EFFECTIVE]: { text: <FormattedMessage id={StockUpStatusName.EFFECTIVE} />, status: 'success' },
  [StockUpStatus.EXPIRE]: { text: <FormattedMessage id={StockUpStatusName.EXPIRE} />, status: 'default' },
  [StockUpStatus.GENERATING]: { text: <FormattedMessage id={StockUpStatusName.GENERATING} />, status: 'processing' },
  [StockUpStatus.REPLENISHED]: { text: <FormattedMessage id={StockUpStatusName.REPLENISHED} />, status: 'success' },
};
