import { FormattedMessage } from "@umijs/max";

export enum DeliveryState {
  /** 待领取 */
  PENDING = 1,
  /** 已领取 */
  PICKED_UP = 2,
  /** 配送中 */
  IN_DELIVERY = 3,
  /** 配送完成 */
  COMPLETED = 4,
  /** 已取消 */
  CANCELLED = 5,
}

export const DeliveryStateMap = {
  [DeliveryState.PENDING]: {
    text: <FormattedMessage id="stocks.delivery.list.state.pending" />,
    status: 'Default',
  },
  [DeliveryState.PICKED_UP]: {
    text: <FormattedMessage id="stocks.delivery.list.state.pickedUp" />,
    status: 'Processing',
  },
  [DeliveryState.IN_DELIVERY]: {
    text: <FormattedMessage id="stocks.delivery.list.state.inDelivery" />,
    status: 'Processing',
  },
  [DeliveryState.COMPLETED]: {
    text: <FormattedMessage id="stocks.delivery.list.state.completed" />,
    status: 'Success',
  },
  [DeliveryState.CANCELLED]: {
    text: <FormattedMessage id="stocks.delivery.list.state.cancelled" />,
    status: 'Error',
  },
}

/**
 * Delivery type enum
 */
export enum DeliveryType {
  /** 送货 */
  DELIVERY = 1,
  /** 取货 */
  PICKUP = 2,
}

export const DeliveryTypeMap = {
  [DeliveryType.DELIVERY]: {
    text: <FormattedMessage id="stocks.delivery.list.billType.delivery" />,
  },
  [DeliveryType.PICKUP]: {
    text: <FormattedMessage id="stocks.delivery.list.billType.pickup" />,
  },
}

/**
 * Distribution mode enum
 */
export enum DistributionMode {
  /** 客户自提 */
  SELF_PICKUP = 1,
  /** 商家配送 */
  MERCHANT_DELIVERY = 2,
  /** 快递物流 */
  LOGISTICS = 3,
}

export const DistributionModeMap = {
  [DistributionMode.SELF_PICKUP]: {
    text: <FormattedMessage id="stocks.delivery.list.distributionMode.selfPickup" />,
  },
  [DistributionMode.MERCHANT_DELIVERY]: {
    text: <FormattedMessage id="stocks.delivery.list.distributionMode.merchantDelivery" />,
  },
  [DistributionMode.LOGISTICS]: {
    text: <FormattedMessage id="stocks.delivery.list.distributionMode.logistics" />,
  },
}


export enum TargetType {
  /**
   * 供应商
   */
  SUPPLIER = 1,
  /**
   * 客户
   */
  CUSTOMER = 2,
}
