import { DeliveryState, DeliveryType, DistributionMode, TargetType } from "./delivery.enums";

export interface DeliveryEntity {
    id?: string;
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 业务类型,1-送货2-取货
     */
    billType?: DeliveryType;
    /**
     * 业务类型描述,1-送货2-取货
     */
    billTypeDesc?: string;
    /**
     * 配送单号
     */
    bizBillNo?: string;
    /**
     * 联系人FirstName
     */
    contactFirstName?: string;
    /**
     * 联系人LastName
     */
    contactLastName?: string;
    /**
     * 联系人first_name、last_name拼接
     */
    contactName?: string;
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 配送地址
     */
    deliveryAddress?: string;
    /**
     * 配送员
     */
    deliveryMan?: string;
    /**
     * 配送员id
     */
    deliveryManId?: string;
    /**
     * 配送对象编码(供应商id、客户id)
     */
    deliveryTargetId?: string;
    /**
     * 配送对象名称(供应商名称、客户名称)
     */
    deliveryTargetName?: string;
    /**
     * 配送对象类型，1-供应商2-客户
     */
    deliveryTargetType?: TargetType;
    /**
     * 配送方式（1：客户自提、2：商家配送、3：快递物流）
     */
    distributionMode?: DistributionMode;
    /**
     * 期望到达时间
     */
    expectedArrTime?: string;
    /**
     * 完成时间
     */
    finishTime?: string;
    /**
     * 配送图片,个以逗号分隔
     */
    images?: string;
    /**
     * 加急标识0：否1：加急
     */
    isUrgent?: number;
    /**
     * 物流公司id
     */
    logisticsCompanyCode?: string;
    /**
     * 物流公司名称
     */
    logisticsCompanyName?: string;
    /**
     * 物流单号
     */
    logisticsNo?: string;
    /**
     * 零售商id
     */
    memberId?: string;
    /**
     * 业务单号
     */
    origBillNo?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 来源0-系统自动生成1-手动添加默认手动添加
     */
    source?: number;
    /**
     * 状态：未配送、配送中、配送完成
     */
    state?: DeliveryState;
    /**
     * 状态描述：未配送、配送中、配送完成
     */
    stateDesc?: string;
    /**
     * 出库单号,冗余
     */
    stockOutBillNo?: string;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 仓库id
     */
    warehouseId?: string;
    /**
     * 仓库名称
     */
    warehouseName?: string;
}




export interface DeliveryDetailEntity {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 业务类型,1-送货2-取货
     */
    billType?: number;
    /**
     * 业务类型描述,1-送货2-取货
     */
    billTypeDesc?: string;
    /**
     * 配送单号
     */
    bizBillNo?: string;
    /**
     * 联系人FirstName
     */
    contactFirstName?: string;
    /**
     * 联系人LastName
     */
    contactLastName?: string;
    /**
     * 联系人first_name、last_name拼接
     */
    contactName?: string;
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * none
     */
    createPerson?: string;
    /**
     * none
     */
    createTime?: string;
    /**
     * 配送地址
     */
    deliveryAddress?: string;
    /**
     * 配送员
     */
    deliveryMan?: string;
    /**
     * 配送员id
     */
    deliveryManId?: string;
    /**
     * 配送对象编码(供应商id、客户id)
     */
    deliveryTargetId?: string;
    /**
     * 配送对象名称(供应商名称、客户名称)
     */
    deliveryTargetName?: string;
    /**
     * 配送对象类型，1-供应商2-客户
     */
    deliveryTargetType?: number;
    /**
     * 配送方式（1：客户自提、2：商家配送、3：快递物流）
     */
    distributionMode?: number;
    /**
     * 期望到达时间
     */
    expectedArrTime?: string;
    /**
     * 完成时间
     */
    finishTime?: string;
    /**
     * 主键id
     */
    id?: string;
    /**
     * 配送图片,个以逗号分隔
     */
    images?: string;
    /**
     * none
     */
    isDelete?: number;
    /**
     * 加急标识0：否1：加急
     */
    isUrgent?: number;
    /**
     * 物流公司id
     */
    logisticsCompanyCode?: string;
    /**
     * 物流公司名称
     */
    logisticsCompanyName?: string;
    /**
     * 物流单号
     */
    logisticsNo?: string;
    /**
     * 零售商id
     */
    memberId?: string;
    /**
     * 业务单号
     */
    origBillNo?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 来源0-系统自动生成1-手动添加默认手动添加
     */
    source?: number;
    /**
     * 状态：未配送、配送中、配送完成
     */
    state?: number;
    /**
     * 状态描述：未配送、配送中、配送完成
     */
    stateDesc?: string;
    /**
     * 出库单号,冗余
     */
    stockOutBillNo?: string;
    /**
     * 出库单明细
     */
    stockOutDetailRoList?: StockOutDetailRoList[];
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * none
     */
    updatePerson?: string;
    /**
     * none
     */
    updateTime?: string;
    /**
     * 仓库id
     */
    warehouseId?: string;
    /**
     * 仓库名称
     */
    warehouseName?: string;
}

export interface StockOutDetailRoList {
    /**
     * 品牌Id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌件号
     */
    brandPartNo?: string;
    /**
     * 类目Id
     */
    categoryId?: string;
    /**
     * 类目名称
     */
    categoryName?: string;
    /**
     * 货位备注,多个以,分隔
     */
    code?: string;
    /**
     * none
     */
    createPerson?: string;
    /**
     * none
     */
    createTime?: string;
    /**
     * 主键
     */
    id?: string;
    /**
     * 图片列表
     */
    images?: string[];
    /**
     * none
     */
    isDelete?: number;
    /**
     * 商品ID
     */
    itemId?: string;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * none
     */
    memberId?: string;
    /**
     * oe码
     */
    oeNo?: string;
    /**
     * 预出库数量
     */
    preAmount?: number;
    /**
     * 实际出库数量
     */
    realAmount?: number;
    /**
     * 剩余数量（预出库数量-实际出库数量）
     */
    remainAmount?: number;
    /**
     * 备注
     */
    remarks?: string;
    /**
     * 扫描数量
     */
    scanAmount?: number;
    /**
     * 出库ID
     */
    stockOutId?: string;
    /**
     * 三方明细行id
     */
    thirdDetailId?: string;
    /**
     * 三方商品号-ETC号
     */
    thirdNo?: string;
    /**
     * 单位id
     */
    unitId?: string;
    /**
     * 单位名称
     */
    unitName?: string;
    /**
     * none
     */
    updatePerson?: string;
    /**
     * none
     */
    updateTime?: string;
    /**
     * 版本号
     */
    version?: number;
}


