export interface AddCheckRequest {
  /**
   * 盘点单号
   */
  bizBillNo?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 盘点方式1:明盘2:盲盘
   */
  mode?: number;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 盘点明细
   */
  stockCheckDetailCmdList?: StockCheckDetailCmdList[];
  /**
   * 盘点类型1:全盘2:抽盘
   */
  type?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
}

export interface StockCheckDetailCmdList {
  /**
   * 盘点数量
   */
  checkAmount?: number;
  /**
   * 主表id
   */
  checkId?: string;
  /**
   * 成本价
   */
  costPrice?: number;
  /**
   * 差异库存(盘点数量-当前库存)
   */
  diffAmount?: number;
  /**
   * None
   */
  extRemark?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 当前库存(提交损益的库存快照)
   */
  stockAmount?: number;
}
