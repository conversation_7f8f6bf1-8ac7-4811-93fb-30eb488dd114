import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { CommonModelForm } from '@/types/CommonModelForm';
import { ProFormSelect, ProFormTextArea } from '@ant-design/pro-components';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { useAsyncEffect } from 'ahooks';
import { Button, SelectProps, Tag, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useState } from 'react';
import { useIntl } from 'umi';
import { queryRelationStoreByIdPost } from '../../../services';
import { PostEntity } from '../../types/post.entity';
export default (props: CommonModelForm<string, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  const [selectStoreId, setSelectStoreId] = useState<string[] | undefined>([]);

  const [warehouseStoreInfoRoList, setWarehouseStoreInfoRoList] = useState<any>([]);

  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
      setSelectStoreId([]);
      const data = await queryStoreByAccount({ status: 1 });
      setWarehouseStoreInfoRoList(data);
    } else {
      const data = await queryRelationStoreByIdPost({ warehouseId: props.recordId });
      form.setFieldsValue(data);
      const roList = data?.warehouseStoreInfoRoList?.map((s) => ({
        id: s.storeId,
        name: s.storeName,
        disabled: s.warehouseRelateStoreExist == 0,
      }));
      if (roList) {
        setWarehouseStoreInfoRoList(roList);
      }
      if (data) {
        setSelectStoreId(data?.storeIdList);
      }
    }
  }, [props.visible]);

  type TagRender = SelectProps['tagRender'];

  const tagRender: TagRender = (props) => {
    const { label, value, closable, onClose, disabled } = props;
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    console.log(disabled);

    return (
      <Tag
        color={disabled ? 'red' : 'green'}
        onMouseDown={onPreventMouseDown}
        closable={true}
        onClose={onClose}
        style={{ marginInlineEnd: 4 }}
      >
        {label}
      </Tag>
    );
  };

  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !props.readOnly }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width="30%"
      modalProps={{
        centered: true,
        onCancel: props.onCancel,
        destroyOnClose: true,
      }}
      submitter={props.readOnly ? submitter : {}}
      onFinish={props.onOk}
    >
      <ProFormText name="id" disabled={props.readOnly} hidden={true} />
      <ProFormText
        rules={[{ required: !props.readOnly, max: 100 }]}
        name="warehouseName"
        fieldProps={{ maxLength: 100 }}
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'stocks.warehouse.label.warehouseName' })}
        placeholder={intl.formatMessage(
          { id: 'common.placeholder.inputWithMaxLength' },
          { maxLength: 100 },
        )}
      />
      <ProFormText name="storeId" disabled={props.readOnly} hidden={true} />
      <ProFormSelect
        name="storeIdList"
        mode="multiple"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'stocks.warehouse.label.affiliatedStore' })}
        rules={rules}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        tooltip={intl.formatMessage({ id: 'stocks.warehouse.tooltip.disabledStore' })}
        fieldProps={{
          fieldNames: { label: 'name', value: 'id' },
          value: selectStoreId,
          allowClear: false,
          tagRender: tagRender,
          onDeselect: (e) => {
            if (form.getFieldValue('storeId') == e) {
              //默认门店不支持删除
              message.warning(
                intl.formatMessage({
                  id: 'stocks.warehouse.message.defaultStoreDeletionNotAllowed',
                }),
              );
              selectStoreId?.push(e);
              return true;
            } else {
              setSelectStoreId(selectStoreId?.filter((t) => t != e));
            }
          },
          onSelect: (e) => {
            selectStoreId?.push(e);
          },
        }}
        options={warehouseStoreInfoRoList}
      />
      <ProFormTextArea
        name="remark"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'common.label.remark' })}
        placeholder={intl.formatMessage(
          { id: 'common.placeholder.inputWithMaxLength' },
          { maxLength: 100 },
        )}
        fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
        rules={[{ max: 100 }]}
      />
    </ModalForm>
  );
};
