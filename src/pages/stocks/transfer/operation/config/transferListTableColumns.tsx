import ColumnRender from '@/components/ColumnRender';
import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Popconfirm } from 'antd';
import type { TransferDetailPostEntity } from '../../detail/types/transfer.detail.post.entity';

export interface TransferListTableColumnsProps {
  handleDeleteItem: (id: string) => void;
}

export const TransferListTableColumns = (props: TransferListTableColumnsProps) => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });

  return [
    {
      title: t('common.column.index'),
      valueType: 'index',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 40,
      fixed: 'left',
    },
    {
      title: t('goods.list.table.itemSn'),
      dataIndex: 'itemSn',
      key: 'itemSn',
      readonly: true,
      width: 100,
    },
    {
      title: t('goods.list.table.itemName'),
      dataIndex: 'itemName',
      key: 'itemName',
      readonly: true,
      width: 140,
    },
    {
      title: t('goods.list.table.oeNos'),
      dataIndex: 'oeNo',
      key: 'oeNo',
      readonly: true,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: t('goods.list.table.brandPartNos'),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      readonly: true,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: t('goods.list.table.brandName'),
      dataIndex: 'brandName',
      key: 'brandName',
      readonly: true,
      width: 100,
    },
    {
      title: t('goods.list.table.categoryName'),
      dataIndex: 'categoryName',
      key: 'categoryName',
      readonly: true,
      width: 100,
    },
    {
      title: t('goods.list.table.unit'),
      dataIndex: 'unitName',
      key: 'unitName',
      readonly: true,
      width: 50,
    },
    {
      title: t('stocks.transfer.operation.goodsModal.columns.outWarehouseStock'),
      dataIndex: 'inventoryNum',
      key: 'inventoryNum',
      readonly: true,
      width: 100,
      fixed: 'right',
    },
    {
      title: t('stocks.transfer.detail.columns.transferQuantity'),
      dataIndex: 'transferNum',
      key: 'transferNum',
      valueType: 'digit',
      readonly: false,
      width: 120,
      fixed: 'right',
      fieldProps: (_, config) => {
        return {
          min: 0,
        };
      },
    },
    {
      title: t('common.column.operation'),
      editable: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Popconfirm
          title={t('common.confirm.delete')}
          onConfirm={() => props.handleDeleteItem(record.id)}
        >
          <AuthButton isHref authority="removeTransferItem">
            {t('common.button.delete')}
          </AuthButton>
        </Popconfirm>
      ),
    },
  ] as ProColumns<TransferDetailPostEntity>[];
};
