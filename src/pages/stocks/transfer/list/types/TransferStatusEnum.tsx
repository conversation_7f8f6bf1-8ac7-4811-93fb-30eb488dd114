import { FormattedMessage } from '@umijs/max';

export enum TransferStatusEnum {
  CANCEL = 0,
  DRAFT = 1,
  PENDING_SHIPMENT = 2,
  PENDING_ARRIVAL = 3,
  COMPLETED = 4,
}

export enum TransferBillType {
  // 销售调拨
  SALE_TRANSFER = 1,
  // 补货调拨
  REPLENISHMENT_TRANSFER = 2,
}


export const TransferStatusNameOptions = {
  [TransferStatusEnum.CANCEL]: { text: <FormattedMessage id="stocks.transfer.status.cancel" />, status: 'default' },
  [TransferStatusEnum.DRAFT]: { text: <FormattedMessage id="stocks.transfer.status.draft" />, status: 'processing' },
  [TransferStatusEnum.PENDING_SHIPMENT]: { text: <FormattedMessage id="stocks.transfer.status.pendingShipment" />, status: 'processing' },
  [TransferStatusEnum.PENDING_ARRIVAL]: { text: <FormattedMessage id="stocks.transfer.status.inTransit" />, status: 'error' },
  [TransferStatusEnum.COMPLETED]: { text: <FormattedMessage id="stocks.transfer.status.completed" />, status: 'success' },
};

export const TransferBillTypeNameOptions = {
  [TransferBillType.SALE_TRANSFER]: { text: <FormattedMessage id="stocks.transfer.billType.saleTransfer" /> },
  [TransferBillType.REPLENISHMENT_TRANSFER]: { text: <FormattedMessage id="stocks.transfer.billType.replenishmentTransfer" /> },
};
