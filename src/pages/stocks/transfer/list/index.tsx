import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { useRef } from 'react';
import { useActivate } from 'react-activation';
import { queryCheckPagePost } from '../services';
import { PostListTableColumns } from './config/postListTableColumns';
import type { TransferPostEntity } from './types/transfer.post.entity';

const TransferList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  useActivate(() => {
    actionRef.current?.reload();
  });

  return (
    <PageContainer>
      <FunProTable<TransferPostEntity, any>
        rowKey="id"
        requestPage={queryCheckPagePost}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns()}
        headerTitle={
          <AuthButton
            type="primary"
            key="primary"
            authority="addWarehouseTransfer"
            onClick={() => history.push('/purchase/transfer/operation')}
          >
            {intl.formatMessage({ id: 'stocks.transfer.list.button.newTransfer' })}
          </AuthButton>
        }
      />
    </PageContainer>
  );
};
export default withKeepAlive(TransferList);
