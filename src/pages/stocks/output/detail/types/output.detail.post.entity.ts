import { DistributionModeStatus } from '../../list/types/DistributionModeStatus';
import { OutPutStatus } from '../../list/types/OutPutStatus';

export interface OutPutDetailPostEntity {
  /**
   * 出库单明细
   */
  stockOutDetailRoList?: StockOutDetailRoList[];
  /**
   * 主单明细
   */
  stockOutRo?: StockOutRo;
}

export interface StockOutDetailRoList {
  /**
   * 品牌Id
   */
  brandId?: string;
  locationCode?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 供应商编码
   */
  brandPartNo?: string;
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 货位备注,多个以,分隔
   */
  code?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * 预出库数量
   */
  preAmount?: number;
  /**
   * 实际出库数量
   */
  realAmount?: number;
  /**
   * 剩余数量（预出库数量-实际出库数量）
   */
  remainAmount?: number;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 扫描数量
   */
  scanAmount?: number;
  /**
   * 出库ID
   */
  stockOutId?: string;
  /**
   * 三方明细行id
   */
  thirdDetailId?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 版本号
   */
  version?: number;
}

/**
 * 主单明细
 */
export interface StockOutRo {
  /**
   * 业务类型
   */
  billType?: number;
  /**
   * 业务类型描述
   */
  billTypeDesc?: string;
  /**
   * 出库单号
   */
  bizBillNo?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 收货方
   */
  customer?: string;
  /**
   * 配送地址
   */
  deliveryAddress?: string;
  /**
   * 配送方式（1：客户自提、2：商家配送、3：快递物流）
   */
  distributionMode?: DistributionModeStatus;
  /**
   * 配送方式中文描述
   */
  distributionModeDesc?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 物流公司id
   */
  logisticsCompanyCode?: string;
  /**
   * 物流公司名称
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 业务单号
   */
  origBillNo?: string;
  /**
   * 实际出库人
   */
  realOutPerson?: string;
  /**
   * 实际出库时间
   */
  realOutTime?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 出库单状态：0取消1.待出库2.已出库3.部分出库
   */
  state?: OutPutStatus;
  /**
   * 出库单状态描述：0取消1.待出库2.已出库3.部分出库
   */
  stateDesc?: string;
  /**
   * 计划出库商品总数
   */
  totalAmount?: number;
  /**
   * 计划出库商品种类总和
   */
  totalItem?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * 出库仓库id
   */
  warehouseId?: string;
  /**
   * 入库仓库id
   */
  warehouseIdIn?: string;
  /**
   * 入库仓库名称
   */
  warehouseInName?: string;
  /**
   * 出库仓库名称
   */
  warehouseName?: string;
}
