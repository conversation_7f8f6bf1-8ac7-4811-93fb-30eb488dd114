import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { CommonModelForm } from '@/types/CommonModelForm';
import { ProFormSelect } from '@ant-design/pro-components';
import ProForm, { ModalForm, ProFormFieldSet, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useAsyncEffect } from 'ahooks';
import { Switch } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useIntl } from 'umi';
import { queryPostDetail } from '../../../services';
import { PostEntity, PostStatus } from '../../types/post.entity';

export default (props: CommonModelForm<string, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
    } else {
      const data = await queryPostDetail({ id: props.recordId });
      const { area, rack, level, position, ...rest } = data;
      form.setFieldsValue({
        ...rest,
        code: [area, rack, level, position],
      });
    }
  }, [props.visible]);

  const rules = [{ required: !props.readOnly }];
  return (
    <ModalForm
      form={form}
      layout="vertical"
      title={props.title}
      open={props.visible}
      width="400px"
      initialValues={{
        state: PostStatus.ENABLE,
      }}
      modalProps={{
        centered: true,
        onCancel: props.onCancel,
      }}
      onFinish={props.onOk}
    >
      <ProFormText name="id" hidden={true} />
      <ProFormSelect
        name="warehouseId"
        rules={rules}
        disabled={props.readOnly || props.recordId != '0'}
        label={intl.formatMessage({ id: 'stocks.location.label.warehouseName' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        fieldProps={{ fieldNames: { label: 'warehouseName', value: 'id' } }}
        request={() => warehouseList({ state: YesNoStatus.YES }).then((s) => s?.warehouseSimpleRoList ?? [])}
      />
      <ProFormFieldSet rules={rules} name={'code'} label={intl.formatMessage({ id: 'stocks.location.label.locationInfo' })}>
        <ProFormText width={80} name="area" placeholder={intl.formatMessage({ id: 'stocks.location.label.area' })} />
        <ProFormText width={80} name="rack" placeholder={intl.formatMessage({ id: 'stocks.location.label.shelf' })} />
        <ProFormText width={80} name="level" placeholder={intl.formatMessage({ id: 'stocks.location.label.layer' })} />
        <ProFormText width={80} name="position" placeholder={intl.formatMessage({ id: 'stocks.location.label.position' })} />
      </ProFormFieldSet>
      <ProFormTextArea
        name="remark"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'common.label.remark' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.inputWithMaxLength' }, { maxLength: 100 })}
        fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
        rules={[{ max: 100 }]}
      />
      <ProForm.Item
        className='form-item-switch'
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        name="state"
        label={intl.formatMessage({ id: 'stocks.location.label.enable' })}>
        <Switch />
      </ProForm.Item>
    </ModalForm>
  );
};
