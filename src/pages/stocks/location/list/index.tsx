import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { useIntl } from 'umi';
import { createPost, modifyPost, modifyRemoveByIdPost, queryPostList } from '../services';
import FormModal from './components/modal';
import { PostListTableColumns } from './config/postListTableColumns';
import { PostStatus, type PostEntity } from './types/post.entity';

const LocationList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const dataCache = useRef<PostEntity[]>([]);
  const [locationModalProps, setLocationModalProps] = useState<CommonModelForm<string, PostEntity>>(
    {
      visible: false,
      recordId: '0',
      readOnly: false,
      title: '',
    },
  );

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * s删除事件
   * @param ids
   */
  const handleDeleteItem = async (id: string) => {
    await modifyRemoveByIdPost({ id });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (id: string) => {
    setLocationModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'stocks.location.title.editLocation' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setLocationModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: PostEntity) => {
    try {
      const { id, code = [], state, ...rest } = values;
      let result;
      const [area, rack, level, position] = code;
      if (id) {
        //编辑
        result = await modifyPost({ id, ...rest, area, rack, level, position, state: state ? PostStatus.ENABLE : PostStatus.DISABLE });
      } else {
        //新增
        result = await createPost({ ...rest, area, rack, level, position, state: state ? PostStatus.ENABLE : PostStatus.DISABLE });
      }
      if (result) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        requestPage={(query) =>
          queryPostList(query).then((result) => {
            dataCache.current = result.data;
            return result;
          })
        }
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
        })}
        rowSelection={{
          selectedRowKeys: checkedList,
          onChange: (selectedRowKeys) => {
            setCheckedList(selectedRowKeys as string[]);
          },
        }}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addLocation"
              onClick={() =>
                setLocationModalProps({
                  visible: true,
                  recordId: '0',
                  readOnly: false,
                  title: intl.formatMessage({ id: 'stocks.location.title.addLocation' }),
                })
              }
            >
              {intl.formatMessage({ id: 'stocks.location.title.addLocation' })}
            </AuthButton>
            <AuthButton
              key="import"
              type='primary'
              ghost
              authority="importLocation"
              onClick={() => {
                importData({
                  moduleId: 'WAREHOUSE_LOCATION_IMPORT',
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'stocks.location.text.batchImportLocation' }),
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.batchImport' })}
            </AuthButton>
          </Space>
        }
      />
      <FormModal {...locationModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
export default withKeepAlive(LocationList);
