export interface LocationListEntity {
  warehouseLocationRoList: WarehouseLocationRoList[];
}

export interface WarehouseLocationRoList {
  /**
      * 库区
      */
  area?: string;
  /**
   * 库位编码
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 层
   */
  level?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 货位
   */
  position?: string;
  /**
   * 货架
   */
  rack?: string;
  /**
   * 库存备注
   */
  remark?: string;
  /**
   * 状态:1：启用;0：停用
   */
  state?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
