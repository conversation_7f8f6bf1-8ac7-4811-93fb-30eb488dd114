// 状态: 1：启用; 0：停用
export enum PostStatus {
  ENABLE = 1,
  DISABLE = 0,
}
export interface PostEntity {
  /**
   * 库区
   */
  area?: string;
  /**
   * 库位编码
   */
  code?: string;
  /**
   * None
   */
  extRemark?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  lastName?: string;
  /**
   * 层
   */
  level?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 货位
   */
  position?: string;
  /**
   * 货架
   */
  rack?: string;
  /**
   * 仓库备注
   */
  remark?: string;
  /**
   * 状态:1：启用;0：停用
   */
  state?: PostStatus;
  /**
   * 仓库id
   */
  warehouseId?: string;
}
