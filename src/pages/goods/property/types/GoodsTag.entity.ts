/**
 * 商品属性-商品标签管理
 */
export interface GoodsTagEntity {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlag?: number;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlagName?: string;
  /**
   * 标签id
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
  /**
   * 0=启用1=禁用
   */
  tagStatus?: number;
  /**
   * 0=启用1=禁用
   */
  tagStatusName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}
