import { CommonStatusValueEnum } from '@/types/CommonStatus';
import { REG_LENGTH_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import { type ProColumns } from '@ant-design/pro-components';
import { type GoodsPropertyTableType } from '../services';

export default (intl: any) =>
  [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.property.inputFieldLabel.originRegionName' }),
      dataIndex: 'originRegionName',
      formItemProps: {
        rules: [REQUIRED_RULES, REG_LENGTH_RULE],
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.property.common.status' }),
      dataIndex: 'relationStatus',
      search: false,
      valueType: 'select',
      valueEnum: CommonStatusValueEnum,
      width: 150,
      fixed: 'right',
      align: 'center',
      fieldProps: {
        allowClear: false,
      },
    },
  ] as ProColumns<GoodsPropertyTableType>[];
