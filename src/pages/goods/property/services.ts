import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type GoodsBrandEntity } from './types/GoodsBrand.entity';
import { type GoodsCategoryEntity } from './types/GoodsCategory.entity';
import { type GoodsOriginRegionEntity } from './types/GoodsOriginRegion.entity';
import { type GoodsUnitEntity } from './types/GoodsUnit.entity';
import { type GoodsPriceLevelEntity } from './types/GoodsPriceLevel.entity';
import { GoodsTagEntity } from '@/pages/goods/property/types/GoodsTag.entity';

export type GoodsPropertyTableType = GoodsCategoryEntity &
  GoodsBrandEntity &
  GoodsUnitEntity &
  GoodsOriginRegionEntity &
  GoodsPriceLevelEntity &
  GoodsTagEntity;

/**
 * 商品属性-分页查询
 *
 * @param params
 * @param requestType
 * @returns
 */
export const queryGoodsPropertyPage = async (
  params: Partial<GoodsPropertyTableType> & PageRequestParamsType,
  requestType: string,
) => {
  const result = await request<PageResponseDataType<GoodsPropertyTableType>>(
    `/ipmsgoods/${requestType}/pageQuery`,
    { data: params },
  );
  if (result) {
    const fieldsToConvert: string[] = [
      'brandStatus',
      'unitStatus',
      'categoryStatus',
      'relationStatus',
      'levelStatus',
      'tagStatus',
      'showFlag',
    ];
    return {
      ...result,
      // @ts-ignore
      data: result.data?.map((item) => {
        const newItem = { ...item };
        for (const field of fieldsToConvert) {
          if (field in newItem && newItem[field] !== undefined && newItem[field] !== null) {
            newItem[field] = String(newItem[field]);
          }
        }
        return newItem;
      }),
    };
  }
  return { data: [] };
};
export const addGoodsProperty = async (
  params: Partial<GoodsPropertyTableType>,
  requestType: string,
) => {
  return request<boolean>(`/ipmsgoods/${requestType}/insert`, {
    data: params,
  });
};
// 新增品牌
export const addBrandProperty = async (params: { brandName: string }) => {
  return request<string[]>(`/ipmsgoods/brand/insert`, {
    data: params,
  });
};
export const editGoodsCategory = async (
  params: Partial<GoodsPropertyTableType>,
  requestType: string,
) => {
  return request<boolean>(`/ipmsgoods/${requestType}/update`, {
    data: params,
  });
};
