import { Button, Divider, Drawer, Form, Input, message, Popconfirm, Space, Switch } from 'antd';
import type { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import {
  deleteItemGroup,
  getItemGroupBase,
  itemGroupSave,
  queryGoodsPage,
  queryIcItemGroupList,
} from '@/pages/goods/list/services';
import FunProTable from '@/components/common/FunProTable';
import React, { useEffect, useRef, useState } from 'react';
import { GeneralGroupGoodsColumns } from '@/pages/goods/list/config/GeneralGroupGoodsColumns';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { useIntl } from '@@/exports';

export interface GeneralGroupCreateDrawerProps {
  visible: boolean;
  onClose?: () => void;
  id?: string;
  onSuccess?: () => void;
}

export default function GeneralGroupCreateDrawer(props: GeneralGroupCreateDrawerProps) {
  const leftItemListRef = useRef<GoodsEntity[]>([]);
  const [form] = Form.useForm();
  const { visible, onClose, id, onSuccess } = props;
  const [leftSelectedRowKeys, setLeftSelectedRowKeys] = useState<React.Key[]>([]);
  const [rightItemList, setRightItemList] = useState<GoodsEntity[]>([]);
  const [rightSelectedRowKeys, setRightSelectedRowKeys] = useState<React.Key[]>([]);
  const [mainItemId, setMainItemId] = useState<string>();

  const intl = useIntl();

  useEffect(() => {
    if (rightItemList.length > 0) {
      let _mainItemId;
      rightItemList.forEach((item) => {
        // @ts-ignore
        if (item.isMain) {
          _mainItemId = item.itemId;
        }
      });
      if (!_mainItemId) {
        _mainItemId = rightItemList[0].itemId;
      }
      setMainItemId(_mainItemId);
    }
  }, [rightItemList]);

  useEffect(() => {
    if (!id && visible && form) {
      form.setFieldsValue({ transferSuggest: true });
    }
  }, [form, id, visible]);

  useEffect(() => {
    return () => {
      setLeftSelectedRowKeys([]);
      setRightItemList([]);
      setRightSelectedRowKeys([]);
      setMainItemId(undefined);
      form.resetFields();
    };
  }, [visible]);

  useEffect(() => {
    if (id) {
      queryIcItemGroupList({
        groupId: id,
        isFetchBrand: true,
        isFetchBrandPart: true,
        isFetchOe: true,
        isFetchCategory: true,
      }).then((res) => {
        // @ts-ignore
        setRightItemList(res);
      });
      getItemGroupBase({ groupId: id }).then((res) => {
        form.setFieldsValue({
          name: res.name,
          transferSuggest: Boolean(res.transferSuggest),
          id: res.id,
        });
      });
    }
  }, [id]);

  const handleSubmit = (values: any) => {
    if (rightItemList.length === 0) {
      message.warning(
        intl.formatMessage({
          id: 'common.message.needSelectOne',
        }),
      );
      return;
    }
    itemGroupSave({
      id,
      name: values.name,
      transferSuggest: values.transferSuggest ? 1 : 0,
      icItemGroupDetailCmdList: rightItemList.map((item) => ({
        itemId: item.itemId,
        isMain: item.itemId === mainItemId ? 1 : 0,
      })),
    }).then((res) => {
      if (res) {
        message.success(
          intl.formatMessage({
            id: 'common.message.save.success',
          }),
        );
        onClose?.();
        onSuccess?.();
      }
    });
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      title={
        id
          ? intl.formatMessage({
              id: 'goods.general.group.edit',
            })
          : intl.formatMessage({
              id: 'goods.general.group.add',
            })
      }
      width={1800}
      classNames={{
        body: 'bg-gray-50',
      }}
      footer={
        <div className="flex gap-3 justify-end">
          <Button type="primary" ghost={true} onClick={onClose}>
            {intl.formatMessage({
              id: 'common.button.cancel',
            })}
          </Button>
          <Button type="primary" onClick={form.submit}>
            {intl.formatMessage({
              id: 'common.button.confirm',
            })}
          </Button>
        </div>
      }
    >
      <div className="flex bg-white rounded-lg h-full p-4">
        <div className="flex-1 shrink-0">
          <LeftTitle
            title={intl.formatMessage({
              id: 'goods.general.group.goods.list',
            })}
          />
          <FunProTable<GoodsEntity, any>
            rowKey="itemId"
            // @ts-ignore
            requestPage={(data) =>
              queryGoodsPage(data).then((res) => {
                // @ts-ignore
                leftItemListRef.current = res.data;
                return res;
              })
            }
            options={false}
            scroll={{ x: 400 }}
            rowSelection={{
              selectedRowKeys: leftSelectedRowKeys,
              onChange: (selectedKeys) => {
                setLeftSelectedRowKeys(selectedKeys);
              },
            }}
            search={{
              collapseRender: false,
              collapsed: false,
              layout: 'vertical',
              className: '!p-0 !pt-3',
            }}
            ghost={true}
            columns={GeneralGroupGoodsColumns({})}
            headerTitle={
              <Space>
                <AuthButton
                  type="primary"
                  ghost={true}
                  key="create"
                  authority=""
                  disabled={leftSelectedRowKeys.length === 0}
                  onClick={() => {
                    const goods: GoodsEntity[] = [];
                    const addedItemIds = rightItemList.map((item) => item.itemId);
                    leftSelectedRowKeys.forEach((item) => {
                      if (!addedItemIds.includes(item as string)) {
                        const good = leftItemListRef.current.find((m) => m.itemId === item);
                        if (good) {
                          goods.push(good);
                        }
                      }
                    });
                    setLeftSelectedRowKeys([]);
                    setRightItemList([...rightItemList, ...goods]);
                  }}
                >
                  {intl.formatMessage({
                    id: 'goods.general.group.add',
                  })}
                </AuthButton>
              </Space>
            }
          />
        </div>
        <Divider type={'vertical'} className="h-full mx-6" />
        <div className="flex-1 shrink-0">
          <LeftTitle
            title={intl.formatMessage({
              id: 'goods.general.group.goods.set',
            })}
          />
          <Form
            className="mt-6"
            colon={false}
            layout={'vertical'}
            form={form}
            onFinish={handleSubmit}
          >
            <Form.Item name="id" hidden>
              <Input />
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({
                id: 'goods.general.group.name',
              })}
              name="name"
              required={true}
              rules={[REQUIRED_RULES]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label={intl.formatMessage({
                id: 'goods.general.group.merge.transfer.suggestions',
              })}
              name="transferSuggest"
            >
              <Switch />
            </Form.Item>
            {Boolean(id) && (
              <div className="flex justify-end">
                <Popconfirm
                  title={intl.formatMessage({
                    id: 'common.confirm.delete',
                  })}
                  onConfirm={() => {
                    deleteItemGroup(id!).then((res) => {
                      if (res) {
                        message.success(
                          intl.formatMessage({
                            id: 'common.message.deleteSuccess',
                          }),
                        );
                        onClose?.();
                        onSuccess?.();
                      }
                    });
                  }}
                >
                  <AuthButton isHref authority="" className="-mt-12 relative">
                    {intl.formatMessage({
                      id: 'goods.general.group.remove',
                    })}
                  </AuthButton>
                </Popconfirm>
              </div>
            )}
          </Form>
          <FunProTable<GoodsEntity, any>
            rowKey="itemId"
            pagination={false}
            dataSource={rightItemList}
            options={false}
            scroll={{ x: 400 }}
            rowSelection={{
              selectedRowKeys: rightSelectedRowKeys,
              onChange: (selectedKeys) => {
                setRightSelectedRowKeys(selectedKeys);
              },
            }}
            ghost={true}
            search={false}
            columns={GeneralGroupGoodsColumns({ mainItemId })}
            headerTitle={
              <Space>
                <AuthButton
                  type="primary"
                  ghost={true}
                  key="create"
                  authority=""
                  disabled={rightSelectedRowKeys.length === 0}
                  onClick={() => {
                    setRightItemList(
                      rightItemList.filter((item) => !rightSelectedRowKeys.includes(item.itemId)),
                    );
                    setRightSelectedRowKeys([]);
                  }}
                >
                  {intl.formatMessage({
                    id: 'common.button.delete',
                  })}
                </AuthButton>
                <AuthButton
                  type="primary"
                  ghost={true}
                  key="create"
                  authority=""
                  disabled={rightSelectedRowKeys.length !== 1}
                  onClick={() => {
                    setMainItemId(rightSelectedRowKeys[0] as string);
                    setRightSelectedRowKeys([]);
                  }}
                >
                  {intl.formatMessage({
                    id: 'goods.general.group.set.primary.goods',
                  })}
                </AuthButton>
              </Space>
            }
          />
        </div>
      </div>
    </Drawer>
  );
}
