import LeftTitle from '@/components/LeftTitle';
import { type GoodsCreateDrawerFormType } from '@/pages/goods/list/types/GoodsCreateDrawerFormType';
import { addBrandProperty, queryGoodsPropertyPage } from '@/pages/goods/property/services';
import {
  REG_LENGTH_REMARK_RULE,
  REG_LENGTH_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_RULE,
  REQUIRED_RULES,
} from '@/utils/RuleUtils';
import FunProFormUploadButtonDragable from '@/components/FunProFormUploadButtonDragable';
import type { GoodsPriceLevelEntity } from '@/pages/goods/property/types/GoodsPriceLevel.entity';
import { MAX_AMOUNT } from '@/utils/Constants';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import {
  ProFormCheckbox,
  ProFormDigit,
  ProFormList,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import {
  DrawerForm,
  ProFormField,
  ProFormGroup,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, Flex, Form, message } from 'antd';
import type { UploadFile } from 'antd/es/upload';
import { isEmpty, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import { addGoods, goodsDetail, updateGoods } from '../../services';
import type { GoodsAddEntity } from '../../types/GoodsEntity.entity';
import { ChannelCodeEnum } from '@/pages/goods/list/types/channel.code';
import RichEditor from '@/components/RichEditor';
import BraftEditor, { EditorState } from 'braft-editor';
import PurchaseForm from '@/pages/goods/list/components/GoodsCreateDrawerForm/components/PurchaseForm';
import { GoodsTagEntity } from '@/pages/goods/property/types/GoodsTag.entity';
import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { getTagList } from '@/pages/customer/list/services';

/**
 * 创建商品
 */
export default (props: GoodsCreateDrawerFormType) => {
  const [form] = Form.useForm();
  const intl = useIntl();
  const [content, setContent] = useState<EditorState>('');

  const [priceDate, setPriceDate] = useState<GoodsPriceLevelEntity[]>();

  const [defaultFileList, setDefaultFileList] = useState<UploadFile[]>([]);
  const [submitImages, setSubmitImages] = useState<UploadFile[]>([]);

  useEffect(() => {
    return () => {
      form.resetFields();
      setContent(undefined);
    };
  }, [props.visible]);

  /**
   * 新增商品
   * @param values
   */
  const handleGoodsCreate = async (values: any) => {
    try {
      if (submitImages) {
        values.images = submitImages.map((t, sort) => ({ imageUrl: t.url, sort }));
      }
      let result = false;
      if (values?.itemId) {
        result = await updateGoods(values);
      } else {
        result = await addGoods(values);
      }
      if (result) {
        props.onCancel(isEmpty(values?.itemId));
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  useAsyncEffect(async () => {
    setDefaultFileList([]);
    if (props.recordId == 0) {
      form.resetFields();
    } else if (props?.recordId) {
      const result = await goodsDetail({ itemId: props.recordId });
      const {
        images,
        oeNos,
        brandPartNos,
        priceDetails,
        channelCodeList,
        barCodeList,
        itemTagList,
        ...restValues
      } = result;
      setContent(BraftEditor.createEditorState(result.ecommerceDetail));
      form.setFieldsValue(restValues);
      if (oeNos) {
        form.setFieldValue('oeNos', oeNos[0]);
      }
      if (channelCodeList) {
        form.setFieldValue(
          'channelCodeList',
          channelCodeList.map((item: number) => item.toString()),
        );
      }
      if (itemTagList) {
        form.setFieldValue(
          'itemTagIdList',
          itemTagList.map((item: any) => item.tagId),
        );
      }
      if (barCodeList) {
        form.setFieldValue(
          'barCodeList',
          barCodeList.map((item: string) => ({ barCode: item })),
        );
      }
      if (brandPartNos) {
        form.setFieldValue('brandPartNos', brandPartNos.join(','));
      }
      if (images) {
        const files: UploadFile[] = images.map((t: string) => ({
          uid: uniqueId('fileuid_'),
          name: uniqueId('filename_'),
          url: t,
          status: 'done',
        }));
        setDefaultFileList(files);
      }
      if (priceDetails) {
        //定义一个数组
        const priceDetailsArr: Record<number, any> = {};
        priceDetails.forEach((item) => {
          priceDetailsArr[item.levelId! as unknown as number] = item.levelPrice;
        });
        form.setFieldsValue({ priceDetails: priceDetailsArr });
      }
    }
  }, [props.visible, props?.recordId]);

  //品牌输入值
  const [searchBrandName, setSearchBrandName] = useState<string>();
  const [brandOptions, setBrandOptions] = useState<{ label: any; value: number }[]>([]);
  useAsyncEffect(async () => {
    if (!props.visible) {
      return;
    }
    const { data: brandData } = await queryGoodsPropertyPage(
      { brandName: searchBrandName, pageNo: 1, pageSize: 1000, brandStatus: '1' },
      'brand',
    );
    setBrandOptions(
      brandData.map((t) => ({
        label: t.brandName,
        value: t.brandId,
      })),
    );
  }, [searchBrandName, props.visible]);

  const addGoodsBrand = async () => {
    if (searchBrandName) {
      const result = await addBrandProperty({ brandName: searchBrandName });
      if (result) {
        const brandId = parseInt(result[0]);
        setBrandOptions((pre) => [{ value: brandId, label: searchBrandName }, ...pre]);
        form.setFieldValue('brandId', brandId);
        message.success(
          intl.formatMessage(
            { id: 'goods.createForm.addBrandSuccess' },
            { brandName: searchBrandName },
          ),
        );
      }
    } else {
      message.error(intl.formatMessage({ id: 'goods.createForm.inputBrandNameRequired' }));
    }
  };

  // 外部oe信息创建商品
  useEffect(() => {
    if (props.visible && props?.oEOuterParams) {
      form.setFieldsValue(props?.oEOuterParams);
    }
  }, [props.visible, props.oEOuterParams, form]);
  // 外部sku信息创建商品
  useEffect(() => {
    if (props.visible && props?.cloudOuterParams) {
      form.setFieldsValue(props.cloudOuterParams);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.visible, props.cloudOuterParams]);

  const queryLevelPrice = () => {
    queryGoodsPropertyPage(
      { levelName: searchBrandName, pageNo: 1, pageSize: 1000, levelStatus: 1 },
      'priceLevel',
    ).then((result) => {
      if (result) {
        setPriceDate(result.data as GoodsPriceLevelEntity[]);
      }
    });
  };

  useEffect(() => {
    if (!props.visible) {
      return;
    }
    queryLevelPrice();
  }, [searchBrandName, props.visible]);

  return (
    <DrawerForm<GoodsAddEntity>
      form={form}
      grid
      layout="vertical"
      labelWrap={true}
      title={props.title}
      open={props.visible}
      width={1080}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose: () => {
          props.onCancel(false);
        },
      }}
      onFinish={async (formData: any) => {
        const priceDetails = [];
        for (const key in formData.priceDetails) {
          priceDetails.push({ levelId: key, levelPrice: formData.priceDetails[key] });
        }
        if (formData.barCodeList?.length) {
          formData.barCodeList = formData.barCodeList?.map((item: any) => item.barCode);
        }
        if (formData.supplierList?.length) {
          formData.supplierList.forEach((item: any) => {
            if (item.id.startsWith('new_')) {
              delete item.id;
            }
          });
        }
        formData.priceDetails = priceDetails;
        return handleGoodsCreate(formData).then(() => {
          props.onRefresh?.();
        });
      }}
    >
      <ProFormText name="itemId" hidden />
      <ProFormText name="skuId" hidden />
      <ProFormGroup
        style={{
          backgroundColor: 'white',
          padding: 24,
          borderRadius: 8,
        }}
      >
        <ProFormGroup
          title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.baseInfoTitle' })} />}
        >
          <ProFormText
            rules={[REQUIRED_RULES]}
            name="itemName"
            required
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.itemNameLabel' })}
            colProps={{
              span: 8,
            }}
          />
          <ProFormText
            name="itemSn"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.itemSnLabel' })}
            rules={[
              REG_ONLY_ALPHA_AND_DIGIT_RULE,
              REG_LENGTH_RULE,
              { required: !isEmpty(props?.recordId) },
            ]}
            colProps={{
              span: 8,
            }}
          />
          <ProFormCheckbox.Group
            name="channelCodeList"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.channelLabel' })}
            valueEnum={ChannelCodeEnum}
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.oeLabel' })}
            name="oeNos"
            transform={(value: string | string[]) => {
              return { oeNos: !isEmpty(value) ? [value] : value };
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            rules={[REQUIRED_RULES]}
            label={intl.formatMessage({ id: 'goods.createForm.brandPartNoLabel' })}
            name="brandPartNos"
            transform={(value: string) => {
              return { brandPartNos: !isEmpty(value) ? [value] : value };
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormSelect
            rules={[REQUIRED_RULES]}
            name="brandId"
            showSearch
            label={intl.formatMessage({ id: 'goods.createForm.brandLabel' })}
            options={brandOptions}
            fieldProps={{
              filterOption: false,
              onSearch: (value) => setSearchBrandName(value),
              notFoundContent: (
                <Flex vertical align="center" justify="center">
                  <span className="mb-4">
                    {intl.formatMessage({ id: 'goods.createForm.searchEmpty' })}
                  </span>
                  <Button type="link" onClick={addGoodsBrand}>
                    {intl.formatMessage({ id: 'goods.createForm.addBrandAndSelectButton' })}
                  </Button>
                </Flex>
              ),
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormTreeSelect
            name="categoryId"
            fieldProps={{
              filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
              showSearch: true,
            }}
            label={intl.formatMessage({ id: 'goods.createForm.categoryLabel' })}
            request={async () => {
              const { data: categoryData } = await queryGoodsPropertyPage(
                { pageNo: 1, pageSize: 999, categoryStatus: 1, isReturnTree: true },
                'category',
              );
              return transformCategoryTree(categoryData);
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormSelect
            name="itemTagIdList"
            mode={'multiple'}
            showSearch
            label={intl.formatMessage({ id: 'goods.createForm.tags' })}
            request={(query) =>
              queryGoodsPropertyPage(
                { keyword: query.keyWords, pageSize: 9999 },
                'IcItemTagFacade',
              ).then((result) => {
                return result.data.map((item: any) => ({ label: item.tagName, value: item.tagId }));
              })
            }
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            name="realBrandName"
            label={intl.formatMessage({ id: 'goods.createForm.realBrandLabel' })}
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.realBrandPartNoLabel' })}
            name="realBrandPartNo"
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.ownCodeLabel' })}
            name="ownCode"
            colProps={{
              span: 8,
            }}
          />
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.orderCodeLabel' })}
            name="orderCode"
            colProps={{
              span: 8,
            }}
          />
          <ProFormSelect
            showSearch
            name="unitId"
            label={intl.formatMessage({ id: 'goods.createForm.unitLabel' })}
            fieldProps={{ filterOption: false }}
            request={async ({ keyWords: unitName }) => {
              const { data: unitData } = await queryGoodsPropertyPage(
                { unitName, pageNo: 1, pageSize: 1000, unitStatus: 1 },
                'unit',
              );
              return unitData.map((t: any) => ({
                label: t.unitName,
                value: t.unitId,
              }));
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormText
            name="spec"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.specLabel' })}
            colProps={{
              span: 8,
            }}
          />
          <ProFormSelect
            showSearch
            name="originRegionId"
            disabled={props.readOnly}
            fieldProps={{ fieldNames: { label: 'originRegionName', value: 'id' } }}
            label={intl.formatMessage({ id: 'goods.createForm.originRegionLabel' })}
            request={async ({ keyWords: originRegionName }) => {
              const { data: originRegionData } = await queryGoodsPropertyPage(
                { originRegionName, pageNo: 1, pageSize: 1000, relationStatus: 1 },
                'originRegionRelation',
              );
              return originRegionData;
            }}
            colProps={{
              span: 8,
            }}
          />
          <ProFormText
            name="skuWeight"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.skuWeightLabel' })}
            colProps={{
              span: 8,
            }}
          />

          <ProFormText
            label={intl.formatMessage({ id: 'goods.createForm.skuSizeLabel' })}
            colProps={{
              span: 8,
            }}
          >
            <div className="flex gap-1">
              <ProFormDigit
                name="skuLength"
                fieldProps={{
                  precision: 2,
                }}
                min={0}
                placeholder={intl.formatMessage({ id: 'goods.createForm.skuSizeLabel.length' })}
                colProps={{
                  span: 5,
                }}
              />
              <ProFormDigit
                name="skuWidth"
                fieldProps={{
                  precision: 2,
                }}
                min={0}
                placeholder={intl.formatMessage({ id: 'goods.createForm.skuSizeLabel.width' })}
                colProps={{
                  span: 5,
                }}
              />
              <ProFormDigit
                name="skuHeight"
                fieldProps={{
                  precision: 2,
                }}
                min={0}
                placeholder={intl.formatMessage({ id: 'goods.createForm.skuSizeLabel.height' })}
                colProps={{
                  span: 5,
                }}
              />
              <ProFormSelect
                initialValue="cm"
                name="dimensionUnit"
                options={[
                  { label: 'cm', value: 'cm' },
                  { label: 'm', value: 'm' },
                ]}
                colProps={{
                  span: 5,
                }}
              />
            </div>
          </ProFormText>

          <ProFormText
            name="memCode"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.memCodeLabel' })}
            colProps={{
              span: 8,
            }}
          />
          <ProFormList
            name="barCodeList"
            label={intl.formatMessage({ id: 'goods.createForm.barCodeLabel' })}
            colProps={{
              span: 8,
            }}
          >
            <ProFormText name={['barCode']} />
          </ProFormList>
          <ProFormTextArea
            name="adaptModel"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.adaptModelLabel' })}
            rules={[REG_LENGTH_REMARK_RULE]}
            colProps={{
              span: 8,
            }}
          />
          <ProFormTextArea
            name="remark"
            disabled={props.readOnly}
            rules={[REG_LENGTH_REMARK_RULE]}
            label={intl.formatMessage({ id: 'goods.createForm.remarkLabel' })}
            colProps={{
              span: 8,
            }}
          />
        </ProFormGroup>
        <FunProFormUploadButtonDragable
          name="images"
          dragEnable
          defaultFiles={defaultFileList}
          onFileListChange={(fileList) => setSubmitImages(fileList)}
          max={5}
          label={intl.formatMessage({ id: 'goods.createForm.imagesLabel' })}
        />
      </ProFormGroup>
      <ProFormGroup
        title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.priceInfoTitle' })} />}
        style={{
          backgroundColor: 'white',
          padding: 24,
          marginTop: 16,
          borderRadius: 8,
        }}
      >
        <ProFormMoney
          tooltip={intl.formatMessage({ id: 'goods.createForm.suggestPriceTooltip' })}
          name="suggestPrice"
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.suggestPriceLabel' })}
          fieldProps={{
            precision: 2,
            max: MAX_AMOUNT,
          }}
          colProps={{
            span: 8,
          }}
        />
        <ProFormMoney
          tooltip={intl.formatMessage({ id: 'goods.createForm.lowPriceTooltip' })}
          name="lowPrice"
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.lowPriceLabel' })}
          fieldProps={{
            precision: 2,
            max: MAX_AMOUNT,
            min: 0,
          }}
          colProps={{
            span: 8,
          }}
        />
        {priceDate?.map((item, index) => {
          return (
            <ProFormMoney
              key={item.levelId}
              name={['priceDetails', item.levelId]}
              disabled={props.readOnly}
              label={item.levelName}
              fieldProps={{
                precision: 2,
                max: MAX_AMOUNT,
                min: 0,
              }}
              colProps={{
                span: 8,
              }}
            />
          );
        })}
      </ProFormGroup>
      <ProFormGroup
        title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.purchaseTitle' })} />}
        style={{
          backgroundColor: 'white',
          padding: 24,
          marginTop: 16,
          borderRadius: 8,
        }}
      >
        <ProFormDigit
          min={0}
          fieldProps={{ precision: 0 }}
          name="minPurchaseStock"
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.minPurchaseStock' })}
          colProps={{
            span: 8,
          }}
        />
        <ProFormDigit
          min={0}
          fieldProps={{ precision: 0 }}
          name="maxPurchaseStock"
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.maxPurchaseStock' })}
          colProps={{
            span: 8,
          }}
        />
        <PurchaseForm />
      </ProFormGroup>
      <ProFormGroup
        title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.mallDetailTitle' })} />}
        style={{
          backgroundColor: 'white',
          padding: 24,
          marginTop: 16,
          borderRadius: 8,
        }}
      >
        <div className="richText">
          <ProFormField
            name="ecommerceDetail"
            readonly={props.readOnly}
            transform={(value) => {
              return { ecommerceDetail: value ? value.toHTML() : '' };
            }}
          >
            <RichEditor
              form={form}
              name="ecommerceDetail"
              readOnly={props.readOnly}
              content={content}
              setContent={setContent}
            />
          </ProFormField>
        </div>
      </ProFormGroup>
    </DrawerForm>
  );
};
