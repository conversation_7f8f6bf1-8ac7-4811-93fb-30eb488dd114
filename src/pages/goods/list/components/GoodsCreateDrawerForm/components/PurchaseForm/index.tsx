import { useIntl } from '@@/exports';
import { useRef } from 'react';
import { EditableFormInstance, EditableProTable } from '@ant-design/pro-components';
import { ConfigProvider, Flex, Radio } from 'antd';
import { find, isEmpty, uniqueId } from 'lodash';
import { queryPostList } from '@/pages/purchase/supplier/services';
import { SupplierList } from '@/pages/goods/list/types/GoodsEntity.entity';
import { MAX_AMOUNT } from '@/utils/Constants';

export default function PurchaseForm() {
  const intl = useIntl();
  const editorFormRef = useRef<EditableFormInstance<SupplierList>>();
  return (
    <ConfigProvider
      theme={{
        components: {
          InputNumber: {
            controlWidth: 80,
          },
        },
      }}
    >
      <EditableProTable<SupplierList>
        search={false}
        name="supplierList"
        rowKey="id"
        editableFormRef={editorFormRef}
        scroll={{ x: 990 }}
        recordCreatorProps={{
          record: (index) => ({ id: uniqueId('new_'), isDefault: index == 0 ? 1 : 0 }),
          creatorButtonText: intl.formatMessage({
            id: 'goods.createForm.supplier.add',
          }),
        }}
        pagination={false}
        editable={{
          type: 'multiple',
          actionRender: (_, __, defaultDom) => [defaultDom.delete],
        }}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.column.index' }),
            valueType: 'index',
            editable: false,
            width: 40,
            fixed: 'left',
          },
          {
            title: intl.formatMessage({
              id: 'goods.createForm.supplier.default',
            }),
            align: 'center',
            width: 100,
            dataIndex: 'isDefault',
            fixed: 'left',
            render: (_, record) => {
              const rows = editorFormRef.current?.getRowsData?.();
              return (
                <Radio
                  checked={record.isDefault === 1}
                  onChange={() => {
                    rows?.forEach((t) => {
                      const { id } = t;
                      editorFormRef.current?.setRowData?.(id, {
                        isDefault: id == record?.id ? 1 : 0,
                      });
                    });
                  }}
                />
              );
            },
            renderFormItem: (_, { record }) => {
              const rows = editorFormRef.current?.getRowsData?.();
              if (rows) {
                const checkRow = find(rows, (t) => t.isDefault === 1);
                if (isEmpty(checkRow)) {
                  editorFormRef.current?.setRowData?.(0, {
                    isDefault: 1,
                  });
                }
              }
              return (
                <Radio
                  checked={record?.isDefault === 1}
                  onChange={() => {
                    rows?.forEach((t) => {
                      const { id } = t;
                      editorFormRef.current?.setRowData?.(id, {
                        isDefault: id == record?.id ? 1 : 0,
                      });
                    });
                  }}
                />
              );
            },
          },
          {
            title: (
              <Flex align="center">
                <span className="text-[#FF7621]">*</span>
                <span>
                  {intl.formatMessage({
                    id: 'goods.createForm.supplier',
                  })}
                </span>
              </Flex>
            ),
            dataIndex: 'supplierId',
            width: 100,
            valueType: 'select',
            fieldProps: {
              showSearch: true,
              filterOption: false,
            },
            request: async ({ keyWords }) => {
              const { data } = await queryPostList({
                supplierName: keyWords,
                pageNo: 1,
                pageSize: 1000,
              });
              return data?.map((t) => ({
                label: t.supplierInfo?.supplierName,
                value: t.supplierInfo?.id,
              }));
            },
          },
          {
            title: intl.formatMessage({
              id: 'goods.createForm.purchasePrice',
            }),
            dataIndex: 'purchasePrice',
            width: 150,
            valueType: 'money',
            fieldProps: {
              precision: 2,
              min: 0,
              max: MAX_AMOUNT,
              className: 'w-full',
            },
          },
          {
            title: intl.formatMessage({
              id: 'goods.createForm.moq',
            }),
            dataIndex: 'moq',
            width: 100,
            valueType: 'digit',
            fieldProps: {
              min: 0,
              precision: 0,
            },
          },
          {
            title: intl.formatMessage({
              id: 'goods.createForm.mpq',
            }),
            dataIndex: 'mpq',
            width: 100,
            valueType: 'digit',
            fieldProps: {
              min: 0,
              precision: 0,
            },
          },
          {
            title: intl.formatMessage({ id: 'common.column.operation' }),
            valueType: 'option',
            align: 'center',
            width: 100,
            fixed: 'right',
            render: (text, record, _, action) => (
              <a
                key="address_editable"
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                {intl.formatMessage({ id: 'common.button.edit' })}
              </a>
            ),
          },
        ]}
      />
    </ConfigProvider>
  );
}
