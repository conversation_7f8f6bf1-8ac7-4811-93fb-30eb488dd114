import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Space, Tag } from 'antd';
import { queryGoodsPropertyPage } from '../../property/services';
import { type GoodsEntity } from '../types/GoodsEntity.entity';
import { transformCategoryTree } from '@/utils/transformCategoryTree';

export interface GeneralGroupGoodsColumnsProps {
  mainItemId?: string;
}

export const GeneralGroupGoodsColumns = (props: GeneralGroupGoodsColumnsProps) => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemGroupName' }),
      dataIndex: 'itemGroupRoList',
      width: 120,
      formItemProps: {
        name: 'itemGroupName',
      },
      render: (_, record) => {
        return (
          <div>
            {record?.itemGroupRoList?.map((t) => (
              <div className="block">{t.name}</div>
            ))}
          </div>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
      dataIndex: 'itemSn',
      search: false,
      hideInSearch: true,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
      dataIndex: 'images',
      search: false,
      width: 140,
      render: (_, record: StoreGoodsEntity) => {
        return (
          <div>
            {props?.mainItemId === record.itemId && <Tag color={'orange'}>主商品</Tag>}
            {record.itemName}
          </div>
        );
      },
    },
    {
      dataIndex: 'queryKeyWord',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'goods.list.table.queryKeywordPlaceholder' }),
      },
      formItemProps: {
        tooltip: intl.formatMessage({ id: 'goods.list.table.queryKeywordTooltip' }),
        label: intl.formatMessage({ id: 'goods.list.table.queryKeyword' }),
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.oeNos' }),
      dataIndex: 'oeNos',
      search: false,
      width: 140,
      ellipsis: true,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.oeNos),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      search: false,
      width: 100,
      ellipsis: true,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.brandPartNos),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.brandName' }),
      dataIndex: 'brandName',
      width: 100,
      ellipsis: true,
      valueType: 'select',
      formItemProps: {
        name: 'brandId',
      },
      fieldProps: {
        showSearch: true,
        filterOption: false,
        optionRender: (option: any) => <Space>{option.data.label}</Space>,
      },
      request: async ({ keyWords: brandName }) => {
        const { data } = await queryGoodsPropertyPage(
          { brandName, pageNo: 1, pageSize: 1000 },
          'brand',
        );
        return data.map((t: any) => ({
          label: t.brandName,
          dataType: t.dataType,
          value: t.brandId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.category' }),
      dataIndex: 'categoryName',
      width: 100,
      ellipsis: true,
      valueType: 'treeSelect',
      formItemProps: {
        name: 'categoryId',
      },
      fieldProps: {
        treeCheckable: true,
        maxTagCount: 3,
        filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
      },
      request: () => {
        return queryGoodsPropertyPage(
          { pageSize: 999, pageNo: 1, isReturnTree: true },
          'category',
        ).then((result) => transformCategoryTree(result.data));
      },
    },
  ] as ProColumns<GoodsEntity>[];
};
