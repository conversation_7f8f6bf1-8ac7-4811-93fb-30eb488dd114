export interface GetItemGroupBaseRequest {
  /**
   * none
   */
  firstName?: string;
  /**
   * 分组id
   */
  groupId?: string;
  /**
   * 是否获取品牌
   */
  isFetchBrand?: boolean;
  /**
   * 是否获取供应商编码
   */
  isFetchBrandPart?: boolean;
  /**
   * 是否获取类目
   */
  isFetchCategory?: boolean;
  /**
   * 是否获取oe
   */
  isFetchOe?: boolean;
  /**
   * 是否获取单位名称
   */
  isFetchUnit?: boolean;
  /**
   * none
   */
  lastName?: string;
  /**
   * none
   */
  memberId?: string;
  /**
   * none
   */
  operatorName?: string;
  /**
   * none
   */
  operatorNo?: string;
}
