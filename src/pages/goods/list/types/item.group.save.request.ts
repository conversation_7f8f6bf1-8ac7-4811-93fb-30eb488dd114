export interface ItemGroupSaveRequest {
  /**
   * param
   */
  extRemark?: string;
  /**
   * param
   */
  firstName?: string;
  /**
   * 明细
   */
  icItemGroupDetailCmdList?: IcItemGroupDetailCmdList[];
  /**
   * 主键
   */
  id?: string;
  /**
   * 是否删除，0未删除
   */
  isDelete?: number;
  /**
   * param
   */
  lastName?: string;
  /**
   * param
   */
  memberId?: string;
  /**
   * param
   */
  memberName?: string;
  /**
   * 通用组名称
   */
  name?: string;
  /**
   * param
   */
  operatorName?: string;
  /**
   * param
   */
  operatorNo?: string;
  /**
   * 0禁用1启用
   */
  state?: number;
  /**
   * 调货建议按主商品合并，0:否，1是
   */
  transferSuggest?: number;
}

export interface IcItemGroupDetailCmdList {
  /**
   * param
   */
  extRemark?: string;
  /**
   * param
   */
  firstName?: string;
  /**
   * 通用组id
   */
  groupId?: number;
  /**
   * 是否为主商品，0:否，1:是
   */
  isMain?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * param
   */
  lastName?: string;
  /**
   * param
   */
  memberId?: string;
  /**
   * param
   */
  memberName?: string;
  /**
   * param
   */
  operatorName?: string;
  /**
   * param
   */
  operatorNo?: string;
}
