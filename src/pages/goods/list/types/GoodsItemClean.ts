/**
 * 商品数据清洗请求参数
 */
export interface GoodsItemCleanRequest {
  /**
   * 使用车系信息
   */
  adaptSeries?: string;
  /**
   * 品牌ID集合
   */
  brandIdList?: string[];
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 供应商编码，模糊匹配，且返回结果中brandPartNos只返回匹配的供应商编码，例如添加商品时查询使用
   */
  brandPartNo?: string;
  /**
   * 类目ID集合，只传三级类目ID
   */
  categoryIdList?: string[];
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * etc号集合，精准匹配
   */
  etcNoList?: string[];
  /**
   * 排除品牌ID集合
   */
  excludedBrandIdList?: string[];
  /**
   * 是否精准查询，针对使用关键字查询
   */
  isAccurate?: boolean;
  /**
   * 是否过滤零售商品牌黑名单
   */
  isBlackFilter?: boolean;
  /**
   * 是否查询标准适用车型，查询零零汽
   */
  isFetchAdaptCarModel?: boolean;
  /**
   * 是否获取品牌聚合数据
   */
  isFetchBrandAggs?: boolean;
  /**
   * 是否获取类目聚合数据
   */
  isFetchCategoryAggs?: boolean;
  /**
   * 是否查询关联零售商商品
   */
  isFetchMemberItem?: boolean;
  /**
   * 是否查询零零汽使用车型信息，平台采购场景使用
   */
  isQueryCarModel?: boolean;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 车型ID，vin解析场景
   */
  modelId?: string;
  /**
   * OE码，模糊匹配，且返回结果中oeNos只返回匹配的oe码，例如添加商品时查询使用
   */
  oeNo?: string;
  /**
   * OE码集合，精准匹配
   */
  oeNoList?: string[];
  /**
   * 查询关键字，通过商品名称、OE、供应商编码进行匹配
   */
  queryKeyWord?: string;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * SKU商品备注
   */
  skuRemark?: string;
  /**
   * 状态:0-下架1-正常
   */
  skuStatus?: number;
  /**
   * sku类型：0-标准1-非标
   */
  skuType?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

/**
 * 商品清洗结果数据
 */
export interface GoodsItemCleanResponse {
  /**
   * 清洗结果备注，例如系统异常等
   */
  cleanRemark?: string;
  /**
   * 清洗结果数据
   */
  cleanResultList?: CleanResultListItem[];
  /**
   * None
   */
  memberId?: string;
  /**
   * 待清洗原始数据
   */
  waitCleanData?: WaitCleanData;
}

export interface CleanResultListItem {
  /**
   * 标准试用车型
   */
  adaptCarModel?: string;
  /**
   * etc适配车型id列表
   */
  adaptModelIds?: string[];
  /**
   * 适配车系
   */
  adaptSeries?: string;
  /**
   * 适配车系
   */
  adaptSeriesCode?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 关联供应商编码列表
   */
  brandPartNos?: string[];
  /**
   * 类目ID
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 是否品牌黑名单
   */
  isBrandBlack?: boolean;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 关联零售商商品ID
   */
  memberItemId?: string;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装量
   */
  minPackNum?: number;
  /**
   * 关联OE码列表
   */
  oeNos?: string[];
  /**
   * SKU商品ID
   */
  skuId?: string;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 状态:0-删除1-正常2-下架
   */
  skuStatus?: number;
  /**
   * sku类型：0-标准1-非标
   */
  skuType?: number;
  /**
   * 三方数据编码
   */
  sourceCode?: string;
  /**
   * 三方商品号
   */
  thirdNo?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

/**
 * 待清洗原始数据
 */
export interface WaitCleanData {
  /**
   * 使用车系信息
   */
  adaptSeries?: string;
  /**
   * 品牌ID集合
   */
  brandIdList?: string[];
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 供应商编码，模糊匹配，且返回结果中brandPartNos只返回匹配的供应商编码，例如添加商品时查询使用
   */
  brandPartNo?: string;
  /**
   * 类目ID集合，只传三级类目ID
   */
  categoryIdList?: string[];
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * etc号集合，精准匹配
   */
  etcNoList?: string[];
  /**
   * 排除品牌ID集合
   */
  excludedBrandIdList?: string[];
  /**
   * 是否精准查询，针对使用关键字查询
   */
  isAccurate?: boolean;
  /**
   * 是否过滤零售商品牌黑名单
   */
  isBlackFilter?: boolean;
  /**
   * 是否查询标准适用车型，查询零零汽
   */
  isFetchAdaptCarModel?: boolean;
  /**
   * 是否获取品牌聚合数据
   */
  isFetchBrandAggs?: boolean;
  /**
   * 是否获取类目聚合数据
   */
  isFetchCategoryAggs?: boolean;
  /**
   * 是否查询关联零售商商品
   */
  isFetchMemberItem?: boolean;
  /**
   * 是否查询零零汽使用车型信息，平台采购场景使用
   */
  isQueryCarModel?: boolean;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 车型ID，vin解析场景
   */
  modelId?: string;
  /**
   * OE码，模糊匹配，且返回结果中oeNos只返回匹配的oe码，例如添加商品时查询使用
   */
  oeNo?: string;
  /**
   * OE码集合，精准匹配
   */
  oeNoList?: string[];
  /**
   * 查询关键字，通过商品名称、OE、供应商编码进行匹配
   */
  queryKeyWord?: string;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * SKU商品备注
   */
  skuRemark?: string;
  /**
   * 状态:0-下架1-正常
   */
  skuStatus?: number;
  /**
   * sku类型：0-标准1-非标
   */
  skuType?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}
