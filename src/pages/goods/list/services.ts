import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type React from 'react';
import type { GoodsAddEntity, GoodsInfoByOEResult } from './types/GoodsEntity.entity';
import { type GoodsEntity } from './types/GoodsEntity.entity';
import { IcItemGroupEntity, IcItemGroupListRequest } from './types/IcItemGroupType';
import type { ItemQueryPageRequest } from './types/item.query.page.request';
import { ItemGroupSaveRequest } from './types/item.group.save.request';
import { GetItemGroupBaseRequest } from './types/get.item.group.base.request';
import { ItemGroupBase } from './types/item.group.base';

/**
 * 商品分页查询
 *
 * @param params
 * @returns
 */
export const queryGoodsPage = async (params: ItemQueryPageRequest) => {
  const { brandId, categoryId, ...rest } = params;
  rest.isFetchAllInventory = true;
  if (brandId) {
    rest.brandIdList = [brandId];
  }
  if (categoryId) {
    rest.categoryIdList = categoryId;
  }
  return request<PageResponseDataType<GoodsEntity>>(`/ipmsconsole/goods/member/query`, {
    data: rest,
  });
};
/**
 * 商品sku分页查询
 *
 * @param params
 * @returns
 */
export const querySkuGoodsPage = async (params: ItemQueryPageRequest) => {
  return request<PageResponseDataType<GoodsEntity>>(`/ipmsgoods/item/sku/pageQuery`, {
    data: params,
  });
};
/**
 * 商品新增
 *
 * @param params
 * @returns
 */
export const addGoods = async (params: GoodsAddEntity) => {
  return request<boolean>(`/ipmsgoods/item/add`, {
    data: params,
  });
};
/**
 * 编辑商品主数据信息
 *
 * @param params
 * @returns
 */
export const updateGoods = async (params: Partial<GoodsEntity> & PageRequestParamsType) => {
  return request<boolean>(`/ipmsgoods/item/update`, {
    data: params,
  });
};
/**
 * 批量新增商品
 *
 * @param params
 * @returns
 */
export const addGoodsBatch = async (params: Partial<GoodsEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<GoodsEntity>>(`/ipmsgoods/item/add/batch`, {
    data: params,
  });
};
/**
 * 批量启用或者禁用商品
 *
 * @param params
 * @returns
 */
export const updateGoodsStatusBatch = async (params: {
  itemIdList: React.Key[];
  itemStatus: number;
}) => {
  return request<boolean>(`/ipmsgoods/item/updateStatus/batch`, {
    data: params,
  });
};
/**
 * 查询商品详情信息
 *
 * @param params
 * @returns
 */
export const goodsDetail = async (params: { itemId: React.Key }) => {
  return request<GoodsEntity>(`/ipmsgoods/item/detail`, {
    data: params,
  });
};
/**
 * 行业OE查询商品信息
 *
 * @param params
 * @returns
 */
export const queryGoodsListByOeNo = async (params: { oeNo: string }) => {
  return request<PageResponseDataType<GoodsInfoByOEResult>>(
    `ipmsgoods/goodsSearch/queryIndustryOeNo`,
    {
      data: params,
    },
  );
};

/**
 * 通用组基础信息查询
 */
export const getItemGroupBase = async (params: GetItemGroupBaseRequest): Promise<ItemGroupBase> => {
  return request(`/ipmsgoods/icItemGroupFacade/getItemGroupBase`, {
    data: params,
  });
};

/**
 * 通用分组列表
 */
export const queryIcItemGroupList = async (
  params: IcItemGroupListRequest,
): Promise<IcItemGroupEntity[]> => {
  return request(`/ipmsgoods/icItemGroupFacade/queryList`, {
    data: params,
  });
};

/**
 * 保存通用组
 * @param params
 */
export const itemGroupSave = async (params: ItemGroupSaveRequest): Promise<boolean> => {
  return request(`/ipmsgoods/icItemGroupFacade/save`, {
    data: params,
  });
};

/**
 * 删除通用组
 * @param id
 */
export const deleteItemGroup = async (id: string): Promise<boolean> => {
  return request(`/ipmsgoods/icItemGroupFacade/deleteItemGroup`, {
    data: { id },
  });
};
