import { PositionType } from './CustomerContactEntity';

// 0=启用1=禁用2=待审核
export enum CustomerStatus {
  /**
   * 启用
   */
  ENABLE = 0,
  /**
   * 禁用
   */
  DISABLE = 1,
  /**
   * 待审核
   */
  PENDING = 2,
}

export enum DeliveryAmountType {
  /**
   * 免运费
   */
  Free = 1,
  /**
   * 固定运费
   */
  Fixed = 2,
  /**
   * 议价运费
   */
  Bargaining = 3,
}

export enum SettleType {
  /**
   * 现款
   */
  COD = 'COD',
  /**
   * 按周
   */
  WEEKLY = 'Weekly',
  /**
   * 按月
   */
  MONTHLY = 'Monthly',
}

export interface Contact {
  /**
   * 邮箱地址
   */
  email?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 编辑传入id
   */
  id?: string;
  /**
   * 1:默认联系人0:非默认联系人
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 职务
   */
  positions?: PositionType[];
  /**
   * 客户备注
   */
  remark?: string;
}

export interface Tag {
  /** 标签ID */
  tagId: string;
  /** 标签名称 */
  tagName?: string;
  /** 主键 */
  id?: string;
}

export interface Address {
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 市编码
   */
  cityCode?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 编辑传入id
   */
  id?: string;
  /**
   * 1:默认地址0:非默地址
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 邮编
   */
  postCode?: string;
  /**
   * 区县编码
   */
  prefectureCode?: string;
  /**
   * 省编码
   */
  provinceCode?: string;

  fullAddress?: string;
}

export interface Image {
  /** 图片地址 */
  url: string;
  /** 编辑传入id */
  id?: string;
  uid?: string;
}

export interface Settle {
  /**
   * 是否挂账
   */
  credit?: boolean;
  /**
   * 账期
   */
  creditTermsType?: string;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: number;
  /**
   * 是否多币种1=多币种0=单币种
   */
  isMultiCurrency?: number;
  /**
   * 客户期望额度
   */
  requestCreditLimit?: number;
  /**
   * 客户期望结算方式
   */
  requestSettleType?: SettleType;
  /**
   * 额度单位元（最多两位小数）
   */
  totalAmount?: number;
  usedAmount?: string;
  availableAmount?: string;
  /**
   * 客户应收
   */
  receivableAmountCurrency?: string;
  /**
   * 客户预收
   */
  advanceAmountCurrency?: string;
}

export interface Billing {
  /**
   * 开户行账号
   */
  accountNo?: string;
  /**
   * 地址
   */
  address?: string;
  /**
   * 开户行名称
   */
  bankName?: string;
  /**
   * 开票单位
   */
  billingUnit?: string;
  /**
   * 编辑传入id
   */
  id?: string;
  /**
   * 电话
   */
  phone?: string;
  /**
   * 纳税识别号
   */
  taxNo?: string;
}
export interface Base {
  /**
   * abn
   */
  abn?: string;
  /**
   * 客户名称
   */
  cstName?: string;
  /**
   * 客户编码
   */
  cstSn?: string;
  /**
   * 0=启用1=禁用2=待审核
   */
  cstStatus?: CustomerStatus;
  /**
   * 财务邮箱
   */
  financeEmail?: string;
  /**
   * 客户ID
   */
  id?: string;
  /**
   * 客户简称
   */
  nickName?: string;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 发送财务邮件
   */
  sendFinanceEmailFlag?: boolean;
  /**
   * 归属门店ID
   */
  storeId?: string;
  storeName?: string;
  /**
   * 通用邮箱
   */
  universalEmail?: string;
  /**
   * 运费
   */
  deliveryAmount?: number;
  /**
   * 运费类型
   */
  deliveryAmountType?: number;
}

export interface CustomerSaveEntity {
  /**
   * 客户地址信息
   */
  addresses?: Address[];

  hasMallPermission?: boolean;
  /**
   * 客户基础信息
   */
  base?: Base;
  /**
   * 客户开票信息
   */
  billings?: Billing[];
  /**
   * 客户联系人信息
   */
  contacts?: Contact[];
  /**
   * None
   */
  extRemark?: string;
  firstName?: string;
  /**
   * 客户图片
   */
  images?: Image[];
  lastName?: string;
  /**
   * 零售商ID网关映射
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 挂账信息
   */
  settle?: Settle;
  /**
   * 客户标签信息
   */
  tags?: Tag[];
}
