export interface UpdateMallPermissionRequest {
    /**
     * 联系人id
     */
    contactId?: string;
    /**
     * 客户id
     */
    cstId?: string;
    /**
     * extRemark
     */
    extRemark?: string;
    firstName?: string;
    /**
     * 商城权限
     */
    hasMallPermission?: number;
    lastName?: string;
    /**
     * memberId
     */
    memberId?: string;
    /**
     * memberName
     */
    memberName?: string;
    /**
     * operatorName
     */
    operatorName?: string;
    /**
     * operatorNo
     */
    operatorNo?: string;
}

export enum MallDeviceBindingOperateType {
    /**
     * 新增
     */
    ADD = 1,
    /**
     * 删除
     */
    DELETE = 2,
}

export interface UpdateMallDeviceBindingRequest {
    /**
     * 联系人id
     */
    contactId?: string;
    /**
     * 客户id
     */
    cstId?: string;
    /**
     * 密钥id
     */
    id?: string;
    /**
     * 操作类型1新增2删除
     */
    operateType?: MallDeviceBindingOperateType;
}

export interface CustomerMallPermissionContact {
    /**
     * 客户ID
     */
    cstId?: string;
    /**
     * 设备绑定信息
     */
    deviceBindingList?: DeviceBindingList[];
    /**
     * 邮箱地址
     */
    email?: string;
    /**
     * 联系人姓名
     */
    firstName?: string;
    /**
     * 是否开通商城权限
     */
    hasMallPermission?: number;
    /**
     * 主键
     */
    id?: string;
    /**
     * 1:默认联系人0:非默认联系人
     */
    isDefault?: number;
    /**
     * 联系人姓名
     */
    lastName?: string;
    /**
     * 联系人电话
     */
    phone?: string;
    /**
     * 职务
     */
    position?: string;
    /**
     * 职务
     */
    positionList?: PositionList[];
    /**
     * QQ号码
     */
    qq?: string;
    /**
     * 联系人备注
     */
    remark?: string;
    /**
     * 微信号
     */
    wechat?: string;
}

export interface DeviceBindingList {
    /**
     * 绑定时间
     */
    bindingTime?: string;
    /**
     * 联系人id
     */
    contactId?: string;
    /**
     * 客户id
     */
    cstId?: string;
    /**
     * 设备id
     */
    deviceKey?: string;
    /**
     * id
     */
    id?: string;
    /**
     * 秘钥
     */
    secretKey?: string;
}

export interface PositionList {
    /**
     * positionCode
     */
    positionCode?: string;
    /**
     * 职务
     */
    positionName?: string;
}
