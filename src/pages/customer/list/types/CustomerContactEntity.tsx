import { FormattedMessage } from "@umijs/max";
import { Contact } from "./CustomerSaveEntity";


export enum PositionType {
  BOSS = '1',
  PURCHASE = '2',
  FINANCE = '3',
  RECEPTION = '4',
  REPAIRMAN = '5',
}


export type CustomerContactEntity = Contact;


export const PositionEnum = {
  [PositionType.BOSS]: {
    text: <FormattedMessage id="customer.customerList.position.boss" />,
  },
  [PositionType.PURCHASE]: {
    text: <FormattedMessage id="customer.customerList.position.purchase" />,
  },
  [PositionType.FINANCE]: {
    text: <FormattedMessage id="customer.customerList.position.finance" />,
  },
  [PositionType.RECEPTION]: {
    text: <FormattedMessage id="customer.customerList.position.reception" />,
  },
  [PositionType.REPAIRMAN]: {
    text: <FormattedMessage id="customer.customerList.position.repairman" />,
  },
};

