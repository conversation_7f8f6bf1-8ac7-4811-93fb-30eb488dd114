import { ProDescriptions } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { <PERSON><PERSON>, Divider, Drawer, Form, Space, Switch } from "antd";
import { useEffect, useState } from "react";
import { queryCstContactList, updateMallDeviceBinding, updateMallPermission } from "../../services";
import { CustomerMallPermissionContact, MallDeviceBindingOperateType } from "../../types/CustomerMallPermission";

export interface OpenMallAccountDrawerProps {
    visible: boolean;
    onClose: () => void;
    cstData: any;
}

const OpenMallAccountDrawer = (props: OpenMallAccountDrawerProps) => {
    const intl = useIntl();
    const [contactList, setContactList] = useState<CustomerMallPermissionContact[]>([]);

    const reload = () => {
        queryCstContactList({ cstId: props.cstData.cstId }).then(res => {
            setContactList(res);
        });
    }

    useEffect(() => {
        if (props.visible) {
            reload()
        }
    }, [props.visible, props.cstData.cstId]);


    const onUpdateMallDeviceBinding = (params) => {
        updateMallDeviceBinding(params).then(result => {
            if (result) {
                reload();
            }
        })
    }

    return (
        <Drawer
            title={intl.formatMessage({ id: 'customer.customerList.openMallAccount.title' })}
            open={props.visible}
            onClose={props.onClose}
            bodyStyle={{ backgroundColor: '#F2F2F2', padding: 0 }}
            width={800}
        >
            <div className="p-4 m-4 bg-white rounded-lg">

                <ProDescriptions
                    column={3}
                    dataSource={props.cstData}
                    columns={[
                        {
                            title: intl.formatMessage({ id: 'customer.customerList.table.column.customerSn' }),
                            dataIndex: 'cstSn',
                        },
                        {
                            title: intl.formatMessage({ id: 'customer.customerList.table.column.customerName' }),
                            dataIndex: 'cstName',
                        },

                    ]}
                />

                {
                    contactList.map((t, index) => (
                        <div key={t.cstId} className={`${index === 0 ? 'mt-0' : 'mt-4'} border-solid border border-black/[0.08] rounded-lg`}>
                            <div className='flex justify-between items-center px-4 py-2'>
                                <div>
                                    <span className='font-semibold text-[16px] text-[#000000D9]'>{t.firstName} {t.lastName}</span>
                                    <span className='text-gray-500 text-sm'>
                                        <span>{t.positionList?.map(tag => tag.positionName).join(',')}</span>
                                        <Divider type="vertical" />
                                        <span>{t.email}</span>
                                    </span>
                                </div>

                                <Form.Item className="form-item-switch"
                                    style={{ marginBottom: 0 }}
                                    label={intl.formatMessage({ id: 'customer.customerList.openMallAccount.title' })}>
                                    <Switch
                                        onChange={(value) => {
                                            updateMallPermission({ contactId: t.id, cstId: t.cstId, hasMallPermission: value ? 1 : 0 }).then(result => {
                                                if (result) {
                                                    reload();
                                                }
                                            })
                                        }}
                                        checked={t.hasMallPermission === 1}
                                    />
                                </Form.Item>
                            </div>

                            {
                                Boolean(t.deviceBindingList?.length) && <>
                                    <Divider className="!my-0" />
                                    <div className="px-4 py-3">
                                        <div>{intl.formatMessage({ id: 'customer.customerList.openMallAccount.mallSecretKey' })}</div>
                                        {
                                            t.deviceBindingList?.map((item, index) => (
                                                <div key={item.id} className="text-gray-500 my-1">
                                                    <Space className=''>
                                                        <span>{intl.formatMessage({ id: 'customer.customerList.openMallAccount.secretKey' })}{index + 1}: {item.secretKey}</span>
                                                        <span>{intl.formatMessage({ id: 'customer.customerList.openMallAccount.boundDevice' })}: {item.deviceKey}</span>
                                                        <Button type="link" onClick={() => onUpdateMallDeviceBinding({
                                                            contactId: t.id,
                                                            cstId: t.cstId,
                                                            id: item.id,
                                                            operateType: MallDeviceBindingOperateType.DELETE
                                                        })}>{intl.formatMessage({ id: 'customer.customerList.openMallAccount.delete' })}</Button>
                                                    </Space>
                                                </div>
                                            ))
                                        }
                                        <Button type="link" className="px-0" onClick={() => onUpdateMallDeviceBinding({
                                            contactId: t.id,
                                            cstId: t.cstId,
                                            operateType: MallDeviceBindingOperateType.ADD
                                        })}>{intl.formatMessage({ id: 'customer.customerList.openMallAccount.addSecretKey' })}</Button>
                                    </div>
                                </>
                            }
                        </div>
                    ))
                }
            </div>
        </Drawer>
    );
}

export default OpenMallAccountDrawer;