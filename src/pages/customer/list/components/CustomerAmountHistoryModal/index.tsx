import { LogTypeMap } from '@/types/CommonStatus';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Empty, Flex, Modal, Pagination } from 'antd';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { queryAccountChangeLogPage } from '../../services';
import type { CustomerAccountChangeLogType } from '../../types/CustomerAccountChangeLogType';
import type { CustomerAmountHistoryModalType } from '../../types/CustomerAmountHistoryModalType';
export default (props: CustomerAmountHistoryModalType) => {
  const intl = useIntl();
  const [dataList, setDataList] = useState<CustomerAccountChangeLogType[]>([]);
  const [params, setParams] = useState<PageRequestParamsType>({ pageNo: 1, pageSize: 5 });
  const [total, setTotal] = useState<number>();
  useAsyncEffect(async () => {
    if (props.visible) {
      console.log(props.recordId);
      const { data, total: totalCount } = await queryAccountChangeLogPage({
        customerId: props.recordId,
        accountType: 1,
        ...params,
      });
      setTotal(totalCount);
      setDataList(data);
    }
  }, [props.visible, params]);

  const onChange = (pageNo: number, pageSize: number) => {
    setParams({ pageNo, pageSize });
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.title' })}
      width={1080}
      centered
      open={props.visible}
      onCancel={props.onCancel}
      footer={null}
    >
      {!isEmpty(dataList) ? (
        <div>
          {dataList &&
            dataList?.map((t) => (
              <div
                key={t}
                className="p-4 mb-4 border-solid border border-black/[0.08] rounded flex flex-col"
              >
                {/* <span className="font-semibold text-[16px] text-[#000000D9] mb-2">
                  {t.createTime}
                </span> */}
                <ProDescriptions
                  className="text-[#********]!"
                  title={t.createTime}
                  column={4}
                  columns={[
                    {
                      title: intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operatorLabel' }),
                      dataIndex: 'updatePerson',
                    },
                    {
                      title: intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operationTypeLabel' }),
                      dataIndex: 'logType',
                      render: (text) => <span>{LogTypeMap[text]}</span>,
                    },
                    {
                      title: intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operationContentLabel' }),
                      dataIndex: 'changeJson',
                      ellipsis: true,
                      span: 2,
                    },
                  ]}
                  dataSource={t}
                />
                {/* <Space className="text-[#********]" size={16}>
                  <span>{intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operatorLabel' })}{t.updatePerson}</span>
                  <span>{intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operationTypeLabel' })}{LogTypeMap[t.logType]}</span>
                  <span>{intl.formatMessage({ id: 'customer.customerList.amountHistoryModal.operationContentLabel' })}{t.changeJson}</span>
                </Space> */}
              </div>
            ))}
          <Flex justify="flex-end">
            <Pagination onChange={onChange} total={total} pageSize={5} />
          </Flex>
        </div>
      ) : (
        <Empty description={intl.formatMessage({ id: 'noData' })} />
      )}
    </Modal>
  );
};
