import LeftTitle from "@/components/LeftTitle";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProFormDependency, ProFormDigit, ProFormGroup, ProFormSelect, ProFormSwitch, ProFormText } from "@ant-design/pro-components";
import { useIntl } from "@umijs/max";
import { Form, Switch, Tooltip } from "antd";
import { SettleTypeOptions } from "../../config/customerOptions";


const SettlementInfo = () => {
    const intl = useIntl();
    const form = Form.useFormInstance();

    return (
        <ProFormGroup
            title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.settlementInfo' })} />}
            style={{
                backgroundColor: 'white',
                padding: 24,
                marginTop: 16,
                borderRadius: 8,
            }}
        >

            <ProFormSwitch
                name={['settle', 'credit']}
                label={intl.formatMessage({ id: 'customer.customerList.createForm.allowOnAccount' })}
                fieldProps={{
                    onChange: (value) => {
                        if (!value) {
                            form.resetFields([
                                ['settle', 'totalAmount'],
                                ['settle', 'creditTerms'],
                            ]);
                        }
                    },
                }}
            />


            <div className="border border-gray-300 border-solid p-4 mb-4 w-full">
                <ProFormDependency name={['settle']}  >
                    {(props) => {
                        const { settle = {} } = props;
                        const allowOnAccount = settle.credit;
                        const totalAmount = settle.totalAmount || 0;
                        const availableAmount = totalAmount;
                        return (
                            <ProFormGroup>
                                <ProFormText name={['settle', 'id']} hidden />
                                <ProFormDigit
                                    name={['settle', 'totalAmount']}
                                    colProps={{ span: 12 }}
                                    wrapperCol={{ span: 18 }}
                                    label={
                                        <span>
                                            {intl.formatMessage({ id: 'customer.customerList.createForm.onAccountLimit' })}
                                            <Tooltip title={intl.formatMessage({ id: 'customer.customerList.createForm.onAccountLimit' })}>
                                                <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    fieldProps={{
                                        addonBefore: '$',
                                        disabled: !allowOnAccount,
                                    }}
                                    help={
                                        <>
                                            {Boolean(settle.requestCreditLimit) && <div>
                                                {intl.formatMessage({ id: 'customer.customerList.createForm.customerExpectedAmount' }, { amount: (settle.requestCreditLimit ?? 0).toFixed(2) })}
                                            </div>}
                                            <div>
                                                {
                                                    Boolean(settle.usedAmount) && intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.usedAndFrozenCredit' }, {
                                                        usedAmount: settle.usedAmount,
                                                        freezeAmount: settle.freezeAmount,
                                                    })
                                                }
                                            </div>
                                        </>
                                    }
                                />
                                <ProFormSelect
                                    name={['settle', 'creditTermsType']}
                                    colProps={{ span: 12 }}
                                    wrapperCol={{ span: 18 }}
                                    label={
                                        <span>
                                            {intl.formatMessage({ id: 'customer.customerList.createForm.accountPeriod' })}
                                            <Tooltip title={intl.formatMessage({ id: 'customer.customerList.createForm.accountPeriod' })}>
                                                <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    fieldProps={{
                                        disabled: !allowOnAccount,
                                    }}
                                    options={SettleTypeOptions}
                                    help={
                                        settle.requestSettleType && intl.formatMessage({ id: 'customer.customerList.createForm.customerExpectedPeriod' }, { period: settle.requestSettleType ? settle.requestSettleType : 'Weekly' })
                                    }
                                />
                            </ProFormGroup>
                        )
                    }
                    }
                </ProFormDependency>

            </div>


            <ProFormGroup colProps={{ span: 12 }}>
                <Form.Item
                    labelCol={{ span: 18 }}
                    wrapperCol={{ span: 6 }}
                    className='form-item-switch'
                    label={intl.formatMessage({ id: 'customer.customerList.createForm.isMultiCurrency' })}
                    name={["settle", "isMultiCurrency"]}
                >
                    <Switch />
                </Form.Item>
            </ProFormGroup>

            <ProFormGroup colProps={{ span: 12 }}>
                <Form.Item
                    labelCol={{ span: 18 }}
                    wrapperCol={{ span: 6 }}
                    className='form-item-switch'
                    label={intl.formatMessage({ id: 'customer.customerList.createForm.isGstExcluded' })}
                    name={["settle", "gstExcluded"]}
                >
                    <Switch />
                </Form.Item>
            </ProFormGroup>



            {/* <div style={{ marginBottom: 16, fontWeight: 500 }}>Simplify平台账号</div>
            <ProFormList
                name="simplifyAccounts"
                creatorButtonProps={{
                    creatorButtonText: intl.formatMessage({ id: 'customer.customerList.createForm.button.addSimplifyAccount' }),
                }}
                min={1}
            >
                <ProFormGroup>
                    <ProFormSelect
                        name="companyEntity"
                        label={intl.formatMessage({ id: 'customer.customerList.createForm.companyEntity' })}
                        placeholder={intl.formatMessage({ id: 'global.select.placeholder' })}
                    />
                    <ProFormText
                        name="simplifyCustomerId"
                        label={intl.formatMessage({ id: 'customer.customerList.createForm.simplifyCustomerId' })}
                    />
                </ProFormGroup>
            </ProFormList> */}


        </ProFormGroup>
    );
};

export default SettlementInfo;