import LeftTitle from "@/components/LeftTitle";
import { queryDistrictAreaTree } from "@/pages/system/store/services";
import { requiredProps } from "@/types/validateRules";
import { EditableFormInstance, EditableProTable, ProFormGroup } from "@ant-design/pro-components";
import { useIntl, useModel } from "@umijs/max";
import { useDebounceEffect } from "ahooks";
import { Cascader, Flex, Radio } from "antd";
import { find, isEmpty, uniqueId } from "lodash";
import { useCallback, useRef, useState } from "react";
import CustomerAddressTableColumns from "../../config/CustomerAddressTableColumns";
import { CustomerAddressEntity } from "../../types/CustomerAddressEntity";

export const AddressCode: React.FC<{ postCode: string }> = ({ postCode, ...rest }) => {
    const intl = useIntl();
    const [options, setOptions] = useState<any[]>([]);

    const queryDistrictArea = useCallback((postCode: string) => {
        if (!postCode) return;
        queryDistrictAreaTree({ postCode }).then(res => {
            setOptions(res);
        });
    }, []);

    useDebounceEffect(
        () => {
            queryDistrictArea(postCode);
        },
        [postCode],
        {
            wait: 1000,
        },
    );

    return (
        <Cascader
            options={options}
            fieldNames={{ label: 'areaName', value: 'areaId' }}
            {...rest}
            placeholder={intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.provinceCityDistrict' })}
        />
    );
};


/**
 * 地址信息
 */
const Address = () => {
    const intl = useIntl();
    const { set } = useModel('dictModel');
    const editorFormRef = useRef<EditableFormInstance<CustomerAddressEntity>>();
    return (
        <ProFormGroup
            title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.addressInfo' })} />}
            style={{
                backgroundColor: 'white',
                padding: 24,
                marginTop: 16,
                borderRadius: 8,
            }}
        >
            <EditableProTable<CustomerAddressEntity>
                className="mt-4"
                editableFormRef={editorFormRef}
                name="addresses"
                search={false}
                rowKey="id"
                columnEmptyText={false}
                scroll={{ x: 'max-content' }}
                recordCreatorProps={{
                    record: (index) => ({ id: uniqueId('address_'), isDefault: index == 0 ? 1 : 0 }),
                    creatorButtonText: intl.formatMessage({ id: 'customer.customerList.createForm.button.addAddress' }),
                }}
                editable={{
                    type: 'multiple',
                    actionRender: (_, __, defaultDom) => [defaultDom.delete],
                }}
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        fixed: 'left',
                        editable: false,
                        width: 40,
                    },
                    {
                        title: intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.isDefaultAddress' }),
                        align: 'center',
                        width: 100,
                        dataIndex: 'isDefault',
                        fixed: 'left',
                        render: (_, record) => {
                            const rows = editorFormRef.current?.getRowsData?.();
                            return (
                                <Radio
                                    checked={record.isDefault === 1}
                                    onChange={() => {
                                        rows?.forEach((t) => {
                                            const { id } = t;
                                            editorFormRef.current?.setRowData?.(id, {
                                                isDefault: id == record?.id ? 1 : 0,
                                            });
                                        });
                                    }}
                                />
                            );
                        },
                        renderFormItem: (_, { record }) => {
                            const rows = editorFormRef.current?.getRowsData?.();
                            if (rows) {
                                const checkRow = find(rows, (t) => t.isDefault === 1);
                                if (isEmpty(checkRow)) {
                                    editorFormRef.current?.setRowData?.(0, {
                                        isDefault: 1,
                                    });
                                }
                            }
                            return (
                                <Radio
                                    checked={record?.isDefault === 1}
                                    onChange={() => {
                                        rows?.forEach((t) => {
                                            const { id } = t;
                                            editorFormRef.current?.setRowData?.(id, {
                                                isDefault: id == record?.id ? 1 : 0,
                                            });
                                        });
                                    }}
                                />
                            );
                        },
                    },
                    {
                        dataIndex: 'postCode',
                        title: (
                            <Flex align="center">
                                <span className="text-[#FF7621]">*</span>
                                <span>{intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.postCode' })}</span>
                            </Flex>
                        ),
                        width: 200,
                        fieldProps: (form, { rowKey }) => ({
                            onChange: () => {
                                form.setFieldValue([rowKey, 'addressCode'], undefined);
                            },
                        }),
                    },
                    {
                        title: (
                            <Flex align="center">
                                <span className="text-[#FF7621]">*</span>
                                <span>{intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.provinceCityDistrict' })}</span>
                            </Flex>
                        ),
                        width: 200,
                        dataIndex: 'addressCode',
                        renderFormItem: (_, { record }) => {
                            const { postCode = '' } = record;
                            return (
                                <AddressCode postCode={postCode} />
                            )
                        },
                        renderText: (text, record) => {
                            return <>{record?.provinceName} / {record?.prefectureName}</>;
                        },
                        formItemProps: () => {
                            return {
                                rules: [requiredProps],
                            };
                        },
                    },
                    ...CustomerAddressTableColumns(intl),
                    {
                        title: intl.formatMessage({ id: 'common.column.operation' }),
                        valueType: 'option',
                        align: 'center',
                        fixed: 'right',
                        render: (text, record, _, action) => (
                            <a
                                onClick={() => {
                                    action?.startEditable?.(record.id);
                                }}
                            >
                                {intl.formatMessage({ id: 'common.button.edit' })}
                            </a>
                        ),
                    },
                ]}
            />
        </ProFormGroup>
    );
};
export default Address;