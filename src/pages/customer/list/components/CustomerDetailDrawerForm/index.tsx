import iconCstDef from '@/assets/icons/icon_cst_def.png';
import LeftTitle from '@/components/LeftTitle';
import { RightOutlined } from '@ant-design/icons';
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, Col, Empty, Flex, Image, Row, Space, Spin, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { CustomerStatusEnum, DeliveryAmountTypeEnum } from '../../config/customerOptions';
import { getCstDetail } from '../../services';
import type { CustomerAmountHistoryModalType } from '../../types/CustomerAmountHistoryModalType';
import type { CustomerDetailDrawerFormType } from '../../types/CustomerDetailDrawerFormType';
import type { CustomerSaveEntity, CustomerStatus } from '../../types/CustomerSaveEntity';
import CustomerAmountHistoryModal from '../CustomerAmountHistoryModal';

/**
 * 基础信息
 * @param props CommonModelForm
 * @returns
 */
const renderBaseInfo = (data: CustomerSaveEntity, intl: any) => {
  const { base, images, tags } = data;
  const imageUrl = images?.[0]?.url;
  return (
    <ProCard className="py-2 rounded-lg">
      <Row className="text-[#000000D9]" justify="space-between" align="top">
        <Col span={4}>
          <Image
            className="rounded"
            preview={!isEmpty(imageUrl)}
            width={160}
            height={120}
            fallback={iconCstDef}
            src={imageUrl}
          />
        </Col>
        <Col span={20}>
          <Flex vertical={false} align="center">
            <span className="text-xl font-semibold">{base?.cstName}</span>
            {
              <Tag
                color={CustomerStatusEnum[base?.cstStatus as CustomerStatus]?.color}
                className="ml-2"
              >
                {CustomerStatusEnum[base?.cstStatus as CustomerStatus]?.text}
              </Tag>
            }
            {base?.hasMallPermission && (
              <Tag color="orange" className="ml-2">
                {intl.formatMessage({ id: 'customer.customerList.table.column.mallPermission' })}
              </Tag>
            )}
          </Flex>
          <ProDescriptions
            column={3}
            dataSource={base}
            columns={[
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.detailForm.label.customerSn',
                }),
                dataIndex: 'cstSn',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.detailForm.label.nickName',
                }),
                dataIndex: 'nickName',
                ellipsis: true,
              },
              {
                title: intl.formatMessage({ id: 'customer.customerList.table.column.ABN' }),
                dataIndex: 'abn',
              },
              {
                title: intl.formatMessage({ id: 'customer.customerList.detailForm.label.store' }),
                dataIndex: 'storeName',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.detailForm.label.salesman',
                }),
                dataIndex: 'salesmanName',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.detailForm.label.customerTags',
                }),
                dataIndex: 'tags',
                render: () => tags?.map((t) => t.tagName).join(),
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.createForm.label.universalEmail',
                }),
                dataIndex: 'universalEmail',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.createForm.label.financeEmail',
                }),
                dataIndex: 'financeEmail',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.createForm.label.sendFinanceEmailFlag',
                }),
                dataIndex: 'sendFinanceEmailFlag',
                render: (text) =>
                  text ? (
                    <FormattedMessage id="common.option.yes" />
                  ) : (
                    <FormattedMessage id="common.option.no" />
                  ),
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.createForm.label.deliverWay',
                }),
                dataIndex: 'deliveryAmountType',
                valueEnum: DeliveryAmountTypeEnum,
              },
              {
                title: intl.formatMessage({ id: 'customer.customerList.detailForm.label.source' }),
                dataIndex: 'sourceName',
              },
              {
                title: intl.formatMessage({
                  id: 'customer.customerList.detailForm.label.createTime',
                }),
                dataIndex: 'createTime',
              },
              {
                title: intl.formatMessage({ id: 'customer.customerList.detailForm.label.remark' }),
                dataIndex: 'remark',
                span: 3,
              },
            ]}
          />
        </Col>
      </Row>
    </ProCard>
  );
};

/**
 * 联系人信息
 */
const renderContact = (data: CustomerSaveEntity, intl: any) => {
  const { contacts } = data;
  return (
    <ProCard
      title={
        <LeftTitle
          title={intl.formatMessage({ id: 'customer.customerList.detailForm.group.contactInfo' })}
        />
      }
      className="rounded-lg mt-4 cust-ant-empty-normal"
    >
      {!isEmpty(contacts) ? (
        contacts?.map((contact, index) => (
          <div
            key={contact.id}
            className={`p-4 ${
              index === 0 ? 'mt-0' : 'mt-4'
            } border-solid border border-black/[0.08] rounded`}
          >
            <ProDescriptions
              column={4}
              title={
                <>
                  {contact?.firstName ?? ''} {contact?.lastName ?? ''}
                  {contact.isDefault == 1 && (
                    <Tag color="blue" className="ml-2">
                      {intl.formatMessage({
                        id: 'customer.customerList.detailForm.tag.defaultContact',
                      })}
                    </Tag>
                  )}
                </>
              }
              dataSource={contact}
              columns={[
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.contact.label.position',
                  }),
                  dataIndex: 'position',
                  render: (text, record) => {
                    return <>{record?.positionList?.map((item) => item.positionName).join(',')}</>;
                  },
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.contact.label.phone',
                  }),
                  dataIndex: 'phone',
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.contact.label.email',
                  }),
                  dataIndex: 'email',
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.contact.label.remark',
                  }),
                  dataIndex: 'remark',
                  span: 3,
                },
              ]}
            />
          </div>
        ))
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};
/**
 * 地址信息
 */
const renderAddress = (data: CustomerSaveEntity, intl: any) => {
  const { addresses } = data;
  return (
    <ProCard
      title={
        <LeftTitle
          title={intl.formatMessage({ id: 'customer.customerList.detailForm.group.addressInfo' })}
        />
      }
      className="rounded-lg mt-4 cust-ant-empty-normal"
    >
      {!isEmpty(addresses) ? (
        addresses?.map((address, index) => (
          <div
            key={address.id}
            className={`p-4 ${
              index === 0 ? 'mt-0' : 'mt-4'
            } border-solid border border-black/[0.08] rounded`}
          >
            <ProDescriptions
              column={6}
              title={
                <>
                  {intl.formatMessage({
                    id: 'customer.customerList.detailForm.address.label.address',
                  })}{' '}
                  {index + 1}
                  {address.isDefault == 1 && (
                    <Tag color="blue" className="ml-2">
                      {intl.formatMessage({
                        id: 'customer.customerList.detailForm.tag.defaultAddress',
                      })}
                    </Tag>
                  )}
                </>
              }
              dataSource={address}
              columns={[
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.createForm.addressTable.column.postCode',
                  }),
                  dataIndex: 'postCode',
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.createForm.addressTable.column.province',
                  }),
                  dataIndex: 'provinceName',
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.createForm.addressTable.column.prefecture',
                  }),
                  dataIndex: 'prefectureName',
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.address.label.detailAddress',
                  }),
                  dataIndex: 'address',
                  ellipsis: true,
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.address.label.contactName',
                  }),
                  dataIndex: 'name',
                  render: (text, record) => {
                    return (
                      <>
                        {record?.firstName} {record?.lastName}
                      </>
                    );
                  },
                },
                {
                  title: intl.formatMessage({
                    id: 'customer.customerList.detailForm.address.label.phone',
                  }),
                  dataIndex: 'phone',
                },
              ]}
            />
          </div>
        ))
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};

/**
 * 结算信息
 * @param props CommonModelForm
 * @returns
 */
const renderSettlementInfo = (
  data: CustomerSaveEntity,
  showHistoryModal: (recordId: string) => void,
  intl: any,
) => {
  const { settle, base } = data;
  return (
    <ProCard
      extra={
        settle && (
          <Space size={8} className="cursor-pointer" onClick={() => showHistoryModal(base.id)}>
            <span className="text-[#00000099]">
              {intl.formatMessage({
                id: 'customer.customerList.detailForm.link.viewAmountHistory',
              })}
            </span>
            <RightOutlined style={{ color: '#00000099' }} />
          </Space>
        )
      }
      title={
        <LeftTitle
          title={intl.formatMessage({
            id: 'customer.customerList.detailForm.group.settlementInfo',
          })}
        />
      }
      className="py-2 rounded-lg mt-4 cust-ant-empty-normal"
    >
      {settle ? (
        <ProDescriptions
          column={2}
          dataSource={settle}
          columns={[
            {
              title: intl.formatMessage({
                id: 'customer.customerList.createForm.label.creditTerms',
              }),
              dataIndex: 'settleType',
              valueEnum: DeliveryAmountTypeEnum,
            },
            {
              title: intl.formatMessage({
                id: 'customer.customerList.detailForm.settlement.label.creditLimit',
              }),
              dataIndex: 'creditLimit',
              renderText: (text, record) => {
                return (
                  <>
                    {record?.totalAmount}
                    {intl.formatMessage(
                      { id: 'customer.customerList.detailForm.settlement.label.remainingCredit' },
                      {
                        usedAmount: record?.usedAmount ?? 0,
                        freezeAmount: record?.freezeAmount ?? 0,
                        availableAmount: record?.availableAmount ?? 0,
                      },
                    )}
                  </>
                );
              },
            },
            {
              title: intl.formatMessage({ id: 'customer.customerList.createForm.isMultiCurrency' }),
              dataIndex: 'isMultiCurrency',
              renderText: (text: 0 | 1) =>
                text ? (
                  <FormattedMessage id="common.option.yes" />
                ) : (
                  <FormattedMessage id="common.option.no" />
                ),
            },
            {
              title: intl.formatMessage({ id: 'customer.customerList.createForm.isGstExcluded' }),
              dataIndex: 'isGstExcluded',
              renderText: (text: 0 | 1) =>
                text ? (
                  <FormattedMessage id="common.option.yes" />
                ) : (
                  <FormattedMessage id="common.option.no" />
                ),
            },
          ]}
        />
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};

export default (props: CustomerDetailDrawerFormType) => {
  const intl = useIntl();
  const [form] = useForm();
  const [data, setData] = useState<CustomerSaveEntity>();
  // 导入客户
  const [historyModalProps, setHistoryModalProps] = useState<CustomerAmountHistoryModalType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setHistoryModalProps((pre) => ({ ...pre, visible: false, recordId: '' }));
    },
  });
  useAsyncEffect(async () => {
    if (isEmpty(props.recordId)) {
      form.resetFields();
    } else {
      const detail = await getCstDetail({ cstId: props.recordId });
      setData(detail);
    }
  }, [props.visible]);

  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const showHistoryModal = (recordId: string) => {
    setHistoryModalProps((pre) => ({ ...pre, visible: true, recordId }));
  };

  return (
    <DrawerForm
      form={form}
      grid
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width={1300}
      drawerProps={{
        maskClosable: true,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose: props.onCancel,
      }}
      submitter={submitter}
    >
      {data ? (
        <>
          {/* 基础信息 */}
          {renderBaseInfo(data, intl)}
          {/* 联系人信息 */}
          {renderContact(data, intl)}
          {/* 地址信息信息 */}
          {renderAddress(data, intl)}
          {/* 结算信息 */}
          {renderSettlementInfo(data, showHistoryModal, intl)}
        </>
      ) : (
        <Spin />
      )}
      <CustomerAmountHistoryModal {...historyModalProps} />
    </DrawerForm>
  );
};
