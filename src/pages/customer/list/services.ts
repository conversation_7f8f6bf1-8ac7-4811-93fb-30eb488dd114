import type { GetCstListRequest } from '@/pages/customer/list/types/get.cst.list.request';
import type { GetCstPagedRequest } from '@/pages/customer/list/types/get.cst.paged.request';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { CustomerAccountChangeLogType } from './types/CustomerAccountChangeLogType';
import { type CustomerEntity } from './types/CustomerEntity';
import { CustomerMallPermissionContact, UpdateMallDeviceBindingRequest, UpdateMallPermissionRequest } from './types/CustomerMallPermission';
import type { CustomerSaveEntity } from './types/CustomerSaveEntity';
import type { CustomerTagEntity } from './types/CustomerTagEntity';

/**
 * 用户管理-分页查询
 *
 * @param params
 * @returns
 */
export const queryCustomerPage = async (params: PageRequestParamsType & GetCstPagedRequest) => {
  return request<PageResponseDataType<CustomerEntity>>(`/ipmscst/CstManageFacade/getCstPaged`, {
    data: params,
  });
};

/**
 * 客户模糊查询
 */
export const getCstList = (params: GetCstListRequest): Promise<CustomerEntity[]> => {
  return request(`/ipmscst/CstManageFacade/getCstList`, {
    data: params,
  });
};

/**
 * 用户管理-保存客户信息
 *
 * @param params
 * @returns
 */
export const saveCst = async (params: CustomerSaveEntity) => {
  return request<boolean>(`/ipmscst/CstManageFacade/saveCst`, {
    data: params,
  });
};
/**
 * 用户管理-获取客户详情
 *
 * @param params
 * @returns
 */
export const getCstDetail = async (params: { cstId: string }) => {
  return request<CustomerSaveEntity>(`/ipmscst/CstManageFacade/getCstDetail`, {
    data: params,
  });
};
/**
 * 用户管理-根据条件查询全量标签
 *
 * @param params
 * @returns
 */
export const getTagList = async (params: { tagStatus: number; tagType: number }): Promise<CustomerTagEntity[]> => {
  return request(`/ipmscst/CstTagManageFacade/getTagList`, {
    data: params,
  });
};

/**
 * 用户管理-启用、禁用客户
 *
 * @param params
 * @returns
 */
export const changeStatus = async (params: { cstId: string; status: number }) => {
  return request<boolean>(`/ipmscst/CstManageFacade/changeStatus`, {
    data: params,
  });
};
/**
 * 用户管理-挂账账户操作记录查询
 * @param params
 * @returns
 */
export const queryAccountChangeLogPage = async (
  params: PageRequestParamsType & {
    customerId: string;
    accountType: number;
  },
) => {
  return request<PageResponseDataType<CustomerAccountChangeLogType>>(
    `/ipmsaccount/queryAccountChangeLogPage`,
    {
      data: params,
    },
  );
};

/**
 * 查询联系人列表
 */
export const queryCstContactList = async (params: { cstId: string }): Promise<CustomerMallPermissionContact[]> => {
  return request(
    `/ipmscst/cstContactFacade/queryCstContactListByCondition`,
    {
      data: params
    },
  );
};

/**
 * 更新联系人的商城权限
 */
export const updateMallPermission = async (params: UpdateMallPermissionRequest): Promise<boolean> => {
  return request(
    `/ipmscst/cstManageFacade/updateMallPermission`,
    {
      data: params
    },
  );
};


/**
 * 密钥管理
 */
export const updateMallDeviceBinding = async (params: UpdateMallDeviceBindingRequest): Promise<boolean> => {
  return request(
    `/ipmscst/cstManageFacade/updateMallDeviceBinding`,
    {
      data: params
    },
  );
};