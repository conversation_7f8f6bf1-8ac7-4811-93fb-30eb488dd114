import type { ProColumns } from '@ant-design/pro-components';
import { type CustomerContactEntity, PositionEnum } from '../types/CustomerContactEntity';

export default (intl: any): ProColumns<CustomerContactEntity>[] => [
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.phone' }),
    dataIndex: 'phone',
    search: true,
    //valueType: 'digit',
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.email' }),
    dataIndex: 'email',
    width: 200,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.position' }),
    dataIndex: 'positions',
    valueEnum: PositionEnum,
    search: false,
    width: 200,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    renderText: (value: string[], record) => record?.positionList?.map((item) => item.positionName).join(','),
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.remark' }),
    dataIndex: 'remark',
    width: 200,
    search: false,
    ellipsis: true,
  },
];
