import { FormattedMessage } from '@umijs/max';
import { CustomerStatus, DeliveryAmountType, SettleType } from "../types/CustomerSaveEntity";

export const CustomerStatusEnum = {
    [CustomerStatus.ENABLE]: { text: <FormattedMessage id="common.option.enable" />, status: 'Success', color: 'green' },
    [CustomerStatus.DISABLE]: { text: <FormattedMessage id="common.option.disable" />, status: 'Error', color: 'red' },
    [CustomerStatus.PENDING]: { text: <FormattedMessage id="common.option.peddingAudit" />, status: 'Warning', color: 'orange' },
};

export const CustomerStatusOptions = [
    { label: <FormattedMessage id="common.option.enable" />, value: CustomerStatus.ENABLE },
    { label: <FormattedMessage id="common.option.disable" />, value: CustomerStatus.DISABLE },
    { label: <FormattedMessage id="common.option.peddingAudit" />, value: CustomerStatus.PENDING },
]

export const DeliveryAmountTypeEnum = {
    [DeliveryAmountType.Free]: { text: <FormattedMessage id="customer.customerList.deliveryAmountType.free" /> },
    [DeliveryAmountType.Fixed]: { text: <FormattedMessage id="customer.customerList.deliveryAmountType.fixed" /> },
    [DeliveryAmountType.Bargaining]: { text: <FormattedMessage id="customer.customerList.deliveryAmountType.bargaining" /> },
};

export const DeliveryAmountTypeOptions = [
    { label: <FormattedMessage id="customer.customerList.deliveryAmountType.free" />, value: DeliveryAmountType.Free },
    { label: <FormattedMessage id="customer.customerList.deliveryAmountType.fixed" />, value: DeliveryAmountType.Fixed },
    { label: <FormattedMessage id="customer.customerList.deliveryAmountType.bargaining" />, value: DeliveryAmountType.Bargaining },
]

export const SettleTypeOptions = [
    { label: 'COD', value: SettleType.COD },
    { label: 'Weekly', value: SettleType.WEEKLY },
    { label: 'Monthly', value: SettleType.MONTHLY },
]