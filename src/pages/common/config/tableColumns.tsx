import { allSimpleQuery } from '@/pages/personnel/user/services';
import { FormattedMessage } from '@umijs/max';
import { querySupplierList } from '../../purchase/supplier/services';

/**
 * 查询条件 开始结束时间
 */

export const TimeAt = (title: string) => ({
  title: title,
  dataIndex: 'timeAt',
  valueType: 'dateRange',
  hideInTable: true,
  search: {
    transform: (value: any) => {
      return {
        startTime: value[0],
        endTime: value[1],
      };
    },
  },
});

/**
 * 供应商
 */
export const SupplierIdColumn = {
  title: <FormattedMessage id="purchase.list.columns.supplierName" />,
  dataIndex: 'supplierId',
  key: 'supplierId',
  search: true,
  valueType: 'select',
  hideInTable: true,
  request: async () => {
    const data = await querySupplierList({});
    return data?.map(({ supplierId, supplierName }) => ({
      key: supplierId,
      value: supplierId,
      label: supplierName,
    }));
  },
};
/**
 * 门店
 */
export const StoreIdColumn = (title: string) => ({
  title: title,
  dataIndex: 'storeId',
  key: 'storeId',
  search: true,
  valueType: 'select',
  hideInTable: true,
  request: async () => {
    const data = await allSimpleQuery({});
    return data?.map(({ id, name }) => ({
      key: id,
      value: id,
      label: name,
    }));
  },
});
