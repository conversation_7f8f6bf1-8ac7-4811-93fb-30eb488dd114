/**
 * 采购退货单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
// @ts-ignore
import nzhcn from 'nzh/cn';
import { queryReturnOrderFacadeById } from '@/pages/purchase/returns/services';
import { ReutrnPostEntity } from '@/pages/purchase/returns/detail/types/return.post.entity';

export interface PurchaseReturnPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.purchaseReturnOrder];
}

const PurchaseReturn = (props: PurchaseReturnPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<ReutrnPostEntity>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const returnId = searchParams.get('returnId');

  useEffect(() => {
    if (returnId) {
      setLoading(true);
      queryReturnOrderFacadeById({ id: returnId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [returnId]);

  useEffect(() => {
    if (orderDetail) {
      QRCode.toDataURL(orderDetail?.returnOrder?.orderNo!, { margin: 1 }).then((result) => {
        setQrCodeUrl(result);
      });
    }
  }, [orderDetail]);

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail?.returnOrder?.storeName}采购退货单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td>供应商: {orderDetail?.returnOrder?.supplierName}</td>
            <td>单&emsp;&emsp;号: {orderDetail?.returnOrder?.orderNo}</td>
            <td>制单人: {orderDetail?.returnOrder?.creator}</td>
          </tr>
          <tr>
            <td>制单日期: {orderDetail?.returnOrder?.createTime}</td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
            <td>发货仓库: {orderDetail?.returnOrder?.outWarehouseName}</td>
          </tr>
          {orderDetail?.returnOrder?.remark && (
            <tr>
              <td colSpan={3}>备注: {orderDetail?.returnOrder?.remark}</td>
            </tr>
          )}
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td>OE</td>
              <td>供应商编码</td>
              <td>品牌</td>
              <td>数量</td>
              <td>单位</td>
              <td>单价(元)</td>
              <td>合计(元)</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {orderDetail.returnLineList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.skuName}</td>
                <td>{item.oe}</td>
                <td>{item.brandPartNo}</td>
                <td>{item.brandName}</td>
                <td>{item.num}</td>
                <td>{item.unit}</td>
                <td>{item.price?.toFixed(2)}</td>
                <td>{item.sumPrice?.toFixed(2)}</td>
                <td>{item.locationCode}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top has-td-border">
          <tr>
            <td className="no-border-top">结算方式: {orderDetail?.returnOrder?.payTypeDesc}</td>
            <td className="no-border-top">退货数量: {orderDetail?.returnOrder?.num}</td>
            <td className="no-border-top">
              退款总额(元):{' '}
              {nzhcn.toMoney(orderDetail?.returnOrder?.sumAmount, { outSymbol: false })}{' '}
              {orderDetail?.returnOrder?.sumAmount}
            </td>
          </tr>
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default PurchaseReturn;
