/**
 * 销售退货单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import { getAfterSaleDetail } from '@/pages/sales/returns/operation/services';
import { AfterSaleOrderRo } from '@/pages/sales/returns/operation/types/ReturnsAfterSaleDetailEntity';
import { sum } from 'lodash';
import { getCstDetail } from '@/pages/customer/list/services';
import type { CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
// @ts-ignore
import nzhcn from 'nzh/cn';

export interface SalesReturnPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.salesReturnOrder];
}

const SalesReturn = (props: SalesReturnPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<AfterSaleOrderRo>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();
  const [cstDetail, setCstDetail] = useState<CustomerSaveEntity>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const orderNo = searchParams.get('orderNo');
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (orderNo && orderId) {
      setLoading(true);
      getAfterSaleDetail({ orderNo, orderId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
            getCstDetail({ cstId: result.main.cstId! }).then((result) => {
              if (result) {
                setCstDetail(result);
              }
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
      QRCode.toDataURL(orderNo, { margin: 1 }).then((result) => {
        setQrCodeUrl(result);
      });
    }
  }, [orderNo]);

  if (!orderDetail) return null;

  const defaultContact = cstDetail?.contacts?.find((item) => item.isDefault);
  const defaultAddress = cstDetail?.addresses?.find((item) => item.isDefault);

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail.main?.storeName}销售退货单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td colSpan={2}>购方单位: {orderDetail.main?.cstName}</td>
            <td style={{ width: '40%' }}>单&emsp;&emsp;号: {orderDetail.main?.orderNo}</td>
          </tr>
          <tr>
            <td colSpan={2}>
              地&emsp;&emsp;址:{' '}
              {`${defaultAddress?.provinceName ?? ''}${defaultAddress?.cityName ?? ''}${
                defaultAddress?.prefectureName ?? ''
              }${defaultAddress?.address ?? ''}`}
            </td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
          </tr>
          <tr>
            <td>联&ensp;系&ensp;人: {defaultContact?.name}</td>
            <td>联系方式: {defaultContact?.phone}</td>
            <td>制单日期: {orderDetail?.main?.orderCreateTime}</td>
          </tr>
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td>OE</td>
              <td>供应商编码</td>
              <td>品牌</td>
              <td>数量</td>
              <td>单位</td>
              <td>单价(元)</td>
              <td>合计(元)</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {orderDetail.goods?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.itemName}</td>
                <td>{item.oeNos?.join(',')}</td>
                <td>{item.brandPartNos?.join(',')}</td>
                <td>{item.brandName}</td>
                <td>{item.refundNum}</td>
                <td>{item.unitName}</td>
                <td>{item.costAmount?.toFixed(2)}</td>
                <td>{item.itemAmount?.toFixed(2)}</td>
                <td>{item.locationCode}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top has-td-border">
          <tr>
            <td className="no-border-top" style={{ width: '20%' }}>
              结算方式: {orderDetail.refunds?.[0]?.refundTypeName}
            </td>
            <td className="no-border-top" style={{ width: '20%' }}>
              退货数量: {sum(orderDetail?.goods.map((t) => t.refundNum))}
            </td>
            <td className="no-border-top">
              退款总额(元): {nzhcn.toMoney(orderDetail?.main?.orderAmount, { outSymbol: false })}{' '}
              {orderDetail?.main?.orderAmount?.toFixed(2)}
            </td>
          </tr>
        </table>
        <table className="no-border-top">
          <tr>
            <td style={{ width: '20%' }}>制&ensp;单&ensp;人: {orderDetail?.main?.salesmanName}</td>
            <td style={{ width: '20%' }}>收货仓库: {orderDetail.main?.backWarehouseName}</td>
            <td style={{ width: '20%' }}>
              审核:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
            <td style={{ width: '20%' }}>
              财务:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
            <td>
              客户签字:
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
          </tr>
          {orderDetail?.main?.remark && (
            <tr>
              <td colSpan={5}>备注: {orderDetail?.main?.remark}</td>
            </tr>
          )}
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default SalesReturn;
