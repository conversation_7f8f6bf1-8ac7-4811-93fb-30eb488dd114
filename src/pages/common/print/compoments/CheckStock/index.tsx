/**
 * 盘点单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import { queryCheckByIdOrNo, queryCheckPostDetail } from '@/pages/stocks/check/services';
import { CheckPostEntity } from '@/pages/stocks/check/list/types/check.post.entity';
import {
  StockCheckModeEnum,
  StockCheckModeOptions,
} from '@/pages/stocks/check/list/types/StockCheckModeEnum';
import { CheckDetailPostEntity } from '@/pages/stocks/check/detail/types/check.detail.post.entity';

export interface OutStockPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.checkOrder];
}

const CheckStock = (props: OutStockPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<CheckPostEntity>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();
  const [goodsList, setGoodsList] = useState<CheckDetailPostEntity[]>([]);

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const checkId = searchParams.get('checkId');

  useEffect(() => {
    if (checkId) {
      setLoading(true);
      queryCheckByIdOrNo({ id: checkId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
            QRCode.toDataURL(result?.bizBillNo!, { margin: 1 }).then((result) => {
              setQrCodeUrl(result);
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
      queryCheckPostDetail({ checkId, pageNo: 1, pageSize: 9999 }).then((result) => {
        if (result.data) {
          setGoodsList(result.data);
        }
      });
    }
  }, [checkId]);

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail.warehouseName}盘点单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td>盘点单号: {orderDetail.bizBillNo}</td>
            <td>盘点仓库: {orderDetail.warehouseName}</td>
            <td>盘点方式: {StockCheckModeOptions[orderDetail.mode!].text}</td>
          </tr>
          <tr>
            <td>盘点状态: {orderDetail.stateDesc}</td>
            <td>制单日期: {orderDetail.createTime}</td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
          </tr>
          {orderDetail?.remarks && (
            <tr>
              <td colSpan={3}>备&emsp;&emsp;注: {orderDetail?.remarks}</td>
            </tr>
          )}
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td width={150}>OE</td>
              <td>供应商编码</td>
              <td>品牌</td>
              <td>分类</td>
              <td>单位</td>
              <td>库存数量</td>
              <td>盘点数量</td>
              <td>盘点差异</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {goodsList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.itemName}</td>
                <td>{item.oeNo}</td>
                <td>{item.brandPartNo}</td>
                <td>{item.brandName}</td>
                <td>{item.categoryName}</td>
                <td>{item.unitName}</td>
                <td>{orderDetail.mode === StockCheckModeEnum.MING_PAN ? item.stockAmount : '/'}</td>
                <td>{item.checkAmount}</td>
                <td>{item.diffAmount}</td>
                <td>{item.code}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top">
          <tr>
            <td>制单人: {orderDetail.createPerson}</td>
            <td>
              盘点人:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
          </tr>
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default CheckStock;
