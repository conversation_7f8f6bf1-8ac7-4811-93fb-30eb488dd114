## 原则

- 使用中文回答；
- 所有页面支持国际化

## 项目技术栈

React, tailwind.css, pro-components(<https://pro-components.antdigital.dev/components/form>)

## 国际化

国际化使用：

```js
const intl = useIntl();
const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

t('common.button.add');
```

国际化配置：

- `pages/customer` => `src/locales/zh-CN/customer.ts` 、 `src/locales/en-US/customer.ts`
- `pages/finance` => `src/locales/zh-CN/finance.ts` 、 `src/locales/en-US/finance.ts`
- `pages/goods` => `src/locales/zh-CN/goods.ts` 、 `src/locales/en-US/goods.ts`
- `pages/home` => `src/locales/zh-CN/home.ts` 、 `src/locales/en-US/home.ts`
- `pages/purchase` => `src/locales/zh-CN/purchase.ts` 、 `src/locales/en-US/purchase.ts`
- `pages/report` => `src/locales/zh-CN/report.ts` 、 `src/locales/en-US/report.ts`
- `pages/sales` => `src/locales/zh-CN/sales.ts` 、 `src/locales/en-US/sales.ts`
- `pages/system` => `src/locales/zh-CN/system.ts` 、 `src/locales/en-US/system.ts`
- `pages/order` => `src/locales/zh-CN/order.ts` 、 `src/locales/en-US/order.ts`
- `pages/stocks` => `src/locales/zh-CN/stocks.ts` 、 `src/locales/en-US/stocks.ts`
- `pages/personnel` => `src/locales/zh-CN/personnel.ts` 、 `src/locales/en-US/personnel.ts`

## 所有页面

1. 使用`PageContainer`组件包裹页面
2. 使用`withKeepAlive`导出页面

举例：

```tsx
import { PageContainer } from '@ant-design/pro-components';
import withKeepAlive from '@/wrappers/withKeepAlive';

const Page = () => {
  return <PageContainer>Page</PageContainer>;
};
export default withKeepAlive(Page);
```

## 分页列表页面

1. 使用组件 FunProTable

```tsx
import FunProTable from '@/components/common/FunProTable';

<FunProTable<Entity, any>
  rowKey="id"
  requestPage={queryPage}
  scroll={{ x: 'max-content' }}
  actionRef={actionRef}
  columns={columns}
/>;
```

2. 列表设置

如果是**门店**字段：

```tsx
  {
    title: '门店',
    dataIndex: 'storeName',
    width: 120,
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    formItemProps: {
      name: 'storeIdList',
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },
```

如果是**对象**字段：

```tsx
  {
    title: '客户',
    dataIndex: 'buyerId',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      showSearch: true,
    },
    request: async (query) => {
      const data = await getCstList({ keyword: query.keyWords });
      return data?.map(({ cstId, cstName }) => ({
        value: cstId,
        label: cstName,
      }));
    },
  },
```

如果是**配送员**,**员工**等字段：

```tsx
    {
      title: '配送员',
      search: {
        transform: (value: any) => {
          return {
            salesmanIdList: [value],
          };
        },
      },
      width: 60,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
```
