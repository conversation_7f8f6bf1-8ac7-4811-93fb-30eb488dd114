# 部署说明

本项目支持通过 Azure 容器服务和 Kubernetes 进行自动化部署。请按照以下步骤完成部署：

## 1. 登录 Azure

首先需要登录 Azure 账号：

```bash
az login
```

## 2. 登录 Azure 容器镜像仓库

使用以下命令登录到 Azure 容器镜像仓库（ACR）：

```bash
az acr login --name gripx
```

## 3. 构建 Docker 镜像

在本地构建适用于 linux/amd64 平台的 Docker 镜像：

```bash
docker build --platform=linux/amd64 -t gripxstoreweb:latest .
```

## 4. 标记并推送镜像到 ACR

将本地镜像打标签，并推送到 Azure 容器镜像仓库：

```bash
docker tag gripxstoreweb:latest gripx.azurecr.io/gripxstoreweb:latest
docker push gripx.azurecr.io/gripxstoreweb:latest
```

## 5. 应用 Kubernetes 部署配置

使用 kubectl 应用部署配置文件：

```bash
kubectl apply -f azureDeployment.yaml
```

## 6. 重启 Kubernetes 部署

部署配置更新后，重启对应的 deployment 以加载最新镜像：

```bash
kubectl rollout restart deployment gripxstoreweb -n dev
```

---

## 一键执行

你也可以直接运行脚本自动完成上述所有步骤：

```bash
sh AzureBuild.sh
```

---

## 注意事项

- 请确保已安装并正确配置 `az`、`docker` 和 `kubectl` 命令行工具。
- 请根据实际情况修改 `azureDeployment.yaml` 配置文件。
- 如需在其他命名空间或环境部署，请相应调整命令中的参数。

---

如有问题请联系项目维护者。
