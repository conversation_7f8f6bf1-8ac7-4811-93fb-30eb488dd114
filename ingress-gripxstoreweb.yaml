apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gripxstoreweb-ingress
  namespace: dev
  # annotations:
  #   nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          # 后端API代理
          - pathType: Prefix
            path: /apigateway
            backend:
              service:
                name: ipmsmonolithic-service
                port:
                  number: 80
          # 前端静态资源
          - pathType: Prefix
            path: /
            backend:
              service:
                name: gripxstoreweb-service
                port:
                  number: 80
